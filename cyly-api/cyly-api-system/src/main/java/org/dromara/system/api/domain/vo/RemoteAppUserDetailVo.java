package org.dromara.system.api.domain.vo;

import lombok.Data;
import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 员工信息
 */
@Data
public class RemoteAppUserDetailVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long detailId;

    /**
     * app用户名
     */
    private String appUserName;

    /**
     * 姓名
     */
    private String name;

    /**
     * 岗位
     */
    private Long postId;

    /**
     * 年龄
     */
    private Long age;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 籍贯
     */
    private String origin;

    /**
     * 生日
     */
    private Date birthday;

    /**
     * 0未婚1已婚
     */
    private Long marriage;

    /**
     * 民族
     */
    private String ethnic;

    /**
     * 院校
     */
    private String colleges;

    /**
     * 专业
     */
    private String specialized;

    /**
     * 学历
     */
    private String degree;

    /**
     * 技能
     */
    private String skill;

    /**
     * 工作经历
     */
    private String workExp;

    /**
     * 紧急联系人
     */
    private String emergencyContacts;

    /**
     * 联系人电话
     */
    private String contactsPhone;

    /**
     * 身份类型
     */
    private String identityType;

    /**
     * 帐号状态（0正常 1停用）
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

}
