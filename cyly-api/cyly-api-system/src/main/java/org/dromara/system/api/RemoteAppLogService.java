package org.dromara.system.api;


import org.dromara.system.api.domain.bo.RemoteAppLogininforBo;
import org.dromara.system.api.domain.bo.RemoteAppOperLogBo;

/**
 * 日志服务
 *
 * <AUTHOR> Li
 */
public interface RemoteAppLogService {

  /**
   * 保存系统日志
   *
   * @param appOperLog 日志实体
   */
  void saveLog(RemoteAppOperLogBo appOperLog);

  /**
   * 保存访问记录
   *
   * @param appLogininfor 访问实体
   */
  void saveLogininfor(RemoteAppLogininforBo appLogininfor);
}
