package org.dromara.system.api;

import org.springframework.stereotype.Repository;

/**
 * 配置服务
 *
 * <AUTHOR>
 */
@Repository
public interface RemoteConfigService {

    /**
     * 获取注册开关
     * @param tenantId 租户id
     * @return true开启，false关闭
     */
    boolean selectRegisterEnabled(String tenantId);


    /**
     * 获取注册开关
     *
     * @param tenantId 租户id
     * @return true开启，false关闭
     */
    boolean selectAppRegisterEnabled(String tenantId);

}
