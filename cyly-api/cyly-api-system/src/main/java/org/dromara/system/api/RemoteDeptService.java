package org.dromara.system.api;

import org.dromara.system.api.domain.vo.RemoteDeptVo;

import java.util.List;

/**
 * 部门服务
 *
 * <AUTHOR> Li
 */
public interface RemoteDeptService {

    /**
     * 通过部门ID查询部门名称
     *
     * @param deptIds 部门ID串逗号分隔
     * @return 部门名称串逗号分隔
     */
    String selectDeptNameByIds(String deptIds);

    void checkDeptDataScope(Long deptId);

    List<RemoteDeptVo> selectBatchIds(List<Long> deptIds);

    RemoteDeptVo selectById(String deptId);
}
