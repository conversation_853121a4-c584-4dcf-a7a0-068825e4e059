package org.dromara.system.api.model;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * xjf
 * 2025年2月2日17:32:34
 * App登录用户信息模型类
 * 用于在系统中表示一个登录用户，包含用户的基本信息、登录信息、权限信息等
 */
@Data
@NoArgsConstructor
public class AppLoginUser implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 系统标识
     */
    private String systemType;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 数据权限 当前角色ID
     */
    private String roleId;

    /**
     * 用户唯一标识
     */
    private String token;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 登录时间
     */
    private Long loginTime;

    /**
     * 过期时间
     */
    private Long expireTime;

    /**
     * 登录IP地址
     */
    private String ipaddr;

    /**
     * 登录地点
     */
    private String loginLocation;

    /**
     * 浏览器类型
     */
    private String browser;

    /**
     * 操作系统
     */
    private String os;

    /**
     * 菜单权限
     */
    private Set<String> menuPermission;

    /**
     * 角色权限
     */
    private Set<String> rolePermission;


    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 密码
     */
    private String password;

    /**
     * 盐
     */
    private String salt;

    /**
     * 角色对象
     */
    private List<RoleDTO> roles;

    /**
     * 人脸识别地址
     */
    private String faceUrl;


    /**
     * 客户端
     */
    private String clientKey;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 电话号码
     */
    private String phoneNumber;

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 祖级列表
     */
    private String ancestors;
    /**
     * XChat身份
     */
    private String chatIdentity;


    /**
     * 获取登录ID
     *
     * @return 登录ID，由用户类型和用户ID组成
     * @throws IllegalArgumentException 如果用户类型或用户ID为空，则抛出此异常
     */
    public String getLoginId() {
        if (userType == null) {
            throw new IllegalArgumentException("用户类型不能为空");
        }
        if (userId == null) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        return userType + ":" + userId;
    }

}
