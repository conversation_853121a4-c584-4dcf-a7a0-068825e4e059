package org.dromara.system.api;
/**
 * 行政区域代码dubbo服务
 * <AUTHOR>
 * @date 2025年3月5日 14:40:29
 */
public interface RemoteDictAreaService {
    /**
     * 地区编码获取地区名称 例如 琼海市
     * @param areaCode 地区编码
     * @return 地区名称
     */
    String getAreaName(String areaCode);

    /**
     * 获取行政区划级别 1-省、2-市、3-区县
     * @param areaCode 地区编码
     */
    Integer getAreaLevel(String areaCode);

    /**
     * 地区名称 例如 琼海市获取地区编码
     * @param areaName 地区名称
     * @return 地区编码
     */
    String getAreaId(String areaName);

    /**
     * 地区编码获取区域名称 例如 海南省/琼海市/嘉积镇
     * @param areaCode 地区编码
     */
    String getAreaFullName(String areaCode);
}
