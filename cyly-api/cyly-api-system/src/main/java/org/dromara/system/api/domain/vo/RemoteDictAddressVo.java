package org.dromara.system.api.domain.vo;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 行政区划表
 */

@Data
public class RemoteDictAddressVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 地区id
     */
    private Long id;

    /**
     * 父级ID（指向上一级行政区划）
     */
    private Long parentId;

    /**
     * 行政区划编码
     */
    private String code;

    /**
     * 行政区划名称
     */
    private String name;

    /**
     * 层级（1-省份，2-城市，3-区县，4-街道，5-村庄/社区）
     */
    private Long level;

    /**
     * 完整名称
     */
    private String fullName;

    /**
     * 状态(0停用 1正常)
     */
    private String status;
}
