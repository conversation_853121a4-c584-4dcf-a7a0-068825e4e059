package org.dromara.system.api;

import org.dromara.common.core.exception.user.UserException;
import org.dromara.system.api.domain.bo.RemoteAppUserBo;
import org.dromara.system.api.domain.vo.RemoteAppUserVo;
import org.dromara.system.api.model.AppLoginUser;
import org.dromara.system.api.model.XcxLoginUser;

import java.util.List;

/**
 * 描述:
 *
 * <AUTHOR>
 * @since 2025-01-16 15:08
 */
public interface RemoteAppUserService {
    /**
     * 通过用户名查询用户信息
     *
     * @param username 用户名
     * @param tenantId 租户id
     * @return 结果
     */
    AppLoginUser getAppUserInfo(String username, String tenantId, String systemType) throws UserException;


    /**
     * 通过用户id、机构id及系统标识查询用户信息
     *
     * @param orgId 机构ID
     * @param systemType 系统类型标识
     * @return 用户登录信息列表
     * @throws UserException 用户相关异常
     */
    List<AppLoginUser> getAppUserInfoByOrgIdAndSystemType(Long orgId, String systemType) throws UserException;

    /**
     * 通过用户id查询用户信息
     *
     * @param userId   用户id
     * @param tenantId 租户id
     * @return 结果
     */
    AppLoginUser getAppUserInfo(Long userId, String tenantId, String systemType) throws UserException;



    /**
     * 通过手机号查询用户信息
     *
     * @param phoneNumber 手机号
     * @param tenantId    租户id
     * @return 结果
     */
    AppLoginUser getAppUserInfoByPhoneNumber(String phoneNumber, String tenantId,String systemType) throws UserException;

    AppLoginUser ImUserInfoByPhoneNumber(String phoneNumber, String tenantId, String systemType) throws UserException;

    /**
     * 通过邮箱查询用户信息
     *
     * @param email    邮箱
     * @param tenantId 租户id
     * @return 结果
     */
    AppLoginUser getAppUserInfoByEmail(String email, String tenantId) throws UserException;

    /**
     * 通过openid查询用户信息
     *
     * @param openid openid
     * @return 结果
     */
    XcxLoginUser getAppUserInfoByOpenid(String openid) throws UserException;

    RemoteAppUserBo insertAppUser(RemoteAppUserBo remoteAppUserBo);

    /**
     * 注册用户信息
     *
     * @param remoteUserBo 用户信息
     * @return 注册是否成功
     */
    Boolean registerUserInfo(RemoteAppUserBo remoteUserBo);

    /**
     * 通过userId查询用户账户
     *
     * @param userId 用户id
     * @return 结果
     */
    String selectUserNameById(Long userId);

    /**
     * 通过用户ID查询用户昵称
     *
     * @param userId 用户id
     * @return 结果
     */
    String selectNickNameById(Long userId);

    String selectChatIdentityById(Long userId);

    String selectPhoneNumberById(Long userId);

    /**
     * 修改用户信息
     * @param remoteAppUserBo 用户信息
     * @return 结果
     */
    int updateUserInfo(RemoteAppUserBo remoteAppUserBo);

    Boolean updateAppUserIdentity(Long userId, String chatIdentity);

    /**
     * 更新用户信息
     *
     * @param userId 用户ID
     * @param ip     IP地址
     */
    void recordLoginInfo(Long userId, String ip);

    List<RemoteAppUserVo> selectListByTaskId(Long taskId);
    /**
     * 通过用户ID查询用户列表
     *
     * @param userIds 用户ids
     * @return 用户列表
     */
    List<RemoteAppUserVo> selectListByIds(List<Long> userIds);

    /**
     * 通过用户ID查询用户列表
     *
     * @param userIds 用户ids
     * @return 用户列表
     */
    List<RemoteAppUserVo> selectAppUserByIds(List<String> userIds);

    /**
     * 根据appUserId查询用户信息
     * @param appUserId 用户Id
     * @return 用户信息
     */
    RemoteAppUserVo selectVoByAppUserId(Long appUserId);

    Boolean updateTaskId(RemoteAppUserVo v);

    /**
     * 评估员列表
     */
    List<RemoteAppUserVo> selectList(String systemType);
}
