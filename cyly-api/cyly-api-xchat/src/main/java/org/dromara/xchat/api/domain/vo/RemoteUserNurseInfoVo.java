package org.dromara.xchat.api.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 用户与护理员与长者信息关联远程视图对象
 *
 * <AUTHOR>
 * @date 2024-12-27
 */
@Data
@NoArgsConstructor
public class RemoteUserNurseInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户身份绑定id
     */
    private Long id;

    /**
     * 云信用户id
     */
    private String userId;

    /**
     * 长者id
     */
    private Long elderId;

    /**
     * 亲属id
     */
    private Long familyId;

    /**
     * 护理员id
     */
    private Long nurseId;

    /**
     * 绑定人姓名
     */
    private String userName;

    /**
     * 绑定人身份证号
     */
    private String userIdCard;

    /**
     * 老人关系(0我的父母 1我的伴侣 2其他)
     */
    private String userType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
