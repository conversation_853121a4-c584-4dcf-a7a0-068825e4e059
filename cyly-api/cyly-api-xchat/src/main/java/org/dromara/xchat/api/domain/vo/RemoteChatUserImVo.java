package org.dromara.xchat.api.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * IM用户信息对象 chat_user_im
 *
 * <AUTHOR>
 * @date 2024-03-26
 */
@Data
@NoArgsConstructor
public class RemoteChatUserImVo implements Serializable
{
    @Serial
    private static final long serialVersionUID = 1L;

    /** 云信 IM 账号 */
    private String accid;

    /** 登录密钥token */
    private String token;

    /** 部门ID */
    private Long deptId;

    /** 名称 */
    private String name;

    /** 用户昵称 */
    private String nickName;

    /** 用户签名 */
    private String sign;

    /** 用户类型（00系统用户） */
    private String userType;

    /** 用户邮箱 */
    private String email;

    /** 用户生日 */
    private String birth;

    /** 手机号码 */
    private String mobile;

    /** 用户性别（0未知 1男 2女）- 这里参考的是IM的，后台管理的是 （0男 1女 2未知） */
    private Integer gender;

    /** 用户头像 URL */
    private String icon;

    /** 最后登录IP */
    private String loginIp;

    /** 最后登录时间 */
    private Date loginDate;

    /** 用户资料扩展字段 */
    private String ex;
    /**
     * APP的token,跟后台管理的校验类似
     */
    private String appToken;

    /**
     * 是否同步好友
     */
    private Boolean isAscFriend = false;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * x-chat身份（0亲属 1护理员）
     */
    private String chatIdentity;
}
