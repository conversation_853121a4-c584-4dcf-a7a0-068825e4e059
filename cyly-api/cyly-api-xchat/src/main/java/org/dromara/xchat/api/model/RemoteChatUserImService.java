package org.dromara.xchat.api.model;


import org.dromara.xchat.api.domain.vo.RemoteChatUserImVo;

import java.util.List;

/**
 * 用户服务
 *
 * <AUTHOR> Li
 */
public interface RemoteChatUserImService {
    RemoteChatUserImVo selectUserByPhoneNumber(String phoneNumber);

    void insertChatUserIm(RemoteChatUserImVo convert);

    List<RemoteChatUserImVo> selectAllSysUserImList(Long deptId, String accid);
}
