package org.dromara.xchat.api.model;



import org.dromara.xchat.api.domain.vo.LoginUserResource;
import org.dromara.xchat.api.domain.vo.RemoteChatUserImVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 21:34
 */
public interface RemoteNeteaseClient {
    RemoteChatUserImVo register(LoginUserResource userResource) ;

    String sendVerificationCode(String smsCode, String phoneNumber);

    Map<String, Object> refreshUserToken(String accId);

    void addFriend(String accid, List<RemoteChatUserImVo> convert, Long userId);
}
