package org.dromara.aea.api.domain.bo;

import java.util.Date;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import lombok.Data;
import java.io.Serializable;


/**
 * 长者用户视图对象 aea_elder
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
@Data
@ExcelIgnoreUnannotated
public class RemoteElderBo implements Serializable {


    /**
     * 长者id
     */
    @ExcelProperty(value = "长者id")
    private Long id;

    /**
     * 长者姓名
     */
    @ExcelProperty(value = "长者姓名")
    private String name;

    /**
     * 长者性别 1男0女
     */
    @ExcelProperty(value = "长者性别 1男0女")
    private Long gender;

    /**
     * 出生日期
     */
    @ExcelProperty(value = "出生日期")
    private Date birthday;

    /**
     * 年龄
     */
    @ExcelProperty(value = "年龄")
    private Long age;

    /**
     * 身高
     */
    @ExcelProperty(value = "身高")
    private String height;

    /**
     * 体重
     */
    @ExcelProperty(value = "体重")
    private String weight;

    /**
     * 婚姻状况编码 1未婚 2已婚 3丧偶 4离异
     */
    @ExcelProperty(value = "婚姻状况编码 1未婚 2已婚 3丧偶 4离异")
    private String maritalCode;

    /**
     * 身份证类型
     * 1 居民身份证
     * 2 社保卡
     */
    @ExcelProperty(value = "身份证类型 1 居民身份证 2 社保卡")
    private Long idCardType;

    /**
     * 身份证号
     */
    @ExcelProperty(value = "身份证号")
    private String idCardNumber;

    /**
     * 微信账号
     */
    @ExcelProperty(value = "微信账号")
    private String wechatPhone;

    /**
     * 固定电话
     */
    @ExcelProperty(value = "固定电话")
    private String homePhone;

    /**
     * 精神健康 1有精神病史 2无
     */
    @ExcelProperty(value = "精神健康 1有精神病史 2无")
    private String mentalHealth;

    /**
     * 进行评估原因  1首次评估  2常规评估  3即时评估  4因对评估结果有疑问进行复评  5退出服务评估-包括服务最后三天   6退出服务跟进评估  7其他-例如上诉、研究
     */
    @ExcelProperty(value = "进行评估原因  1首次评估  2常规评估  3即时评估  4因对评估结果有疑问进行复评  5退出服务评估-包括服务最后三天   6退出服务跟进评估  7其他-例如上诉、研究")
    private String reasonForEvaluation;

    /**
     * 最近评估等级
     * 1能力完好
     * 2能力轻度受损(轻度失能)
     * 3能力中度受损(中度失能)
     * 4能力重度受损(重度失能)
     * 5能力完全丧失(完全失能)
     */
    @ExcelProperty(value = "最近评估等级 1能力完好 2能力轻度受损(轻度失能) 3能力中度受损(中度失能) 4能力重度受损(重度失能) 5能力完全丧失(完全失能)")
    private String latestEvalGrade;

    /**
     * 经济来源  1退休金/养老金 2子女补贴 3亲友资助 4国家普惠型补贴 5个人储蓄6其他补贴
     */
    @ExcelProperty(value = "经济来源  1退休金/养老金 2子女补贴 3亲友资助 4国家普惠型补贴 5个人储蓄6其他补贴")
    private String incomeBy;

    /**
     * 社保号码
     */
    @ExcelProperty(value = "社保号码")
    private String ssNumber;

    /**
     * 支付方式
     */
    @ExcelProperty(value = "支付方式")
    private String paymentWay;

    /**
     * 类别(居家，机构）
     */
    @ExcelProperty(value = "类别(居家，机构）")
    private Long kind;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态")
    private Long status;

    /**
     * 民族
     */
    @ExcelProperty(value = "民族")
    private String ethnic;

    /**
     * 省（居住地）
     */
    @ExcelProperty(value = "省", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "居=住地")
    private String residenceProvince;

    /**
     * 市（居住地）
     */
    @ExcelProperty(value = "市", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "居=住地")
    private String residenceCity;

    /**
     * 区/县（居住地）
     */
    @ExcelProperty(value = "区/县", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "居=住地")
    private String residenceDistrict;

    /**
     * 街道/镇（居住地）
     */
    @ExcelProperty(value = "街道/镇", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "居=住地")
    private String residenceStreet;

    /**
     * 社区/街道（居住地）
     */
    @ExcelProperty(value = "社区/街道", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "居=住地")
    private String residenceCommunity;

    /**
     * 详细地址（居住地）
     */
    @ExcelProperty(value = "详细地址", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "居=住地")
    private String residenceAddressDetail;

    /**
     * 省（户籍地）
     */
    @ExcelProperty(value = "省", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "户=籍地")
    private String idCardProvince;

    /**
     * 市（户籍地）
     */
    @ExcelProperty(value = "市", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "户=籍地")
    private String idCardCity;

    /**
     * 区/县（户籍地）
     */
    @ExcelProperty(value = "区/县", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "户=籍地")
    private String idCardDistrict;

    /**
     * 街道/镇（户籍地）
     */
    @ExcelProperty(value = "街道/镇", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "户=籍地")
    private String idCardStreet;

    /**
     * 社区/村委（户籍地）
     */
    @ExcelProperty(value = "社区/村委", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "户=籍地")
    private String idCardCommunity;

    /**
     * 详细地址（户籍地）
     */
    @ExcelProperty(value = "详细地址", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "户=籍地")
    private String idCardAddressDetail;

    /**
     * 政治面貌：1中共党员, 2共青团员, 3民主党派, 4无党派人士, 5群众
     */
    @ExcelProperty(value = "政治面貌：1中共党员, 2共青团员, 3民主党派, 4无党派人士, 5群众")
    private Long politicalAffiliation;

    /**
     * 老人特殊分类
     * 1 特困
     * 2 低保
     * 3 低保边缘
     */
    @ExcelProperty(value = "老人特殊分类1 特困 2 低保 3 低保边缘")
    private Long specialType;

    /**
     * 亲属名字
     */
    @ExcelProperty(value = "亲属名字")
    private String relativeName;

    /**
     * 亲属联系电话
     */
    @ExcelProperty(value = "亲属联系电话")
    private String relativePhone;

    /**
     * 亲属身份证号
     */
    @ExcelProperty(value = "亲属身份证号")
    private String relativeIdNumber;

    /**
     * 亲属与老人关系：1配偶, 2子女, 3父母, 4兄弟姐妹, 5孙子/女, 6其他亲属
     */
    @ExcelProperty(value = "亲属与老人关系：1配偶, 2子女, 3父母, 4兄弟姐妹, 5孙子/女, 6其他亲属")
    private Long relativeRelationship;

    /**
     * 亲属居住的详细地址
     */
    @ExcelProperty(value = "亲属居住的详细地址")
    private String relativeResidenceAddress;

    /**
     * 教育程度代码
     */
    @ExcelProperty(value = "教育程度代码")
    private String educationCode;

    /**
     * 教育程度名称
     */
    @ExcelProperty(value = "教育程度名称")
    private String educationName;

    /**
     * 居住状况代码
     */
    @ExcelProperty(value = "居住状况代码")
    private String residencyCode;

    /**
     * 居住状况名称
     */
    @ExcelProperty(value = "居住状况名称")
    private String residencyName;

    /**
     * 居住情况（1独居、2与配偶居住、3与子女居住、4与父母居住、5与姐妹兄弟居住、6与其它亲属居住、7与非亲属关系的人居住、8养老机构）
     */
    @ExcelProperty(value = "居住情况", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=独居、2与配偶居住、3与子女居住、4与父母居住、5与姐妹兄弟居住、6与其它亲属居住、7与非亲属关系的人居住、8养老机构")
    private String liveWithWhoCode;

    /**
     * 居住情况（1独居、2与配偶居住、3与子女居住、4与父母居住、5与姐妹兄弟居住、6与其它亲属居住、7与非亲属关系的人居住、8养老机构）
     */
    @ExcelProperty(value = "居住情况", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=独居、2与配偶居住、3与子女居住、4与父母居住、5与姐妹兄弟居住、6与其它亲属居住、7与非亲属关系的人居住、8养老机构")
    private String liveWithWhoName;

    /**
     * 入院时间（机构老人）
     */
    @ExcelProperty(value = "入院时间", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "机=构老人")
    private Long inTime;

    /**
     * 房号（机构老人）
     */
    @ExcelProperty(value = "房号", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "机=构老人")
    private String roomNum;

    /**
     * 床号（机构老人）
     */
    @ExcelProperty(value = "床号", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "机=构老人")
    private String bedNum;

    /**
     * 护理小组id（机构老人）
     */
    @ExcelProperty(value = "护理小组id", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "机=构老人")
    private Long grpId;

    /**
     * 负责组员id（机构老人）
     */
    @ExcelProperty(value = "负责组员id", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "机=构老人")
    private Long grpUserId;

    /**
     * 负责组员姓名（机构老人）
     */
    @ExcelProperty(value = "负责组员姓名", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "机=构老人")
    private String grpUserName;

    /**
     * 负责组员手机（机构老人）
     */
    @ExcelProperty(value = "负责组员手机", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "机=构老人")
    private String grpUserMobile;

    /**
     * 照护级别
     */
    @ExcelProperty(value = "照护级别")
    private Long careLevel;

    /**
     * 注意事项
     */
    @ExcelProperty(value = "注意事项")
    private String note;

    /**
     * 头像
     */
    @ExcelProperty(value = "头像")
    private String avatar;

    /**
     * 地理位置
     */
    @ExcelProperty(value = "地理位置")
    private String location;

    /**
     * 宗教信仰
     */
    @ExcelProperty(value = "宗教信仰")
    private String religiousBelief;

    /**
     * 参保地
     */
    @ExcelProperty(value = "参保地")
    private String insuredPlace;

    /**
     * 失能时间
     */
    @ExcelProperty(value = "失能时间")
    private Date disabilityTime;

    /**
     * 是否首次申请
     */
    @ExcelProperty(value = "是否首次申请")
    private Date applying;

    /**
     * 联系电话
     */
    @ExcelProperty(value = "联系电话")
    private String phone;

    /**
     * 保障方式
     */
    @ExcelProperty(value = "保障方式")
    private String guaranteeMethod;

    /**
     * 照护者
     */
    @ExcelProperty(value = "照护者")
    private String caregiver;

    /**
     * 评估参照日期
     */
    @ExcelProperty(value = "评估参照日期")
    private Date evaReferenceDate;

    /**
     * 申请人期望的照顾目标
     */
    @ExcelProperty(value = "申请人期望的照顾目标")
    private String expectedCareGoals;

    /**
     * 上次入住医院至今时间
     */
    @ExcelProperty(value = "上次入住医院至今时间")
    private String untilNow;

    /**
     * 申请人对入住长期照顾院舍决定的控制
     */
    @ExcelProperty(value = "申请人对入住长期照顾院舍决定的控制")
    private String controlOfDecisions;

    /**
     * 开始入住时间
     */
    @ExcelProperty(value = "开始入住时间")
    private Date startDate;

    /**
     * 出生地
     */
    @ExcelProperty(value = "出生地")
    private String placeOfBirth;

    /**
     * 种族
     */
    @ExcelProperty(value = "种族")
    private String race;

    /**
     * 过去5年内入住院舍记录
     */
    @ExcelProperty(value = "过去5年内入住院舍记录")
    private String pastRecord;

    /**
     * 主要使用语言
     */
    @ExcelProperty(value = "主要使用语言")
    private String mainLanguage;

    /**
     * 既往工作
     */
    @ExcelProperty(value = "既往工作")
    private String previousWork;


}
