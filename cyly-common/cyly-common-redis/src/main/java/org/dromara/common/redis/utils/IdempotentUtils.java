package org.dromara.common.redis.utils;

import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * 幂等性工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class IdempotentUtils {

    /**
     * 幂等键前缀
     */
    private static final String IDEMPOTENT_KEY_PREFIX = "idempotent:";

    /**
     * 尝试获取幂等锁
     *
     * @param key        幂等键
     * @param expireTime 过期时间
     * @param timeUnit   时间单位
     * @return 是否获取成功
     */
    public static boolean tryIdempotent(String key, long expireTime, TimeUnit timeUnit) {
        String fullKey = IDEMPOTENT_KEY_PREFIX + key;
        Boolean result = RedisUtils.setObjectIfAbsent(fullKey, "1", Duration.ofMillis(timeUnit.toMillis(expireTime)));
        return !Boolean.TRUE.equals(result);
    }

    /**
     * 释放幂等锁
     *
     * @param key 幂等键
     */
    public static void releaseIdempotent(String key) {
        String fullKey = IDEMPOTENT_KEY_PREFIX + key;
        RedisUtils.deleteObject(fullKey);
    }

    /**
     * 执行幂等操作
     *
     * @param key        幂等键
     * @param expireTime 过期时间
     * @param timeUnit   时间单位
     * @param supplier   操作
     * @param <T>        返回类型
     * @return 操作结果
     */
    public static <T> T executeWithIdempotent(String key, long expireTime, TimeUnit timeUnit,
                                             IdempotentSupplier<T> supplier) {
        // 尝试获取幂等锁
        if (tryIdempotent(key, expireTime, timeUnit)) {
            log.info("幂等性检查：操作已执行，key={}", key);
            return supplier.getOnIdempotent();
        }

        try {
            // 执行操作
            return supplier.get();
        } catch (Exception e) {
            // 操作失败，释放幂等锁
            releaseIdempotent(key);
            throw e;
        }
    }

    /**
     * 执行幂等操作（无返回值）
     *
     * @param key        幂等键
     * @param expireTime 过期时间
     * @param timeUnit   时间单位
     * @param runnable   操作
     * @return 是否执行
     */
    public static boolean executeWithIdempotent(String key, long expireTime, TimeUnit timeUnit,
                                              IdempotentRunnable runnable) {
        // 尝试获取幂等锁
        if (tryIdempotent(key, expireTime, timeUnit)) {
            log.info("幂等性检查：操作已执行，key={}", key);
            return false;
        }

        try {
            // 执行操作
            runnable.run();
            return true;
        } catch (Exception e) {
            // 操作失败，释放幂等锁
            releaseIdempotent(key);
            throw e;
        }
    }

    /**
     * 幂等操作接口（有返回值）
     *
     * @param <T> 返回类型
     */
    public interface IdempotentSupplier<T> {
        /**
         * 获取操作结果
         *
         * @return 操作结果
         */
        T get();

        /**
         * 获取幂等时的返回值
         *
         * @return 幂等时的返回值
         */
        T getOnIdempotent();
    }

    /**
     * 幂等操作接口（无返回值）
     */
    public interface IdempotentRunnable {
        /**
         * 执行操作
         */
        void run();
    }
}
