package org.dromara.common.log.event;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.dromara.common.core.constant.Constants;
import org.dromara.common.core.utils.ServletUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.core.utils.ip.AddressUtils;
import org.dromara.common.satoken.utils.AdminLoginHelper;
import org.dromara.system.api.RemoteAppLogService;
import org.dromara.system.api.RemoteClientService;
import org.dromara.system.api.RemoteLogService;
import org.dromara.system.api.domain.bo.RemoteAppLogininforBo;
import org.dromara.system.api.domain.bo.RemoteAppOperLogBo;
import org.dromara.system.api.domain.bo.RemoteLogininforBo;
import org.dromara.system.api.domain.bo.RemoteOperLogBo;
import org.dromara.system.api.domain.vo.RemoteClientVo;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 异步调用日志服务
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class LogEventListener {

    @DubboReference
    private RemoteLogService remoteLogService;
    @DubboReference
    private RemoteClientService remoteClientService;

    @DubboReference
    private RemoteAppLogService remoteAppLogService;

    /**
     * 保存系统操作日志记录
     */
    @Async
    @EventListener
    public void saveLog(OperLogEvent operLogEvent) {
        RemoteOperLogBo sysOperLog = BeanUtil.toBean(operLogEvent, RemoteOperLogBo.class);
        remoteLogService.saveLog(sysOperLog);
    }

    /**
     * 保存用户操作日志记录
     */
    @Async
    @EventListener
    public void saveAppLog(AppOperLogEvent appOperLogEvent) {
        RemoteAppOperLogBo appOperLog = BeanUtil.toBean(appOperLogEvent, RemoteAppOperLogBo.class);
        remoteAppLogService.saveLog(appOperLog);
    }

    /**
     * 保存登录信息
     *
     * @param logininforEvent 登录信息事件对象，包含登录相关信息
     */
    @Async
    @EventListener
    public void saveLogininfor(LogininforEvent logininforEvent) {
        // 获取HTTP请求对象
        HttpServletRequest request = logininforEvent.getRequest();
        // 解析用户代理信息
        final UserAgent userAgent = UserAgentUtil.parse(request.getHeader("User-Agent"));
        // 获取客户端IP地址
        final String ip = ServletUtils.getClientIP(request);
        // 获取客户端真实地址
        String address = AddressUtils.getRealAddressByIP(ip);
        // 获取客户端操作系统
        String os = userAgent.getOs().getName();
        // 获取客户端浏览器
        String browser = userAgent.getBrowser().getName();

        // 构建日志信息
        String logInfo = buildLogInfo(ip, address, logininforEvent);
        // 打印信息到日志
        log.info(logInfo, logininforEvent.getArgs());

        // 封装对象
        RemoteLogininforBo logininfor = buildRemoteLogininfor(logininforEvent, request,ip, address, os, browser);

        // 保存登录信息
        remoteLogService.saveLogininfor(logininfor);
    }

    private String buildLogInfo(String ip, String address, LogininforEvent logininforEvent) {
        StringBuilder s = new StringBuilder();
        s.append(getBlock(ip));
        s.append(address);
        s.append(getBlock(logininforEvent.getUsername()));
        s.append(getBlock(logininforEvent.getStatus()));
        s.append(getBlock(logininforEvent.getMessage()));
        return s.toString();
    }

    private RemoteLogininforBo buildRemoteLogininfor(LogininforEvent logininforEvent, HttpServletRequest request,String ip, String address, String os, String browser) {
        RemoteLogininforBo logininfor = new RemoteLogininforBo();
        logininfor.setTenantId(logininforEvent.getTenantId());
        logininfor.setUserName(logininforEvent.getUsername());
        logininfor.setIpaddr(ip);
        logininfor.setLoginLocation(address);
        logininfor.setBrowser(browser);
        logininfor.setOs(os);
        logininfor.setMsg(logininforEvent.getMessage());

        // 设置客户端信息
        String clientid = request.getHeader(AdminLoginHelper.CLIENT_KEY);
        if (StringUtils.isNotBlank(clientid)) {
            RemoteClientVo clientVo = remoteClientService.queryByClientId(clientid);
            if (ObjectUtil.isNotNull(clientVo)) {
                logininfor.setClientKey(clientVo.getClientKey());
                logininfor.setDeviceType(clientVo.getDeviceType());
            }
        }

        // 设置日志状态
        if (StringUtils.equalsAny(logininforEvent.getStatus(), Constants.LOGIN_SUCCESS, Constants.LOGOUT, Constants.REGISTER)) {
            logininfor.setStatus(Constants.SUCCESS);
        } else if (Constants.LOGIN_FAIL.equals(logininforEvent.getStatus())) {
            logininfor.setStatus(Constants.FAIL);
        }

        return logininfor;
    }



    @Async
    @EventListener
    public void saveAppLogininfor(AppLogininforEvent appLogininforEvent) {
        HttpServletRequest request = appLogininforEvent.getRequest();
        final UserAgent userAgent = UserAgentUtil.parse(request.getHeader("User-Agent"));
        final String ip = ServletUtils.getClientIP(request);
        String address = AddressUtils.getRealAddressByIP(ip);
        StringBuilder s = new StringBuilder();
        s.append(getBlock(ip));
        s.append(address);
        s.append(getBlock(appLogininforEvent.getUsername()));
        s.append(getBlock(appLogininforEvent.getStatus()));
        s.append(getBlock(appLogininforEvent.getMessage()));
        // 打印信息到日志
        log.info(s.toString(), appLogininforEvent.getArgs());
        // 获取客户端操作系统
        String os = userAgent.getOs().getName();
        // 获取客户端浏览器
        String browser = userAgent.getBrowser().getName();
        // 封装对象
        RemoteAppLogininforBo logininfor = new RemoteAppLogininforBo();
        logininfor.setTenantId(appLogininforEvent.getTenantId());
        logininfor.setUserName(appLogininforEvent.getUsername());
        logininfor.setIpaddr(ip);
        logininfor.setLoginLocation(address);
        logininfor.setBrowser(browser);
        logininfor.setOs(os);
        logininfor.setMsg(appLogininforEvent.getMessage());
        // 日志状态
        if (StringUtils.equalsAny(appLogininforEvent.getStatus(), Constants.LOGIN_SUCCESS, Constants.LOGOUT, Constants.REGISTER)) {
            logininfor.setStatus(Constants.SUCCESS);
        } else if (Constants.LOGIN_FAIL.equals(appLogininforEvent.getStatus())) {
            logininfor.setStatus(Constants.FAIL);
        }
        remoteAppLogService.saveLogininfor(logininfor);
    }

    private String getBlock(Object msg) {
        if (msg == null) {
            msg = "";
        }
        return "[" + msg + "]";
    }

}
