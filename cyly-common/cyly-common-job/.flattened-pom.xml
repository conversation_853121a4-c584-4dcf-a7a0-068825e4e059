<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.dromara</groupId>
    <artifactId>cyly-common</artifactId>
    <version>2.3.0</version>
  </parent>
  <groupId>org.dromara</groupId>
  <artifactId>cyly-common-job</artifactId>
  <version>2.3.0</version>
  <description>cyly-common-job 定时任务</description>
  <dependencies>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-autoconfigure</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-commons</artifactId>
    </dependency>
    <dependency>
      <groupId>com.aizuda</groupId>
      <artifactId>snail-job-client-starter</artifactId>
      <version>${snailjob.version}</version>
    </dependency>
    <dependency>
      <groupId>com.aizuda</groupId>
      <artifactId>snail-job-client-job-core</artifactId>
      <version>${snailjob.version}</version>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>cyly-common-core</artifactId>
    </dependency>
  </dependencies>
</project>
