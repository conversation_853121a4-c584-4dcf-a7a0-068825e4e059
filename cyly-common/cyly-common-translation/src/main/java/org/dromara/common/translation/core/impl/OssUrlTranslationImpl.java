package org.dromara.common.translation.core.impl;

import org.dromara.common.translation.annotation.TranslationType;
import org.dromara.common.translation.constant.TransConstant;
import org.dromara.common.translation.core.TranslationInterface;
import org.dromara.resource.api.RemoteFileService;
import lombok.AllArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;

/**
 * OSS翻译实现
 *
 * <AUTHOR> Li
 */
@AllArgsConstructor
@TranslationType(type = TransConstant.OSS_ID_TO_URL)
public class OssUrlTranslationImpl implements TranslationInterface<String> {
    /**
     * Dubbo服务引用配置注解
     * @param mock     启用mock模式（值为"true"时）:
     *                 - 当远程服务调用失败时会触发mock降级处理
     *                 - 需要配套实现对应的mock服务类
     * @param timeout  远程服务调用超时时间配置（单位：毫秒）:
     *                 - 设置15000ms(15秒)的超时熔断保护
     *                 - 超过该时长未响应将抛出超时异常
     */
    @DubboReference(mock = "true",timeout = 15000)//设置超时时间为15秒不采用默认超时时间
    private RemoteFileService remoteFileService;

    @Override
    public String translation(Object key, String other) {
        return remoteFileService.selectUrlByIds(key.toString());
    }
}
