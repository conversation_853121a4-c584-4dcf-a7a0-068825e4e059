package org.dromara.common.satoken.utils;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.dromara.common.satoken.annotation.SystemType;
import org.springframework.stereotype.Component;

/**
 * 系统类型切面类，用于检查用户访问系统的权限
 * <AUTHOR>
 * @date 2025年2月23日 15:21:02
 */
@Aspect
@Component
public class SystemTypeAspect {

    /**
     * 环绕通知，用于检查用户访问系统的权限
     *
     * @param joinPoint 切入点对象，提供了关于当前执行方法的信息
     * @param systemType 方法上标注的SystemType注解，包含系统类型信息
     * @return 执行的目标方法的结果
     * @throws Throwable 如果用户未登录或系统标识不符，抛出异常
     */
    @Around("@annotation(systemType)")
    public Object checkSystemAccess(ProceedingJoinPoint joinPoint, SystemType systemType) throws Throwable {
        // 1. 获取当前用户的系统标识（从会话中读取）
        String userSystemId = (String) StpUserUtil.getSession().get("systemType");

        // 2. 如果未登录或系统标识不匹配，抛出无权限异常
        if (!StpUserUtil.isLogin() || !systemType.value().equals(userSystemId)) {
            //throw new RuntimeException("无权限访问：系统标识不符");内部测试
            throw new RuntimeException("无权限访问！");
        }

        // 3. 放行请求
        return joinPoint.proceed();

    }
}
