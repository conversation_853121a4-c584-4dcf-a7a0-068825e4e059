package org.dromara.common.satoken.utils;

import org.dromara.common.core.constant.TenantConstants;
import org.dromara.common.core.constant.UserConstants;
import org.dromara.common.core.enums.UserType;
import org.dromara.system.api.model.LoginUser;
import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.SaLoginModel;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.Objects;
import java.util.Set;

/**
 * 登录鉴权助手
 * <p>
 * user_type 为 用户类型 同一个用户表 可以有多种用户类型 例如 pc,app
 * deivce 为 设备类型 同一个用户类型 可以有 多种设备类型 例如 web,ios
 * 可以组成 用户类型与设备类型多对多的 权限灵活控制
 * <p>
 * 多用户体系 针对 多种用户类型 但权限控制不一致
 * 可以组成 多用户类型表与多设备类型 分别控制权限
 *
 * <AUTHOR>
 * @date 2025年2月9日 16:50:40
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class AdminLoginHelper {

    public static final String LOGIN_USER_KEY = "loginUser";
    public static final String TENANT_KEY = "tenantId";
    public static final String USER_KEY = "userId";
    public static final String USER_NAME_KEY = "userName";
    public static final String DEPT_KEY = "deptId";
    public static final String DEPT_NAME_KEY = "deptName";

    public static final String UNIT_KEY = "unitId";
    public static final String UNIT_NAME_KEY = "unitName";

    public static final String CLIENT_KEY = "clientid";

    public static final String USER_TYPE = "userType";

    public static final String AREA_KEY = "areaId";
    public static final String AREA_NAME_KEY = "areaName";
    public static final String USER_PHONENUM = "phoneNum";

    public static final String IS_SUPER_ADMIN_KEY = "isSuperAdmin";

    public static final String NICK_NAME_KEY = "nickName";


    /**
     * 登录系统 基于 设备类型
     * 针对相同用户体系不同设备
     *
     * @param loginUser 登录用户信息
     * @param model     配置参数
     */
    public static void login(LoginUser loginUser, SaLoginModel model) {
        model = ObjectUtil.defaultIfNull(model, new SaLoginModel());
        StpUtil.login(loginUser.getLoginId(),
            model.setExtra(TENANT_KEY, loginUser.getTenantId())
                .setExtra(USER_KEY, loginUser.getUserId())
                .setExtra(USER_NAME_KEY, loginUser.getUsername())
                .setExtra(DEPT_KEY, loginUser.getDeptId())
                .setExtra(DEPT_NAME_KEY, loginUser.getDeptName())
                .setExtra(UNIT_KEY, loginUser.getUnitId())
                .setExtra(UNIT_NAME_KEY, loginUser.getUnitName())
                .setExtra(USER_TYPE, loginUser.getUserType())
                .setExtra(USER_PHONENUM, loginUser.getPhoneNumber())
                .setExtra(NICK_NAME_KEY, loginUser.getNickname())
        );

        StpUtil.getTokenSession().set(LOGIN_USER_KEY, loginUser);
    }

    /**
     * 获取用户(多级缓存)
     */
    public static LoginUser getLoginUser() {
        SaSession session = StpUtil.getTokenSession();
        if (ObjectUtil.isNull(session)) {
            return null;
        }
        return (LoginUser) session.get(LOGIN_USER_KEY);
    }

    /**
     * 获取用户基于token
     */
    public static LoginUser getLoginUser(String token) {
        SaSession session = StpUtil.getTokenSessionByToken(token);
        if (ObjectUtil.isNull(session)) {
            return null;
        }
        return (LoginUser) session.get(LOGIN_USER_KEY);
    }

    /**
     * 获取用户id
     */
    public static Long getUserId() {
        return Convert.toLong(getExtra(USER_KEY));
    }

    /**
     * 获取租户ID
     */
    public static String getTenantId() {
        return Convert.toStr(getExtra(TENANT_KEY));
    }

    /**
     * 获取部门ID
     */
    public static Long getDeptId() {
        return Convert.toLong(getExtra(DEPT_KEY));
    }

    public static String getDeptName() {
        return Convert.toStr(getExtra(DEPT_NAME_KEY));
    }

    /**
     * 获取部门ID
     */
    public static Long getUnitId() {
        return Convert.toLong(getExtra(UNIT_KEY));
    }

    public static String getUnitName() {
        return Convert.toStr(getExtra(UNIT_NAME_KEY));
    }


    public static Integer getIsSuperAdmin() {
        return Convert.toInt(getExtra(IS_SUPER_ADMIN_KEY));
    }


    public static String getNickName() {
        return Convert.toStr(getExtra(NICK_NAME_KEY));
    }

    public static String getAreaId() {
        return Convert.toStr(getExtra(AREA_KEY));
    }

    private static Object getExtra(String key) {
        try {
            return StpUtil.getExtra(key);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取用户账户
     */
    public static String getUsername() {
        return getLoginUser().getUsername();
    }

    /**
     * 获取用户类型
     */
    public static UserType getUserType() {
        String loginType = StpUtil.getLoginIdAsString();
        return UserType.getUserType(loginType);
    }

    /**
     * 是否为超级管理员
     *
     * @param userId 用户ID
     * @return 结果
     */
    public static boolean isSuperAdmin(Long userId) {
        return UserConstants.SUPER_ADMIN_ID.equals(userId);
    }

//    public static boolean isSuperAdmin() {
//        return getIsSuperAdmin() == 1;
//    }

    /**
     * 是否为租户管理员
     *
     * @param rolePermission 角色权限标识组
     * @return 结果
     */
    public static boolean isTenantAdmin(Set<String> rolePermission) {
        return rolePermission.contains(TenantConstants.TENANT_ADMIN_ROLE_KEY);
    }

    public static boolean isTenantAdmin() {
        return Convert.toBool(isTenantAdmin(Objects.requireNonNull(getLoginUser()).getRolePermission()));
    }

    public static boolean isLogin() {
        try {
            return getLoginUser() != null;
        } catch (Exception e) {
            return false;
        }
    }

}
