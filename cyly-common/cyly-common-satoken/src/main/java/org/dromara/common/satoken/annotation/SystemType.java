package org.dromara.common.satoken.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @date 2025年2月23日 15:19:02
 */

// 定义一个运行时保留的注解，用于标记方法
@Retention(RetentionPolicy.RUNTIME)
// 该注解只能用于方法
@Target(ElementType.METHOD)
// 定义系统访问权限的注解
public @interface SystemType {
    /**
     * 允许访问的系统标识列表（如 "aea", "ile"）
     */
    String value();
}
