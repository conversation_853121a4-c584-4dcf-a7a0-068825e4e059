package org.dromara.common.satoken.utils;

import org.dromara.common.core.enums.UserType;
import org.dromara.system.api.model.AppLoginUser;
import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.SaLoginModel;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * 描述:
 * 用户端登录鉴权助手
 *
 * <AUTHOR>
 * @since 2025年2月2日17:22:57
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class AppLoginHelper {
    public static final String LOGIN_USER_KEY = "appLoginUser";
    public static final String TENANT_KEY = "tenantId";
    public static final String USER_KEY = "userId";
    public static final String USER_NAME_KEY = "userName";
    public static final String USER_TYPE = "userType";
    public static final String USER_PHONENUMBER = "userPhonenumber";
    public static final String CLIENT_KEY = "clientid";

    /**
     * 登录系统 基于 设备类型
     * 针对相同用户体系不同设备
     *
     * @param loginUser 登录用户信息
     * @param model     配置参数
     */
    public static void login(AppLoginUser loginUser, SaLoginModel model) {
        model = ObjectUtil.defaultIfNull(model, new SaLoginModel());
        StpUserUtil.login(loginUser.getLoginId(),
            model.setExtra(TENANT_KEY, loginUser.getTenantId())
                .setExtra(USER_KEY, loginUser.getUserId())
                .setExtra(USER_NAME_KEY, loginUser.getUserName())
                .setExtra(USER_TYPE, loginUser.getUserType())
                .setExtra(USER_PHONENUMBER, loginUser.getPhoneNumber())
        );
        StpUserUtil.login(loginUser.getLoginId(),model);
        StpUserUtil.getTokenSession().set(LOGIN_USER_KEY, loginUser);
    }

    /**
     * 获取用户(多级缓存)
     */
    public static AppLoginUser getLoginUser() {
        SaSession session = StpUserUtil.getTokenSession();
        if (ObjectUtil.isNull(session)) {
            return null;
        }
        return (AppLoginUser) session.get(LOGIN_USER_KEY);
    }

    /**
     * 获取用户基于token
     */
    public static AppLoginUser getLoginUser(String token) {
        SaSession session = StpUserUtil.getTokenSessionByToken(token);
        if (ObjectUtil.isNull(session)) {
            return null;
        }
        return (AppLoginUser) session.get(LOGIN_USER_KEY);
    }

    /**
     * 获取用户id
     */
    public static Long getUserId() {
        return Convert.toLong(getExtra(USER_KEY));
    }

    /**
     * 获取租户ID
     */
    public static String getTenantId() {
        return Convert.toStr(getExtra(TENANT_KEY));
    }

    public static String getUserPhoneNumber() {
        return Convert.toStr(getExtra(USER_PHONENUMBER));
    }

    private static Object getExtra(String key) {
        try {
            return StpUserUtil.getExtra(key);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取用户账户
     */
    public static String getUsername() {
        return Objects.requireNonNull(getLoginUser()).getUserName();
    }

    /**
     * 获取用户类型
     */
    public static UserType getUserType() {
        String loginType = StpUserUtil.getLoginIdAsString();
        return UserType.getUserType(loginType);
    }

    public static boolean isLogin() {
        try {
            return getLoginUser() != null;
        } catch (Exception e) {
            return false;
        }
    }

}
