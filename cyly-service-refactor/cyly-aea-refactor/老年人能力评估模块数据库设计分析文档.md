# 老年人能力评估模块数据库设计分析文档

## 1. 项目概述

### 1.1 模块简介
本模块基于 **GB/T 42195-2022《老年人能力评估规范》** 国家标准，实现了完整的老年人能力评估流程，包括评估员信息收集、标准化问卷评估、自动分数统计和专业PDF报告生成功能。

### 1.2 技术栈
- **数据库**: MySQL 8.0+
- **字符集**: utf8mb4
- **存储引擎**: InnoDB
- **框架**: Spring Boot + MyBatis-Plus
- **标准依据**: GB/T 42195-2022《老年人能力评估规范》

## 2. 数据库表结构设计

### 2.1 核心表结构概览

| 表名 | 中文名称 | 主要功能 | 记录数量级 |
|------|----------|----------|------------|
| aea_gb_standard_questionnaire | 国标评估问卷表 | 存储问卷基本信息 | 少量（<10） |
| aea_gb_standard_question | 国标评估问题表 | 存储26个评估问题 | 中等（26+） |
| aea_gb_standard_option | 国标评估选项表 | 存储评分选项 | 中等（130+） |
| aea_assessment_task | 评估任务表 | 管理评估任务 | 大量 |
| aea_assessment_record | 评估记录表 | 记录答题详情 | 大量 |
| aea_assessment_result | 评估结果表 | 存储评估结果 | 大量 |
| aea_assessment_report | 评估报告表 | 管理PDF报告 | 大量 |
| aea_report_template | 报告模板表 | 存储报告模板 | 少量（<10） |

### 2.2 详细表结构分析

#### 2.2.1 国标评估问卷表 (aea_gb_standard_questionnaire)
**设计目的**: 管理不同版本的评估问卷

**核心字段**:
- `questionnaire_name`: 问卷名称
- `category`: 问卷类别（国标/自定义）
- `version`: 版本控制
- `status`: 启用状态

**设计亮点**:
- 支持多版本问卷管理
- 灵活的分类体系
- 状态控制机制

#### 2.2.2 国标评估问题表 (aea_gb_standard_question)
**设计目的**: 存储GB/T 42195-2022标准的26个评估问题

**核心字段**:
- `category`: 一级指标（4个维度）
- `sub_category`: 二级指标（26个具体项目）
- `question_order`: 问题排序
- `max_score`: 最高分值（4分制）

**国标四个一级指标**:
1. **SELF_CARE** - 自理能力（8个二级指标）
2. **BASIC_MOVEMENT** - 基础运动能力（4个二级指标）
3. **MENTAL_STATE** - 精神状态（9个二级指标）
4. **PERCEPTION_SOCIAL** - 感知觉与社会参与（5个二级指标）

#### 2.2.3 国标评估选项表 (aea_gb_standard_option)
**设计目的**: 存储标准化的评分选项

**评分标准**:
- **4分**: 独立完成
- **3分**: 需要指导
- **2分**: 需要少量协助
- **1分**: 需要大量协助
- **0分**: 完全依赖

#### 2.2.4 评估任务表 (aea_assessment_task)
**设计目的**: 管理评估任务的全生命周期

**任务类型**:
- **FIRST**: 首次评估
- **REGULAR**: 定期评估
- **IMMEDIATE**: 即时评估

**状态流转**:
```
PENDING → IN_PROGRESS → COMPLETED
                    ↓
                CANCELLED
```

#### 2.2.5 评估记录表 (aea_assessment_record)
**设计目的**: 记录每个问题的具体答案和得分

**关键特性**:
- 一对一关联（任务-问题）
- 详细的评估备注
- 完整的审计信息

#### 2.2.6 评估结果表 (aea_assessment_result)
**设计目的**: 存储计算后的评估结果

**能力等级划分**:
- **INTACT**: 能力完好
- **MILD**: 轻度受损
- **MODERATE**: 中度受损
- **SEVERE**: 重度受损
- **COMPLETE**: 完全丧失

#### 2.2.7 评估报告表 (aea_assessment_report)
**设计目的**: 管理PDF报告的生成和存储

**报告状态**:
- **DRAFT**: 草稿
- **GENERATED**: 已生成
- **SIGNED**: 已签名

#### 2.2.8 报告模板表 (aea_report_template)
**设计目的**: 管理可配置的报告模板

**模板特性**:
- HTML格式存储
- 版本控制
- 默认模板机制

## 3. 技术设计决策

### 3.1 外键约束策略

#### 3.1.1 决策说明
**选择**: 不使用数据库外键约束

**原因分析**:
1. **扩展性考虑**: 便于后期表结构调整和模块扩展
2. **性能优化**: 避免外键检查带来的性能开销
3. **分布式友好**: 支持未来的分库分表需求
4. **维护灵活性**: 减少数据迁移和结构变更的复杂度

#### 3.1.2 数据完整性保障方案

**应用层控制**:
```java
// 示例：业务层数据完整性检查
@Service
public class AssessmentService {
    
    @Transactional
    public void createAssessmentRecord(AssessmentRecordDto dto) {
        // 1. 验证任务存在性
        validateTaskExists(dto.getTaskId());
        
        // 2. 验证问题有效性
        validateQuestionExists(dto.getQuestionId());
        
        // 3. 验证选项匹配性
        validateOptionBelongsToQuestion(dto.getOptionId(), dto.getQuestionId());
        
        // 4. 执行业务逻辑
        assessmentRecordMapper.insert(dto);
    }
}
```

**索引优化**:
- 关键字段建立索引提升查询性能
- 复合索引支持复杂查询场景
- 唯一索引防止重复数据

**事务管理**:
- 使用 `@Transactional` 确保数据一致性
- 合理的事务边界划分
- 异常回滚机制

### 3.2 字段设计原则

#### 3.2.1 数据类型选择
- **BIGINT**: 主键和外键字段，支持大数据量
- **VARCHAR**: 可变长度文本，节省存储空间
- **TEXT**: 长文本内容，如问题描述
- **DATETIME**: 时间字段，精确到秒
- **CHAR(1)**: 状态字段，固定长度提升性能

#### 3.2.2 字段命名规范
- 使用下划线分隔的小写命名
- 统一的前缀约定（如 `aea_`）
- 语义化的字段名称
- 完整的中文注释

#### 3.2.3 默认值策略
- 状态字段提供合理默认值
- 数值字段默认为0
- 时间字段使用函数默认值
- 避免NULL值的使用

### 3.3 索引设计策略

#### 3.3.1 主键索引
- 所有表使用 `BIGINT AUTO_INCREMENT` 主键
- 保证全局唯一性和高性能

#### 3.3.2 普通索引
```sql
-- 查询优化索引
INDEX idx_questionnaire_id (questionnaire_id),
INDEX idx_category (category),
INDEX idx_question_order (question_order),
INDEX idx_elder_id (elder_id),
INDEX idx_assessment_date (assessment_date),
INDEX idx_task_status (task_status)
```

#### 3.3.3 唯一索引
```sql
-- 防重复索引
UNIQUE KEY uk_task_question (task_id, question_id)
```

## 4. 业务流程设计

### 4.1 评估流程图

```mermaid
flowchart TD
    A[创建评估任务] --> B[收集长者信息]
    B --> C[开始问卷评估]
    C --> D[逐题评分]
    D --> E{是否完成所有题目}
    E -->|否| D
    E -->|是| F[计算各维度得分]
    F --> G[确定能力等级]
    G --> H[生成评估结论]
    H --> I[创建PDF报告]
    I --> J[评估完成]
```

### 4.2 数据流转关系

```mermaid
erDiagram
    aea_elder ||--o{ aea_assessment_task : "一对多"
    aea_gb_standard_questionnaire ||--o{ aea_assessment_task : "一对多"
    aea_assessment_task ||--o{ aea_assessment_record : "一对多"
    aea_assessment_task ||--|| aea_assessment_result : "一对一"
    aea_assessment_task ||--o{ aea_assessment_report : "一对多"
    aea_gb_standard_question ||--o{ aea_assessment_record : "一对多"
    aea_gb_standard_option ||--o{ aea_assessment_record : "一对多"
    aea_report_template ||--o{ aea_assessment_report : "一对多"
```

## 5. 性能优化建议

### 5.1 查询优化

#### 5.1.1 常用查询模式
```sql
-- 1. 按长者查询评估历史
SELECT * FROM aea_assessment_task 
WHERE elder_id = ? 
ORDER BY assessment_date DESC;

-- 2. 获取评估详情
SELECT t.*, r.*, res.* 
FROM aea_assessment_task t
LEFT JOIN aea_assessment_record r ON t.id = r.task_id
LEFT JOIN aea_assessment_result res ON t.id = res.task_id
WHERE t.id = ?;

-- 3. 统计分析查询
SELECT ability_level, COUNT(*) as count
FROM aea_assessment_result
WHERE create_time >= ?
GROUP BY ability_level;
```

#### 5.1.2 索引使用建议
- 查询条件字段建立索引
- 排序字段考虑复合索引
- 避免在索引字段使用函数

### 5.2 存储优化

#### 5.2.1 分区策略
```sql
-- 按时间分区（建议）
PARTITION BY RANGE (YEAR(create_time)) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

#### 5.2.2 归档策略
- 定期归档历史评估数据
- 保留最近2年的热数据
- 冷数据迁移到归档表

## 6. 安全性设计

### 6.1 数据安全

#### 6.1.1 敏感信息保护
- 个人信息字段考虑加密存储
- 访问日志记录
- 数据脱敏处理

#### 6.1.2 权限控制
```java
// 示例：基于角色的访问控制
@PreAuthorize("hasRole('ASSESSOR') or hasRole('ADMIN')")
public List<AssessmentTask> getAssessmentTasks() {
    // 业务逻辑
}
```

### 6.2 数据完整性

#### 6.2.1 业务规则校验
- 评估分数范围检查（0-4分）
- 必填字段验证
- 数据格式校验

#### 6.2.2 并发控制
- 乐观锁防止并发修改
- 分布式锁保证数据一致性

## 7. 监控与维护

### 7.1 性能监控

#### 7.1.1 关键指标
- 查询响应时间
- 数据库连接数
- 慢查询统计
- 存储空间使用率

#### 7.1.2 监控工具
- MySQL Performance Schema
- Prometheus + Grafana
- 应用层监控

### 7.2 数据维护

#### 7.2.1 定期任务
```sql
-- 数据一致性检查
SELECT COUNT(*) FROM aea_assessment_record r
LEFT JOIN aea_assessment_task t ON r.task_id = t.id
WHERE t.id IS NULL;

-- 孤立数据清理
DELETE FROM aea_assessment_record 
WHERE task_id NOT IN (SELECT id FROM aea_assessment_task);
```

#### 7.2.2 备份策略
- 每日增量备份
- 每周全量备份
- 异地备份存储

## 8. 扩展性考虑

### 8.1 水平扩展

#### 8.1.1 分库分表策略
- 按机构ID分库
- 按时间分表
- 读写分离

#### 8.1.2 缓存策略
```java
// Redis缓存示例
@Cacheable(value = "questionnaire", key = "#id")
public Questionnaire getQuestionnaire(Long id) {
    return questionnaireMapper.selectById(id);
}
```

### 8.2 功能扩展

#### 8.2.1 多租户支持
- 增加租户ID字段
- 数据隔离机制
- 配置个性化

#### 8.2.2 国际化支持
- 多语言问卷
- 本地化报告模板
- 时区处理

## 9. 总结

### 9.1 设计优势
1. **标准合规**: 严格遵循GB/T 42195-2022国家标准
2. **架构清晰**: 模块化设计，职责分离明确
3. **扩展性强**: 无外键约束，便于后期扩展
4. **性能优化**: 合理的索引设计和查询优化
5. **安全可靠**: 完善的数据校验和权限控制

### 9.2 技术亮点
1. **业务驱动**: 基于实际评估流程设计
2. **数据完整**: 覆盖评估全生命周期
3. **灵活配置**: 支持模板化报告生成
4. **审计友好**: 完整的操作记录追踪

### 9.3 后续优化方向
1. **性能调优**: 根据实际使用情况优化查询
2. **功能增强**: 支持更多评估类型和报告格式
3. **智能化**: 引入AI辅助评估和分析
4. **移动端**: 支持移动设备的评估操作

---

**文档版本**: v1.0  
**创建日期**: 2024年12月  
**维护人员**: 系统架构师  
**更新周期**: 季度更新