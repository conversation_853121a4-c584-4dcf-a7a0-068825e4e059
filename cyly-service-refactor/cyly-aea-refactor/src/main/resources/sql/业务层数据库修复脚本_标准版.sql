-- =============================================
-- 业务层数据库修复脚本 - MySQL标准版
-- 基于cyly-aea.sql表结构，修复业务层与数据库设计不符合的问题
-- 执行前请备份数据库！
-- 兼容MySQL 5.7+ 和 MySQL 8.0+
-- =============================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;
SET sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO';

-- =============================================
-- 1. 修复评估任务表 (aea_assessment_task)
-- =============================================

-- 检查并添加任务名称字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'aea_assessment_task' 
     AND COLUMN_NAME = 'task_name') = 0,
    'ALTER TABLE `aea_assessment_task` ADD COLUMN `task_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT \'\' COMMENT \'任务名称\' AFTER `task_code`',
    'SELECT "task_name字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加任务描述字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'aea_assessment_task' 
     AND COLUMN_NAME = 'task_description') = 0,
    'ALTER TABLE `aea_assessment_task` ADD COLUMN `task_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT \'任务描述\' AFTER `task_name`',
    'SELECT "task_description字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加问卷关联字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'aea_assessment_task' 
     AND COLUMN_NAME = 'questionnaire_id') = 0,
    'ALTER TABLE `aea_assessment_task` ADD COLUMN `questionnaire_id` bigint NULL DEFAULT NULL COMMENT \'关联问卷ID\' AFTER `dept_id`',
    'SELECT "questionnaire_id字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加任务优先级字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'aea_assessment_task' 
     AND COLUMN_NAME = 'priority') = 0,
    'ALTER TABLE `aea_assessment_task` ADD COLUMN `priority` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT \'NORMAL\' COMMENT \'任务优先级：LOW-低，NORMAL-普通，HIGH-高，URGENT-紧急\' AFTER `status`',
    'SELECT "priority字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加预期完成时间字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'aea_assessment_task' 
     AND COLUMN_NAME = 'expected_completion_time') = 0,
    'ALTER TABLE `aea_assessment_task` ADD COLUMN `expected_completion_time` datetime NULL DEFAULT NULL COMMENT \'预期完成时间\' AFTER `actual_end_time`',
    'SELECT "expected_completion_time字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加实际用时字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'aea_assessment_task' 
     AND COLUMN_NAME = 'actual_duration') = 0,
    'ALTER TABLE `aea_assessment_task` ADD COLUMN `actual_duration` int NULL DEFAULT NULL COMMENT \'实际用时（分钟）\' AFTER `expected_completion_time`',
    'SELECT "actual_duration字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加评估原因字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'aea_assessment_task' 
     AND COLUMN_NAME = 'assessment_reason') = 0,
    'ALTER TABLE `aea_assessment_task` ADD COLUMN `assessment_reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT \'评估原因\' AFTER `actual_duration`',
    'SELECT "assessment_reason字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加索引（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'aea_assessment_task' 
     AND INDEX_NAME = 'idx_questionnaire_id') = 0,
    'ALTER TABLE `aea_assessment_task` ADD INDEX `idx_questionnaire_id` (`questionnaire_id`) USING BTREE',
    'SELECT "idx_questionnaire_id索引已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =============================================
-- 2. 修复评估结果表 (aea_assessment_result)
-- =============================================

-- 检查并添加结果编号字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'aea_assessment_result' 
     AND COLUMN_NAME = 'result_code') = 0,
    'ALTER TABLE `aea_assessment_result` ADD COLUMN `result_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT \'结果编号\' AFTER `elder_id`',
    'SELECT "result_code字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加评估日期字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'aea_assessment_result' 
     AND COLUMN_NAME = 'assessment_date') = 0,
    'ALTER TABLE `aea_assessment_result` ADD COLUMN `assessment_date` datetime NULL DEFAULT NULL COMMENT \'评估日期\' AFTER `result_code`',
    'SELECT "assessment_date字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加主评估员姓名字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'aea_assessment_result' 
     AND COLUMN_NAME = 'assessor_name') = 0,
    'ALTER TABLE `aea_assessment_result` ADD COLUMN `assessor_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT \'主评估员姓名\' AFTER `primary_assessor_signature`',
    'SELECT "assessor_name字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加副评估员姓名字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'aea_assessment_result' 
     AND COLUMN_NAME = 'deputy_assessor_name') = 0,
    'ALTER TABLE `aea_assessment_result` ADD COLUMN `deputy_assessor_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT \'副评估员姓名\' AFTER `secondary_assessor_signature`',
    'SELECT "deputy_assessor_name字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加信息提供者姓名字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'aea_assessment_result' 
     AND COLUMN_NAME = 'information_provider_name') = 0,
    'ALTER TABLE `aea_assessment_result` ADD COLUMN `information_provider_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT \'信息提供者姓名\' AFTER `information_provider_signature`',
    'SELECT "information_provider_name字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加评估地点字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'aea_assessment_result' 
     AND COLUMN_NAME = 'assessment_location') = 0,
    'ALTER TABLE `aea_assessment_result` ADD COLUMN `assessment_location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT \'评估地点\' AFTER `information_provider_name`',
    'SELECT "assessment_location字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =============================================
-- 4. 创建标准化国标问卷体系
-- =============================================

-- 创建国标标准问卷表（如果不存在）
CREATE TABLE IF NOT EXISTS `aea_gb_standard_questionnaire` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `questionnaire_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问卷编号',
  `questionnaire_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问卷名称',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1.0' COMMENT '版本号',
  `standard_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '国标编号',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '问卷描述',
  `total_questions` int NOT NULL DEFAULT 0 COMMENT '问题总数',
  `max_score` int NOT NULL DEFAULT 0 COMMENT '最高分数',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `is_del` tinyint NOT NULL DEFAULT 0 COMMENT '删除状态: 0-否, 1-是',
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint NOT NULL DEFAULT 1 COMMENT '创建者id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者id',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_questionnaire_code` (`questionnaire_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '国标标准问卷表' ROW_FORMAT = DYNAMIC;

-- 创建国标标准问题表（如果不存在）
CREATE TABLE IF NOT EXISTS `aea_gb_standard_question` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `questionnaire_id` bigint NOT NULL COMMENT '问卷ID',
  `question_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问题编号',
  `question_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问题内容',
  `question_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问题类型：SINGLE_CHOICE-单选，MULTIPLE_CHOICE-多选，TEXT-文本，SCORE-评分',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问题分类：SELF_CARE-自理能力，BASIC_MOBILITY-基础运动能力，MENTAL_STATE-精神状态，PERCEPTION_SOCIAL-感知觉与社会参与',
  `sub_category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '子分类',
  `sort_order` int NOT NULL DEFAULT 0 COMMENT '排序',
  `max_score` int NOT NULL DEFAULT 0 COMMENT '最高分数',
  `is_required` tinyint NOT NULL DEFAULT 1 COMMENT '是否必答：0-否，1-是',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `is_del` tinyint NOT NULL DEFAULT 0 COMMENT '删除状态: 0-否, 1-是',
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint NOT NULL DEFAULT 1 COMMENT '创建者id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者id',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_question_code` (`questionnaire_id`, `question_code`) USING BTREE,
  KEY `idx_category` (`category`) USING BTREE,
  KEY `idx_sort_order` (`sort_order`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '国标标准问题表' ROW_FORMAT = DYNAMIC;

-- 创建国标标准选项表（如果不存在）
CREATE TABLE IF NOT EXISTS `aea_gb_standard_option` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `question_id` bigint NOT NULL COMMENT '问题ID',
  `option_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '选项编号',
  `option_content` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '选项内容',
  `score` int NOT NULL DEFAULT 0 COMMENT '选项分值',
  `sort_order` int NOT NULL DEFAULT 0 COMMENT '排序',
  `is_default` tinyint NOT NULL DEFAULT 0 COMMENT '是否默认选项：0-否，1-是',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `is_del` tinyint NOT NULL DEFAULT 0 COMMENT '删除状态: 0-否, 1-是',
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint NOT NULL DEFAULT 1 COMMENT '创建者id',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者id',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_option_code` (`question_id`, `option_code`) USING BTREE,
  KEY `idx_sort_order` (`sort_order`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '国标标准选项表' ROW_FORMAT = DYNAMIC;

-- =============================================
-- 5. 初始化国标GB/T 42195-2022问卷数据
-- =============================================

-- 插入国标问卷（如果不存在）
INSERT IGNORE INTO `aea_gb_standard_questionnaire`
(`id`, `questionnaire_code`, `questionnaire_name`, `version`, `standard_code`, `description`, `total_questions`, `max_score`, `status`, `is_del`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES
(1, 'GB-T-42195-2022', '老年人能力评估规范', '1.0', 'GB/T 42195-2022', '基于国家标准GB/T 42195-2022的老年人能力评估问卷', 26, 650, 1, 0, NULL, 1, NOW(), NULL, NULL, '国标标准评估问卷');

-- 插入自理能力评估问题（8个问题）
INSERT IGNORE INTO `aea_gb_standard_question`
(`id`, `questionnaire_id`, `question_code`, `question_content`, `question_type`, `category`, `sub_category`, `sort_order`, `max_score`, `is_required`, `status`, `is_del`, `create_dept`, `create_by`, `create_time`, `update_by`, `update_time`, `remark`)
VALUES
(1, 1, 'Q001', '进食', 'SINGLE_CHOICE', 'SELF_CARE', '日常生活活动', 1, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估进食能力'),
(2, 1, 'Q002', '穿脱衣物', 'SINGLE_CHOICE', 'SELF_CARE', '日常生活活动', 2, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估穿脱衣物能力'),
(3, 1, 'Q003', '修饰', 'SINGLE_CHOICE', 'SELF_CARE', '日常生活活动', 3, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估修饰能力'),
(4, 1, 'Q004', '如厕', 'SINGLE_CHOICE', 'SELF_CARE', '日常生活活动', 4, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估如厕能力'),
(5, 1, 'Q005', '床椅转移', 'SINGLE_CHOICE', 'SELF_CARE', '日常生活活动', 5, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估床椅转移能力'),
(6, 1, 'Q006', '平地行走', 'SINGLE_CHOICE', 'SELF_CARE', '日常生活活动', 6, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估平地行走能力'),
(7, 1, 'Q007', '洗澡', 'SINGLE_CHOICE', 'SELF_CARE', '日常生活活动', 7, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估洗澡能力'),
(8, 1, 'Q008', '上下楼梯', 'SINGLE_CHOICE', 'SELF_CARE', '日常生活活动', 8, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估上下楼梯能力');

-- =============================================
-- 3. 修复评估报告表 (aea_assessment_report)
-- =============================================

-- 检查并添加报告内容字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'aea_assessment_report' 
     AND COLUMN_NAME = 'report_content') = 0,
    'ALTER TABLE `aea_assessment_report` ADD COLUMN `report_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT \'报告内容（HTML格式）\' AFTER `report_type`',
    'SELECT "report_content字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加报告摘要字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'aea_assessment_report' 
     AND COLUMN_NAME = 'report_summary') = 0,
    'ALTER TABLE `aea_assessment_report` ADD COLUMN `report_summary` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT \'报告摘要\' AFTER `report_content`',
    'SELECT "report_summary字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加下载次数字段
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'aea_assessment_report' 
     AND COLUMN_NAME = 'download_count') = 0,
    'ALTER TABLE `aea_assessment_report` ADD COLUMN `download_count` int NULL DEFAULT 0 COMMENT \'下载次数\' AFTER `error_message`',
    'SELECT "download_count字段已存在" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
