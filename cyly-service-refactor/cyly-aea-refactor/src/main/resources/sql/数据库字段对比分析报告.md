# 数据库字段对比分析报告

## 📋 概述

本报告对比分析了cyly-aea-refactor模块中的实体类与数据库表结构，发现了字段不匹配的问题，并提供了修复方案。

## 🔍 主要问题分析

### 1. 评估任务表 (aea_assessment_task)

#### 数据库表字段 vs 实体类字段对比

| 数据库字段 | 实体类字段 | 状态 | 说明 |
|-----------|-----------|------|------|
| `task_code` | `taskCode` | ✅ 匹配 | 任务编号 |
| `elder_id` | `elderId` | ✅ 匹配 | 老人ID |
| `dept_id` | ❌ 缺失 | ⚠️ 需要添加 | 机构ID |
| `assessment_type` | `assessmentType` | ⚠️ 类型不匹配 | 数据库为tinyint，实体为String |
| `primary_assessor_id` | `assessorId` | ⚠️ 字段名不匹配 | 主评估员ID |
| `secondary_assessor_id` | `deputyAssessorId` | ⚠️ 字段名不匹配 | 副评估员ID |
| `assessment_location` | `assessmentLocation` | ✅ 匹配 | 评估地点 |
| `scheduled_start_time` | ❌ 缺失 | ⚠️ 需要添加 | 计划开始时间 |
| `scheduled_end_time` | ❌ 缺失 | ⚠️ 需要添加 | 计划结束时间 |
| `actual_start_time` | `startTime` | ⚠️ 字段名不匹配 | 实际开始时间 |
| `actual_end_time` | `completedTime` | ⚠️ 字段名不匹配 | 实际结束时间 |
| `status` | `taskStatus` | ⚠️ 类型不匹配 | 数据库为tinyint，实体为String |

### 2. 评估记录表 (aea_assessment_record)

#### 数据库表字段 vs 实体类字段对比

| 数据库字段 | 实体类字段 | 状态 | 说明 |
|-----------|-----------|------|------|
| `task_id` | `taskId` | ✅ 匹配 | 任务ID |
| `questionnaire_id` | ❌ 缺失 | ⚠️ 需要添加 | 问卷ID |
| `question_id` | `questionId` | ✅ 匹配 | 问题ID |
| `option_id` | `optionId` | ✅ 匹配 | 选项ID |
| `answer_content` | `answerContent` | ✅ 匹配 | 答案内容 |
| `score` | `score` | ✅ 匹配 | 得分 |
| `assessor_id` | ❌ 缺失 | ⚠️ 需要添加 | 评估员ID |
| `assessment_time` | `answerTime` | ⚠️ 字段名不匹配 | 评估时间 |

### 3. 评估结果表 (aea_assessment_result)

#### 数据库表字段 vs 实体类字段对比

| 数据库字段 | 实体类字段 | 状态 | 说明 |
|-----------|-----------|------|------|
| `task_id` | `taskId` | ✅ 匹配 | 任务ID |
| `elder_id` | `elderId` | ✅ 匹配 | 老人ID |
| `self_care_score` | ❌ 缺失 | ⚠️ 需要添加 | 自理能力得分 |
| `basic_mobility_score` | ❌ 缺失 | ⚠️ 需要添加 | 基础运动能力得分 |
| `mental_state_score` | ❌ 缺失 | ⚠️ 需要添加 | 精神状态得分 |
| `perception_social_score` | ❌ 缺失 | ⚠️ 需要添加 | 感知觉与社会参与得分 |
| `total_score` | `totalScore` | ✅ 匹配 | 总分 |
| `preliminary_level` | ❌ 缺失 | ⚠️ 需要添加 | 初步评估等级 |
| `adjustment_reason` | ❌ 缺失 | ⚠️ 需要添加 | 等级调整原因 |
| `final_level` | `assessmentLevel` | ⚠️ 字段名不匹配 | 最终评估等级 |
| `assessment_conclusion` | `assessmentConclusion` | ✅ 匹配 | 评估结论 |
| `recommendations` | ❌ 缺失 | ⚠️ 需要添加 | 建议措施 |
| `primary_assessor_signature` | ❌ 缺失 | ⚠️ 需要添加 | 主评估员签名 |
| `secondary_assessor_signature` | ❌ 缺失 | ⚠️ 需要添加 | 副评估员签名 |
| `information_provider_signature` | ❌ 缺失 | ⚠️ 需要添加 | 信息提供者签名 |

### 4. 评估报告表 (aea_assessment_report)

#### 数据库表字段 vs 实体类字段对比

| 数据库字段 | 实体类字段 | 状态 | 说明 |
|-----------|-----------|------|------|
| `report_code` | `reportCode` | ✅ 匹配 | 报告编号 |
| `task_id` | `taskId` | ✅ 匹配 | 任务ID |
| `result_id` | ❌ 缺失 | ⚠️ 需要添加 | 评估结果ID |
| `elder_id` | ❌ 缺失 | ⚠️ 需要添加 | 老人ID |
| `report_title` | `reportTitle` | ✅ 匹配 | 报告标题 |
| `report_type` | ❌ 缺失 | ⚠️ 需要添加 | 报告类型 |
| `template_id` | `templateId` | ✅ 匹配 | 模板ID |
| `pdf_file_url` | `pdfFilePath` | ⚠️ 字段名不匹配 | PDF文件地址 |
| `pdf_file_name` | `pdfFileName` | ✅ 匹配 | PDF文件名 |
| `pdf_file_size` | `fileSize` | ⚠️ 字段名不匹配 | PDF文件大小 |
| `generation_status` | `reportStatus` | ⚠️ 字段名不匹配 | 生成状态 |
| `generation_time` | `generateTime` | ⚠️ 字段名不匹配 | 生成时间 |
| `error_message` | `failureReason` | ⚠️ 字段名不匹配 | 错误信息 |

## 🎯 修复策略

### 策略1：修改实体类以匹配数据库表（推荐）
- 优点：保持数据库表结构稳定，符合现有业务逻辑
- 缺点：需要修改较多实体类代码

### 策略2：修改数据库表以匹配实体类
- 优点：保持代码结构不变
- 缺点：需要执行数据库变更，可能影响现有数据

### 策略3：使用@Column注解映射
- 优点：代码和数据库都不需要大幅修改
- 缺点：增加了映射复杂度

## 📝 建议采用的修复方案

**推荐采用策略1 + 策略3的组合方案：**

1. 对于重要的业务字段，修改实体类以匹配数据库表
2. 对于字段名不匹配但含义相同的字段，使用@Column注解映射
3. 对于缺失的字段，在实体类中添加对应字段

## 🔧 具体修复步骤

1. **修改实体类字段**：添加缺失字段，调整字段类型
2. **添加@Column注解**：处理字段名不匹配的情况
3. **更新Bo和Vo类**：保持数据传输对象的一致性
4. **修改Service层**：调整业务逻辑以适应新的字段结构
5. **更新Mapper XML**：确保SQL映射正确

## ⚠️ 注意事项

1. **数据类型转换**：tinyint类型需要在代码中进行适当的转换处理
2. **枚举值映射**：状态字段需要建立数据库值与枚举值的映射关系
3. **向后兼容性**：确保修改不会影响现有的业务功能
4. **测试验证**：修改后需要进行充分的测试验证

## 📊 影响评估

- **影响范围**：主要影响评估模块的实体类、服务层和数据访问层
- **风险等级**：中等（需要仔细处理数据类型转换和字段映射）
- **修复时间**：预计2-3个工作日
- **测试时间**：预计1-2个工作日

## 🚀 后续优化建议

1. **建立字段映射文档**：维护数据库字段与实体类字段的映射关系
2. **代码生成工具**：考虑使用代码生成工具自动生成实体类
3. **数据库设计规范**：建立统一的数据库设计和命名规范
4. **持续集成检查**：在CI/CD流程中加入数据库表结构与实体类的一致性检查
