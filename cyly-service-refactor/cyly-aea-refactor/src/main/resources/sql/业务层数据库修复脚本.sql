-- =============================================
-- 业务层数据库修复脚本
-- 基于cyly-aea.sql表结构，修复业务层与数据库设计不符合的问题
-- 执行前请备份数据库！
-- =============================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- 1. 修复评估任务表 (aea_assessment_task)
-- =============================================

SELECT '开始修复评估任务表...' as message;

-- 添加任务名称和描述字段
ALTER TABLE `aea_assessment_task` 
ADD COLUMN IF NOT EXISTS `task_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '任务名称' AFTER `task_code`,
ADD COLUMN IF NOT EXISTS `task_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '任务描述' AFTER `task_name`;

-- 添加问卷关联字段
ALTER TABLE `aea_assessment_task` 
ADD COLUMN IF NOT EXISTS `questionnaire_id` bigint NULL DEFAULT NULL COMMENT '关联问卷ID' AFTER `dept_id`;

-- 添加任务管理字段
ALTER TABLE `aea_assessment_task` 
ADD COLUMN IF NOT EXISTS `priority` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'NORMAL' COMMENT '任务优先级：LOW-低，NORMAL-普通，HIGH-高，URGENT-紧急' AFTER `status`,
ADD COLUMN IF NOT EXISTS `expected_completion_time` datetime NULL DEFAULT NULL COMMENT '预期完成时间' AFTER `actual_end_time`,
ADD COLUMN IF NOT EXISTS `actual_duration` int NULL DEFAULT NULL COMMENT '实际用时（分钟）' AFTER `expected_completion_time`,
ADD COLUMN IF NOT EXISTS `assessment_reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评估原因' AFTER `actual_duration`;

-- 添加索引
ALTER TABLE `aea_assessment_task` 
ADD INDEX IF NOT EXISTS `idx_questionnaire_id` (`questionnaire_id`) USING BTREE,
ADD INDEX IF NOT EXISTS `idx_priority` (`priority`) USING BTREE,
ADD INDEX IF NOT EXISTS `idx_expected_time` (`expected_completion_time`) USING BTREE;

SELECT '评估任务表修复完成' as message;

-- =============================================
-- 2. 修复评估结果表 (aea_assessment_result)
-- =============================================

SELECT '开始修复评估结果表...' as message;

-- 添加结果编号和评估日期
ALTER TABLE `aea_assessment_result` 
ADD COLUMN IF NOT EXISTS `result_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '结果编号' AFTER `elder_id`,
ADD COLUMN IF NOT EXISTS `assessment_date` datetime NULL DEFAULT NULL COMMENT '评估日期' AFTER `result_code`;

-- 添加评估员姓名字段
ALTER TABLE `aea_assessment_result` 
ADD COLUMN IF NOT EXISTS `assessor_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主评估员姓名' AFTER `primary_assessor_signature`,
ADD COLUMN IF NOT EXISTS `deputy_assessor_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '副评估员姓名' AFTER `secondary_assessor_signature`,
ADD COLUMN IF NOT EXISTS `information_provider_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '信息提供者姓名' AFTER `information_provider_signature`;

-- 添加评估地点
ALTER TABLE `aea_assessment_result` 
ADD COLUMN IF NOT EXISTS `assessment_location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评估地点' AFTER `information_provider_name`;

-- 添加索引
ALTER TABLE `aea_assessment_result` 
ADD INDEX IF NOT EXISTS `idx_result_code` (`result_code`) USING BTREE,
ADD INDEX IF NOT EXISTS `idx_assessment_date` (`assessment_date`) USING BTREE;

SELECT '评估结果表修复完成' as message;

-- =============================================
-- 3. 修复评估报告表 (aea_assessment_report)
-- =============================================

SELECT '开始修复评估报告表...' as message;

-- 添加报告内容和摘要字段
ALTER TABLE `aea_assessment_report` 
ADD COLUMN IF NOT EXISTS `report_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '报告内容（HTML格式）' AFTER `report_type`,
ADD COLUMN IF NOT EXISTS `report_summary` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '报告摘要' AFTER `report_content`;

-- 添加下载统计字段
ALTER TABLE `aea_assessment_report` 
ADD COLUMN IF NOT EXISTS `download_count` int NULL DEFAULT 0 COMMENT '下载次数' AFTER `error_message`;

SELECT '评估报告表修复完成' as message;

-- =============================================
-- 4. 创建标准化国标问卷体系
-- =============================================

SELECT '开始创建标准化国标问卷体系...' as message;

-- 创建国标标准问卷表
DROP TABLE IF EXISTS `aea_gb_standard_questionnaire`;
CREATE TABLE `aea_gb_standard_questionnaire` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `questionnaire_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问卷编号',
  `questionnaire_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问卷名称',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1.0' COMMENT '版本号',
  `standard_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '国标编号',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '问卷描述',
  `total_questions` int NOT NULL DEFAULT 0 COMMENT '问题总数',
  `max_score` int NOT NULL DEFAULT 0 COMMENT '最高分数',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `is_del` tinyint NOT NULL DEFAULT 0 COMMENT '删除状态: 0-否, 1-是',
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint NOT NULL COMMENT '创建者id',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者id',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_questionnaire_code` (`questionnaire_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '国标标准问卷表' ROW_FORMAT = DYNAMIC;

-- 创建国标标准问题表
DROP TABLE IF EXISTS `aea_gb_standard_question`;
CREATE TABLE `aea_gb_standard_question` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `questionnaire_id` bigint NOT NULL COMMENT '问卷ID',
  `question_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问题编号',
  `question_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问题内容',
  `question_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问题类型：SINGLE_CHOICE-单选，MULTIPLE_CHOICE-多选，TEXT-文本，SCORE-评分',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问题分类：SELF_CARE-自理能力，BASIC_MOBILITY-基础运动能力，MENTAL_STATE-精神状态，PERCEPTION_SOCIAL-感知觉与社会参与',
  `sub_category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '子分类',
  `sort_order` int NOT NULL DEFAULT 0 COMMENT '排序',
  `max_score` int NOT NULL DEFAULT 0 COMMENT '最高分数',
  `is_required` tinyint NOT NULL DEFAULT 1 COMMENT '是否必答：0-否，1-是',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `is_del` tinyint NOT NULL DEFAULT 0 COMMENT '删除状态: 0-否, 1-是',
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint NOT NULL COMMENT '创建者id',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者id',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_question_code` (`questionnaire_id`, `question_code`) USING BTREE,
  KEY `idx_category` (`category`) USING BTREE,
  KEY `idx_sort_order` (`sort_order`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '国标标准问题表' ROW_FORMAT = DYNAMIC;

-- 创建国标标准选项表
DROP TABLE IF EXISTS `aea_gb_standard_option`;
CREATE TABLE `aea_gb_standard_option` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `question_id` bigint NOT NULL COMMENT '问题ID',
  `option_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '选项编号',
  `option_content` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '选项内容',
  `score` int NOT NULL DEFAULT 0 COMMENT '选项分值',
  `sort_order` int NOT NULL DEFAULT 0 COMMENT '排序',
  `is_default` tinyint NOT NULL DEFAULT 0 COMMENT '是否默认选项：0-否，1-是',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  `is_del` tinyint NOT NULL DEFAULT 0 COMMENT '删除状态: 0-否, 1-是',
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint NOT NULL COMMENT '创建者id',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者id',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_option_code` (`question_id`, `option_code`) USING BTREE,
  KEY `idx_sort_order` (`sort_order`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '国标标准选项表' ROW_FORMAT = DYNAMIC;

SELECT '标准化国标问卷体系创建完成' as message;

-- =============================================
-- 5. 初始化国标GB/T 42195-2022问卷数据
-- =============================================

SELECT '开始初始化国标问卷数据...' as message;

-- 插入国标问卷
INSERT INTO `aea_gb_standard_questionnaire` VALUES 
(1, 'GB-T-42195-2022', '老年人能力评估规范', '1.0', 'GB/T 42195-2022', '基于国家标准GB/T 42195-2022的老年人能力评估问卷', 26, 100, 1, 0, NULL, 1, NOW(), NULL, NULL, '国标标准评估问卷');

-- 插入自理能力评估问题（8个问题）
INSERT INTO `aea_gb_standard_question` VALUES 
(1, 1, 'Q001', '进食', 'SINGLE_CHOICE', 'SELF_CARE', '日常生活活动', 1, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估进食能力'),
(2, 1, 'Q002', '穿脱衣物', 'SINGLE_CHOICE', 'SELF_CARE', '日常生活活动', 2, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估穿脱衣物能力'),
(3, 1, 'Q003', '修饰', 'SINGLE_CHOICE', 'SELF_CARE', '日常生活活动', 3, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估修饰能力'),
(4, 1, 'Q004', '如厕', 'SINGLE_CHOICE', 'SELF_CARE', '日常生活活动', 4, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估如厕能力'),
(5, 1, 'Q005', '床椅转移', 'SINGLE_CHOICE', 'SELF_CARE', '日常生活活动', 5, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估床椅转移能力'),
(6, 1, 'Q006', '平地行走', 'SINGLE_CHOICE', 'SELF_CARE', '日常生活活动', 6, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估平地行走能力'),
(7, 1, 'Q007', '洗澡', 'SINGLE_CHOICE', 'SELF_CARE', '日常生活活动', 7, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估洗澡能力'),
(8, 1, 'Q008', '上下楼梯', 'SINGLE_CHOICE', 'SELF_CARE', '日常生活活动', 8, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估上下楼梯能力');

-- 插入基础运动能力评估问题（4个问题）
INSERT INTO `aea_gb_standard_question` VALUES 
(9, 1, 'Q009', '平衡', 'SINGLE_CHOICE', 'BASIC_MOBILITY', '基础运动能力', 9, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估平衡能力'),
(10, 1, 'Q010', '移动', 'SINGLE_CHOICE', 'BASIC_MOBILITY', '基础运动能力', 10, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估移动能力'),
(11, 1, 'Q011', '肌力', 'SINGLE_CHOICE', 'BASIC_MOBILITY', '基础运动能力', 11, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估肌力'),
(12, 1, 'Q012', '关节活动度', 'SINGLE_CHOICE', 'BASIC_MOBILITY', '基础运动能力', 12, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估关节活动度');

-- 插入精神状态评估问题（7个问题）
INSERT INTO `aea_gb_standard_question` VALUES 
(13, 1, 'Q013', '记忆', 'SINGLE_CHOICE', 'MENTAL_STATE', '精神状态', 13, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估记忆能力'),
(14, 1, 'Q014', '理解能力', 'SINGLE_CHOICE', 'MENTAL_STATE', '精神状态', 14, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估理解能力'),
(15, 1, 'Q015', '判断能力', 'SINGLE_CHOICE', 'MENTAL_STATE', '精神状态', 15, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估判断能力'),
(16, 1, 'Q016', '情感状态', 'SINGLE_CHOICE', 'MENTAL_STATE', '精神状态', 16, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估情感状态'),
(17, 1, 'Q017', '行为症状', 'SINGLE_CHOICE', 'MENTAL_STATE', '精神状态', 17, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估行为症状'),
(18, 1, 'Q018', '沟通交流能力', 'SINGLE_CHOICE', 'MENTAL_STATE', '精神状态', 18, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估沟通交流能力'),
(19, 1, 'Q019', '社会功能', 'SINGLE_CHOICE', 'MENTAL_STATE', '精神状态', 19, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估社会功能');

-- 插入感知觉与社会参与评估问题（7个问题）
INSERT INTO `aea_gb_standard_question` VALUES 
(20, 1, 'Q020', '视力', 'SINGLE_CHOICE', 'PERCEPTION_SOCIAL', '感知觉与社会参与', 20, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估视力'),
(21, 1, 'Q021', '听力', 'SINGLE_CHOICE', 'PERCEPTION_SOCIAL', '感知觉与社会参与', 21, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估听力'),
(22, 1, 'Q022', '社会交往能力', 'SINGLE_CHOICE', 'PERCEPTION_SOCIAL', '感知觉与社会参与', 22, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估社会交往能力'),
(23, 1, 'Q023', '户外活动能力', 'SINGLE_CHOICE', 'PERCEPTION_SOCIAL', '感知觉与社会参与', 23, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估户外活动能力'),
(24, 1, 'Q024', '业余活动', 'SINGLE_CHOICE', 'PERCEPTION_SOCIAL', '感知觉与社会参与', 24, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估业余活动'),
(25, 1, 'Q025', '时间定向力', 'SINGLE_CHOICE', 'PERCEPTION_SOCIAL', '感知觉与社会参与', 25, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估时间定向力'),
(26, 1, 'Q026', '空间定向力', 'SINGLE_CHOICE', 'PERCEPTION_SOCIAL', '感知觉与社会参与', 26, 25, 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '评估空间定向力');

-- 插入标准选项数据（每个问题4个选项，分别对应25分、15分、5分、0分）
INSERT INTO `aea_gb_standard_option` VALUES
-- 进食选项
(1, 1, 'A', '能独立进食', 25, 1, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '完全独立'),
(2, 1, 'B', '需要部分帮助', 15, 2, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '需要少量帮助'),
(3, 1, 'C', '需要大量帮助', 5, 3, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '需要较多帮助'),
(4, 1, 'D', '完全依赖他人', 0, 4, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '完全依赖'),

-- 穿脱衣物选项
(5, 2, 'A', '能独立穿脱衣物', 25, 1, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '完全独立'),
(6, 2, 'B', '需要部分帮助', 15, 2, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '需要少量帮助'),
(7, 2, 'C', '需要大量帮助', 5, 3, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '需要较多帮助'),
(8, 2, 'D', '完全依赖他人', 0, 4, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '完全依赖'),

-- 修饰选项
(9, 3, 'A', '能独立修饰', 25, 1, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '完全独立'),
(10, 3, 'B', '需要部分帮助', 15, 2, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '需要少量帮助'),
(11, 3, 'C', '需要大量帮助', 5, 3, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '需要较多帮助'),
(12, 3, 'D', '完全依赖他人', 0, 4, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '完全依赖'),

-- 如厕选项
(13, 4, 'A', '能独立如厕', 25, 1, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '完全独立'),
(14, 4, 'B', '需要部分帮助', 15, 2, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '需要少量帮助'),
(15, 4, 'C', '需要大量帮助', 5, 3, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '需要较多帮助'),
(16, 4, 'D', '完全依赖他人', 0, 4, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '完全依赖'),

-- 床椅转移选项
(17, 5, 'A', '能独立转移', 25, 1, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '完全独立'),
(18, 5, 'B', '需要部分帮助', 15, 2, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '需要少量帮助'),
(19, 5, 'C', '需要大量帮助', 5, 3, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '需要较多帮助'),
(20, 5, 'D', '完全依赖他人', 0, 4, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '完全依赖'),

-- 平地行走选项
(21, 6, 'A', '能独立行走', 25, 1, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '完全独立'),
(22, 6, 'B', '需要辅助器具', 15, 2, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '需要辅助器具'),
(23, 6, 'C', '需要人员帮助', 5, 3, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '需要人员帮助'),
(24, 6, 'D', '无法行走', 0, 4, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '无法行走'),

-- 洗澡选项
(25, 7, 'A', '能独立洗澡', 25, 1, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '完全独立'),
(26, 7, 'B', '需要部分帮助', 15, 2, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '需要少量帮助'),
(27, 7, 'C', '需要大量帮助', 5, 3, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '需要较多帮助'),
(28, 7, 'D', '完全依赖他人', 0, 4, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '完全依赖'),

-- 上下楼梯选项
(29, 8, 'A', '能独立上下楼梯', 25, 1, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '完全独立'),
(30, 8, 'B', '需要扶手或帮助', 15, 2, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '需要扶手或帮助'),
(31, 8, 'C', '需要大量帮助', 5, 3, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '需要较多帮助'),
(32, 8, 'D', '无法上下楼梯', 0, 4, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '无法上下楼梯');

-- 基础运动能力选项（问题9-12）
INSERT INTO `aea_gb_standard_option` VALUES
-- 平衡选项
(33, 9, 'A', '平衡能力良好', 25, 1, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '平衡能力良好'),
(34, 9, 'B', '轻度平衡障碍', 15, 2, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '轻度平衡障碍'),
(35, 9, 'C', '中度平衡障碍', 5, 3, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '中度平衡障碍'),
(36, 9, 'D', '重度平衡障碍', 0, 4, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '重度平衡障碍'),

-- 移动选项
(37, 10, 'A', '移动能力良好', 25, 1, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '移动能力良好'),
(38, 10, 'B', '轻度移动障碍', 15, 2, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '轻度移动障碍'),
(39, 10, 'C', '中度移动障碍', 5, 3, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '中度移动障碍'),
(40, 10, 'D', '重度移动障碍', 0, 4, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '重度移动障碍'),

-- 肌力选项
(41, 11, 'A', '肌力正常', 25, 1, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '肌力正常'),
(42, 11, 'B', '轻度肌力下降', 15, 2, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '轻度肌力下降'),
(43, 11, 'C', '中度肌力下降', 5, 3, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '中度肌力下降'),
(44, 11, 'D', '重度肌力下降', 0, 4, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '重度肌力下降'),

-- 关节活动度选项
(45, 12, 'A', '关节活动度正常', 25, 1, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '关节活动度正常'),
(46, 12, 'B', '轻度关节活动受限', 15, 2, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '轻度关节活动受限'),
(47, 12, 'C', '中度关节活动受限', 5, 3, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '中度关节活动受限'),
(48, 12, 'D', '重度关节活动受限', 0, 4, 0, 1, 0, NULL, 1, NOW(), NULL, NULL, '重度关节活动受限');

SELECT '国标问卷数据初始化完成' as message;

-- =============================================
-- 6. 清理重复表结构
-- =============================================

SELECT '开始清理重复表结构...' as message;

-- 备份aea_evaluate_report表数据（如果需要）
-- CREATE TABLE aea_evaluate_report_backup AS SELECT * FROM aea_evaluate_report;

-- 删除重复的评估报告表
DROP TABLE IF EXISTS `aea_evaluate_report`;

SELECT '重复表结构清理完成' as message;

-- =============================================
-- 7. 更新现有数据
-- =============================================

SELECT '开始更新现有数据...' as message;

-- 为现有评估结果生成结果编号
UPDATE `aea_assessment_result` 
SET `result_code` = CONCAT('AER', DATE_FORMAT(COALESCE(create_time, NOW()), '%Y%m%d'), LPAD(id, 4, '0'))
WHERE `result_code` IS NULL OR `result_code` = '';

-- 为现有评估结果设置评估日期
UPDATE `aea_assessment_result` r
INNER JOIN `aea_assessment_task` t ON r.task_id = t.id
SET r.assessment_date = COALESCE(t.actual_start_time, t.scheduled_start_time, t.create_time)
WHERE r.assessment_date IS NULL;

-- 为现有评估任务设置默认问卷
UPDATE `aea_assessment_task` 
SET `questionnaire_id` = 1
WHERE `questionnaire_id` IS NULL;

SELECT '现有数据更新完成' as message;

SET FOREIGN_KEY_CHECKS = 1;

-- =============================================
-- 执行完成提示
-- =============================================
SELECT '==================================' as message;
SELECT '业务层数据库修复脚本执行完成！' as message;
SELECT '修复内容包括：' as message;
SELECT '1. 评估任务表字段补全' as message;
SELECT '2. 评估结果表字段补全' as message;
SELECT '3. 评估报告表字段补全' as message;
SELECT '4. 标准化国标问卷体系创建' as message;
SELECT '5. 重复表结构清理' as message;
SELECT '6. 现有数据更新' as message;
SELECT '==================================' as message;
