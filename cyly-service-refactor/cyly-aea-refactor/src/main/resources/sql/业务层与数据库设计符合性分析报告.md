# 业务层与数据库设计符合性分析报告

## 📋 概述

本报告基于cyly-aea.sql数据库表结构，分析业务层实现与数据库设计的符合性，识别不符合的地方并提供修复方案。

## 🔍 主要发现的问题

### 1. 评估任务表 (aea_assessment_task) 业务不符合问题

#### 问题1：缺少任务名称和描述字段
**数据库现状**：只有 `task_code` 任务编号
**业务需求**：需要 `task_name` 任务名称和 `task_description` 任务描述
**影响**：无法为评估任务提供可读的名称和详细描述

#### 问题2：缺少问卷关联
**数据库现状**：没有 `questionnaire_id` 字段
**业务需求**：需要关联具体的评估问卷
**影响**：无法确定使用哪个问卷进行评估

#### 问题3：缺少任务优先级和预期时间
**数据库现状**：没有优先级和预期完成时间字段
**业务需求**：需要 `priority` 和 `expected_completion_time` 字段
**影响**：无法进行任务优先级管理和时间规划

### 2. 评估结果表 (aea_assessment_result) 业务不符合问题

#### 问题1：缺少结果编号和评估日期
**数据库现状**：没有 `result_code` 和 `assessment_date` 字段
**业务需求**：需要唯一的结果编号和明确的评估日期
**影响**：无法进行结果追溯和日期管理

#### 问题2：缺少评估员信息
**数据库现状**：只有签名图片，没有评估员姓名字段
**业务需求**：需要 `assessor_name`, `deputy_assessor_name`, `information_provider_name` 字段
**影响**：PDF报告中无法显示评估员姓名

#### 问题3：缺少评估地点
**数据库现状**：没有 `assessment_location` 字段
**业务需求**：需要记录评估地点信息
**影响**：PDF报告中无法显示评估地点

### 3. 评估报告表 (aea_assessment_report) 业务不符合问题

#### 问题1：缺少报告内容和摘要
**数据库现状**：只有PDF文件路径，没有报告内容字段
**业务需求**：需要 `report_content` 和 `report_summary` 字段
**影响**：无法存储和检索报告的文本内容

#### 问题2：缺少下载统计
**数据库现状**：没有 `download_count` 字段
**业务需求**：需要统计报告下载次数
**影响**：无法进行报告使用情况分析

### 4. 国标评估问卷体系不完整

#### 问题1：缺少标准化问卷表
**数据库现状**：有 `aea_gb_evaluate_base` 和 `aea_gb_evaluate_base_info` 但结构不标准
**业务需求**：需要标准化的问卷、问题、选项表结构
**影响**：无法支持标准化的国标评估流程

#### 问题2：缺少问题分类和评分规则
**数据库现状**：没有明确的四个一级指标分类
**业务需求**：需要按照国标四个维度进行问题分类
**影响**：无法正确计算各维度得分

### 5. 重复表结构问题

#### 问题1：存在重复的评估报告表
**数据库现状**：同时存在 `aea_assessment_report` 和 `aea_evaluate_report`
**业务需求**：应该统一使用一个表
**影响**：造成数据冗余和维护困难

## 🎯 业务目标与数据库设计对比

### 业务目标
1. **完整的评估流程管理**：从任务创建到报告生成的全流程
2. **国标GB/T 42195-2022支持**：完整支持国标评估体系
3. **PDF报告生成**：生成标准化的评估报告
4. **数据追溯和统计**：支持评估数据的追溯和统计分析

### 数据库设计现状
1. **基础表结构完整**：核心表已存在
2. **字段不完整**：缺少关键业务字段
3. **国标支持不标准**：问卷体系需要重构
4. **数据冗余**：存在重复表结构

## 🔧 修复方案

### 方案1：数据库表结构修复（推荐）

#### 1.1 修复评估任务表
```sql
ALTER TABLE `aea_assessment_task` 
ADD COLUMN `task_name` varchar(200) NOT NULL COMMENT '任务名称' AFTER `task_code`,
ADD COLUMN `task_description` text COMMENT '任务描述' AFTER `task_name`,
ADD COLUMN `questionnaire_id` bigint COMMENT '关联问卷ID' AFTER `dept_id`,
ADD COLUMN `priority` varchar(20) DEFAULT 'NORMAL' COMMENT '任务优先级' AFTER `status`,
ADD COLUMN `expected_completion_time` datetime COMMENT '预期完成时间' AFTER `actual_end_time`,
ADD COLUMN `actual_duration` int COMMENT '实际用时（分钟）' AFTER `expected_completion_time`,
ADD COLUMN `assessment_reason` varchar(200) COMMENT '评估原因' AFTER `actual_duration`;
```

#### 1.2 修复评估结果表
```sql
ALTER TABLE `aea_assessment_result` 
ADD COLUMN `result_code` varchar(50) COMMENT '结果编号' AFTER `elder_id`,
ADD COLUMN `assessment_date` datetime COMMENT '评估日期' AFTER `result_code`,
ADD COLUMN `assessor_name` varchar(50) COMMENT '主评估员姓名' AFTER `primary_assessor_signature`,
ADD COLUMN `deputy_assessor_name` varchar(50) COMMENT '副评估员姓名' AFTER `secondary_assessor_signature`,
ADD COLUMN `information_provider_name` varchar(50) COMMENT '信息提供者姓名' AFTER `information_provider_signature`,
ADD COLUMN `assessment_location` varchar(200) COMMENT '评估地点' AFTER `information_provider_name`;
```

#### 1.3 修复评估报告表
```sql
ALTER TABLE `aea_assessment_report` 
ADD COLUMN `report_content` longtext COMMENT '报告内容（HTML格式）' AFTER `report_type`,
ADD COLUMN `report_summary` text COMMENT '报告摘要' AFTER `report_content`,
ADD COLUMN `download_count` int DEFAULT 0 COMMENT '下载次数' AFTER `error_message`;
```

### 方案2：创建标准化国标问卷体系

#### 2.1 创建标准问卷表
```sql
CREATE TABLE `aea_gb_standard_questionnaire` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `questionnaire_code` varchar(50) NOT NULL COMMENT '问卷编号',
  `questionnaire_name` varchar(200) NOT NULL COMMENT '问卷名称',
  `version` varchar(20) NOT NULL COMMENT '版本号',
  `standard_code` varchar(50) COMMENT '国标编号',
  `description` text COMMENT '问卷描述',
  `status` tinyint DEFAULT 1 COMMENT '状态：0-禁用，1-启用',
  PRIMARY KEY (`id`)
) COMMENT = '国标标准问卷表';
```

#### 2.2 创建标准问题表
```sql
CREATE TABLE `aea_gb_standard_question` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `questionnaire_id` bigint NOT NULL COMMENT '问卷ID',
  `question_code` varchar(50) NOT NULL COMMENT '问题编号',
  `question_content` text NOT NULL COMMENT '问题内容',
  `question_type` varchar(20) NOT NULL COMMENT '问题类型',
  `category` varchar(50) NOT NULL COMMENT '问题分类',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `is_required` tinyint DEFAULT 1 COMMENT '是否必答',
  PRIMARY KEY (`id`)
) COMMENT = '国标标准问题表';
```

#### 2.3 创建标准选项表
```sql
CREATE TABLE `aea_gb_standard_option` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `question_id` bigint NOT NULL COMMENT '问题ID',
  `option_code` varchar(50) NOT NULL COMMENT '选项编号',
  `option_content` varchar(500) NOT NULL COMMENT '选项内容',
  `score` int NOT NULL DEFAULT 0 COMMENT '选项分值',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  PRIMARY KEY (`id`)
) COMMENT = '国标标准选项表';
```

### 方案3：清理重复表结构

#### 3.1 删除重复的评估报告表
```sql
-- 迁移数据（如果有）
INSERT INTO aea_assessment_report (...)
SELECT ... FROM aea_evaluate_report WHERE ...;

-- 删除重复表
DROP TABLE IF EXISTS `aea_evaluate_report`;
```

## 📊 修复优先级

### 高优先级（立即修复）
1. **评估任务表字段补全**：影响核心业务流程
2. **评估结果表字段补全**：影响PDF报告生成
3. **清理重复表结构**：避免数据混乱

### 中优先级（近期修复）
1. **评估报告表字段补全**：增强报告功能
2. **创建标准化问卷体系**：支持国标评估

### 低优先级（后期优化）
1. **数据迁移和清理**：优化数据质量
2. **索引优化**：提升查询性能

## 🚀 实施建议

### 阶段1：紧急修复（1-2天）
1. 执行数据库字段修复脚本
2. 更新实体类和业务逻辑
3. 进行基础功能测试

### 阶段2：标准化改造（3-5天）
1. 创建标准化问卷体系
2. 实现国标评估逻辑
3. 完善PDF报告生成

### 阶段3：优化完善（1-2天）
1. 清理重复数据
2. 优化查询性能
3. 完善测试用例

## ⚠️ 风险评估

### 数据风险
- **中等风险**：字段添加不会影响现有数据
- **低风险**：新增表不会影响现有业务

### 业务风险
- **低风险**：修复主要是功能增强
- **中等风险**：需要充分测试确保兼容性

### 技术风险
- **低风险**：修改主要是字段添加
- **中等风险**：需要同步更新代码逻辑

## 📝 验收标准

### 功能验收
1. ✅ 评估任务可以创建并包含完整信息
2. ✅ 评估流程可以正常执行
3. ✅ PDF报告可以正常生成
4. ✅ 国标评估逻辑正确

### 性能验收
1. ✅ 查询响应时间 < 500ms
2. ✅ 报告生成时间 < 10s
3. ✅ 并发支持 > 100用户

### 数据验收
1. ✅ 数据完整性保持
2. ✅ 字段映射正确
3. ✅ 约束条件有效
