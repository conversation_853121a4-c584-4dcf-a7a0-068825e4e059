-- =============================================
-- 数据库字段修复脚本
-- 用于修复cyly-aea-refactor模块中数据库表与实体类不匹配的问题
-- 执行前请备份数据库！
-- =============================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =============================================
-- 1. 修复评估任务表 (aea_assessment_task)
-- =============================================

-- 检查表是否存在
SELECT 'aea_assessment_task表修复开始' as message;

-- 添加缺失字段（如果不存在）
ALTER TABLE `aea_assessment_task` 
ADD COLUMN IF NOT EXISTS `task_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '任务名称' AFTER `task_code`,
ADD COLUMN IF NOT EXISTS `task_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '任务描述' AFTER `task_name`,
ADD COLUMN IF NOT EXISTS `questionnaire_id` bigint NULL DEFAULT NULL COMMENT '问卷ID' AFTER `dept_id`,
ADD COLUMN IF NOT EXISTS `assessment_reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评估原因' AFTER `assessment_location`,
ADD COLUMN IF NOT EXISTS `priority` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'NORMAL' COMMENT '任务优先级：LOW-低，NORMAL-普通，HIGH-高，URGENT-紧急' AFTER `status`,
ADD COLUMN IF NOT EXISTS `expected_completion_time` datetime NULL DEFAULT NULL COMMENT '预计完成时间' AFTER `actual_end_time`,
ADD COLUMN IF NOT EXISTS `actual_duration` int NULL DEFAULT NULL COMMENT '实际用时（分钟）' AFTER `expected_completion_time`;

-- 修改字段注释和类型
ALTER TABLE `aea_assessment_task` 
MODIFY COLUMN `assessment_type` tinyint NOT NULL DEFAULT 1 COMMENT '评估类型: 1-首次评估, 2-常规评估, 3-即时评估, 4-复评, 5-退出评估',
MODIFY COLUMN `status` tinyint NOT NULL DEFAULT 0 COMMENT '任务状态: 0-待开始, 1-进行中, 2-已完成, 3-已取消, 4-已暂停';

SELECT 'aea_assessment_task表修复完成' as message;

-- =============================================
-- 2. 修复评估记录表 (aea_assessment_record)
-- =============================================

SELECT 'aea_assessment_record表修复开始' as message;

-- 添加缺失字段
ALTER TABLE `aea_assessment_record` 
ADD COLUMN IF NOT EXISTS `answer_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'ANSWERED' COMMENT '答题状态：UNANSWERED-未答题，ANSWERED-已答题，SKIPPED-跳过' AFTER `assessment_time`,
ADD COLUMN IF NOT EXISTS `question_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '问题类型：SINGLE_CHOICE-单选，MULTIPLE_CHOICE-多选，TEXT-文本，SCORE-评分' AFTER `answer_status`,
ADD COLUMN IF NOT EXISTS `question_category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '问题分类：SELF_CARE-自理能力，BASIC_MOBILITY-基础运动能力，MENTAL_STATE-精神状态，PERCEPTION_SOCIAL-感知觉与社会参与' AFTER `question_type`,
ADD COLUMN IF NOT EXISTS `is_required` tinyint NULL DEFAULT 1 COMMENT '是否必答：0-否，1-是' AFTER `question_category`,
ADD COLUMN IF NOT EXISTS `answer_remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '答案备注' AFTER `is_required`;

SELECT 'aea_assessment_record表修复完成' as message;

-- =============================================
-- 3. 修复评估结果表 (aea_assessment_result)
-- =============================================

SELECT 'aea_assessment_result表修复开始' as message;

-- 添加缺失字段
ALTER TABLE `aea_assessment_result` 
ADD COLUMN IF NOT EXISTS `result_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '结果编号' AFTER `elder_id`,
ADD COLUMN IF NOT EXISTS `assessment_date` datetime NULL DEFAULT NULL COMMENT '评估日期' AFTER `result_code`,
ADD COLUMN IF NOT EXISTS `assessor_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主评估员姓名' AFTER `primary_assessor_signature`,
ADD COLUMN IF NOT EXISTS `deputy_assessor_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '副评估员姓名' AFTER `secondary_assessor_signature`,
ADD COLUMN IF NOT EXISTS `information_provider_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '信息提供者姓名' AFTER `information_provider_signature`,
ADD COLUMN IF NOT EXISTS `assessment_location` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评估地点' AFTER `information_provider_name`;

-- 添加索引
ALTER TABLE `aea_assessment_result` 
ADD INDEX IF NOT EXISTS `idx_result_code` (`result_code`) USING BTREE,
ADD INDEX IF NOT EXISTS `idx_elder_id` (`elder_id`) USING BTREE,
ADD INDEX IF NOT EXISTS `idx_assessment_date` (`assessment_date`) USING BTREE;

SELECT 'aea_assessment_result表修复完成' as message;

-- =============================================
-- 4. 修复评估报告表 (aea_assessment_report)
-- =============================================

SELECT 'aea_assessment_report表修复开始' as message;

-- 添加缺失字段
ALTER TABLE `aea_assessment_report` 
ADD COLUMN IF NOT EXISTS `download_count` int NULL DEFAULT 0 COMMENT '下载次数' AFTER `error_message`,
ADD COLUMN IF NOT EXISTS `report_content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '报告内容（HTML格式）' AFTER `report_type`,
ADD COLUMN IF NOT EXISTS `report_summary` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '报告摘要' AFTER `report_content`;

-- 修改字段类型和注释
ALTER TABLE `aea_assessment_report` 
MODIFY COLUMN `generation_status` tinyint NOT NULL DEFAULT 0 COMMENT '生成状态: 0-待生成, 1-生成中, 2-生成成功, 3-生成失败',
MODIFY COLUMN `report_type` tinyint NOT NULL DEFAULT 1 COMMENT '报告类型: 1-标准报告, 2-简化报告, 3-详细报告';

-- 添加索引
ALTER TABLE `aea_assessment_report` 
ADD INDEX IF NOT EXISTS `idx_elder_id` (`elder_id`) USING BTREE,
ADD INDEX IF NOT EXISTS `idx_generation_status` (`generation_status`) USING BTREE,
ADD INDEX IF NOT EXISTS `idx_generation_time` (`generation_time`) USING BTREE;

SELECT 'aea_assessment_report表修复完成' as message;

-- =============================================
-- 5. 创建数据字典表（用于枚举值映射）
-- =============================================

-- 创建数据字典表
DROP TABLE IF EXISTS `aea_data_dictionary`;
CREATE TABLE `aea_data_dictionary` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `dict_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典类型',
  `dict_key` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典键',
  `dict_value` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典值',
  `dict_label` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典标签',
  `sort_order` int NOT NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态: 0-禁用, 1-启用',
  `is_del` tinyint NOT NULL DEFAULT 0 COMMENT '删除状态: 0-否, 1-是',
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint NOT NULL COMMENT '创建者id',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者id',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_dict_type_key` (`dict_type`, `dict_key`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '评估数据字典表' ROW_FORMAT = Dynamic;

-- 插入数据字典数据
INSERT INTO `aea_data_dictionary` VALUES 
-- 评估类型
(1, 'assessment_type', '1', 'INITIAL', '首次评估', 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '首次评估'),
(2, 'assessment_type', '2', 'REVIEW', '常规评估', 2, 1, 0, NULL, 1, NOW(), NULL, NULL, '常规评估'),
(3, 'assessment_type', '3', 'EMERGENCY', '即时评估', 3, 1, 0, NULL, 1, NOW(), NULL, NULL, '即时评估'),
(4, 'assessment_type', '4', 'RECHECK', '复评', 4, 1, 0, NULL, 1, NOW(), NULL, NULL, '复评'),
(5, 'assessment_type', '5', 'EXIT', '退出评估', 5, 1, 0, NULL, 1, NOW(), NULL, NULL, '退出评估'),

-- 任务状态
(6, 'task_status', '0', 'PENDING', '待开始', 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '待开始'),
(7, 'task_status', '1', 'IN_PROGRESS', '进行中', 2, 1, 0, NULL, 1, NOW(), NULL, NULL, '进行中'),
(8, 'task_status', '2', 'COMPLETED', '已完成', 3, 1, 0, NULL, 1, NOW(), NULL, NULL, '已完成'),
(9, 'task_status', '3', 'CANCELLED', '已取消', 4, 1, 0, NULL, 1, NOW(), NULL, NULL, '已取消'),
(10, 'task_status', '4', 'PAUSED', '已暂停', 5, 1, 0, NULL, 1, NOW(), NULL, NULL, '已暂停'),

-- 任务优先级
(11, 'task_priority', 'LOW', 'LOW', '低', 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '低优先级'),
(12, 'task_priority', 'NORMAL', 'NORMAL', '普通', 2, 1, 0, NULL, 1, NOW(), NULL, NULL, '普通优先级'),
(13, 'task_priority', 'HIGH', 'HIGH', '高', 3, 1, 0, NULL, 1, NOW(), NULL, NULL, '高优先级'),
(14, 'task_priority', 'URGENT', 'URGENT', '紧急', 4, 1, 0, NULL, 1, NOW(), NULL, NULL, '紧急优先级'),

-- 评估等级
(15, 'assessment_level', '1', 'EXCELLENT', '能力完好', 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '能力完好'),
(16, 'assessment_level', '2', 'MILD_IMPAIRMENT', '轻度受损', 2, 1, 0, NULL, 1, NOW(), NULL, NULL, '轻度受损'),
(17, 'assessment_level', '3', 'MODERATE_IMPAIRMENT', '中度受损', 3, 1, 0, NULL, 1, NOW(), NULL, NULL, '中度受损'),
(18, 'assessment_level', '4', 'SEVERE_IMPAIRMENT', '重度受损', 4, 1, 0, NULL, 1, NOW(), NULL, NULL, '重度受损'),
(19, 'assessment_level', '5', 'COMPLETE_LOSS', '完全丧失', 5, 1, 0, NULL, 1, NOW(), NULL, NULL, '完全丧失'),

-- 报告生成状态
(20, 'generation_status', '0', 'PENDING', '待生成', 1, 1, 0, NULL, 1, NOW(), NULL, NULL, '待生成'),
(21, 'generation_status', '1', 'GENERATING', '生成中', 2, 1, 0, NULL, 1, NOW(), NULL, NULL, '生成中'),
(22, 'generation_status', '2', 'SUCCESS', '生成成功', 3, 1, 0, NULL, 1, NOW(), NULL, NULL, '生成成功'),
(23, 'generation_status', '3', 'FAILED', '生成失败', 4, 1, 0, NULL, 1, NOW(), NULL, NULL, '生成失败');

SELECT 'aea_data_dictionary表创建完成' as message;

-- =============================================
-- 6. 创建视图（用于简化查询）
-- =============================================

-- 创建评估任务详情视图
DROP VIEW IF EXISTS `v_aea_assessment_task_detail`;
CREATE VIEW `v_aea_assessment_task_detail` AS
SELECT 
    t.id,
    t.task_code,
    t.task_name,
    t.task_description,
    t.elder_id,
    e.name as elder_name,
    t.dept_id,
    t.assessment_type,
    d1.dict_label as assessment_type_name,
    t.primary_assessor_id,
    t.secondary_assessor_id,
    t.assessment_location,
    t.scheduled_start_time,
    t.scheduled_end_time,
    t.actual_start_time,
    t.actual_end_time,
    t.status,
    d2.dict_label as status_name,
    t.priority,
    t.expected_completion_time,
    t.actual_duration,
    t.create_time,
    t.update_time,
    t.remark
FROM aea_assessment_task t
LEFT JOIN aea_elder e ON t.elder_id = e.id
LEFT JOIN aea_data_dictionary d1 ON d1.dict_type = 'assessment_type' AND d1.dict_key = t.assessment_type
LEFT JOIN aea_data_dictionary d2 ON d2.dict_type = 'task_status' AND d2.dict_key = t.status
WHERE t.is_del = 0;

SELECT '视图创建完成' as message;

-- =============================================
-- 7. 更新现有数据（如果需要）
-- =============================================

-- 为现有记录生成结果编号
UPDATE aea_assessment_result 
SET result_code = CONCAT('AER', DATE_FORMAT(create_time, '%Y%m%d'), LPAD(id, 4, '0'))
WHERE result_code IS NULL OR result_code = '';

-- 为现有记录设置评估日期
UPDATE aea_assessment_result r
INNER JOIN aea_assessment_task t ON r.task_id = t.id
SET r.assessment_date = COALESCE(t.actual_start_time, t.scheduled_start_time, t.create_time)
WHERE r.assessment_date IS NULL;

SELECT '数据更新完成' as message;

SET FOREIGN_KEY_CHECKS = 1;

-- =============================================
-- 执行完成提示
-- =============================================
SELECT '==================================' as message;
SELECT '数据库字段修复脚本执行完成！' as message;
SELECT '请检查以下内容：' as message;
SELECT '1. 表结构是否正确' as message;
SELECT '2. 数据是否完整' as message;
SELECT '3. 索引是否创建成功' as message;
SELECT '4. 视图是否可以正常查询' as message;
SELECT '==================================' as message;
