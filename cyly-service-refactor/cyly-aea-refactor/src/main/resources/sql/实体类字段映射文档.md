# 实体类字段映射文档

## 📋 概述

本文档详细记录了cyly-aea-refactor模块中实体类字段与数据库表字段的映射关系，以及修复后的字段对应情况。

## 🔧 修复完成的实体类

### 1. AeaAssessmentTask (评估任务)

#### 字段映射关系

| 实体类字段 | 数据库字段 | 数据类型 | 映射注解 | 说明 |
|-----------|-----------|----------|----------|------|
| `id` | `id` | Long | @TableId | 主键ID |
| `taskCode` | `task_code` | String | 自动映射 | 任务编号 |
| `taskName` | `task_name` | String | 自动映射 | 任务名称 |
| `taskDescription` | `task_description` | String | 自动映射 | 任务描述 |
| `elderId` | `elder_id` | Long | 自动映射 | 老人ID |
| `deptId` | `dept_id` | Long | 自动映射 | 机构ID |
| `questionnaireId` | `questionnaire_id` | Long | 自动映射 | 问卷ID |
| `assessorId` | `primary_assessor_id` | Long | @TableField | 主评估员ID |
| `deputyAssessorId` | `secondary_assessor_id` | Long | @TableField | 副评估员ID |
| `assessmentType` | `assessment_type` | Integer | 自动映射 | 评估类型 |
| `assessmentLocation` | `assessment_location` | String | 自动映射 | 评估地点 |
| `scheduledStartTime` | `scheduled_start_time` | LocalDateTime | @TableField | 计划开始时间 |
| `scheduledEndTime` | `scheduled_end_time` | LocalDateTime | @TableField | 计划结束时间 |
| `startTime` | `actual_start_time` | LocalDateTime | @TableField | 实际开始时间 |
| `completedTime` | `actual_end_time` | LocalDateTime | @TableField | 实际结束时间 |
| `taskStatus` | `status` | Integer | @TableField | 任务状态 |
| `expectedCompletionTime` | `expected_completion_time` | LocalDateTime | 自动映射 | 预计完成时间 |
| `actualDuration` | `actual_duration` | Integer | 自动映射 | 实际用时（分钟） |
| `priority` | `priority` | String | 自动映射 | 任务优先级 |
| `assessmentReason` | `assessment_reason` | String | 自动映射 | 评估原因 |

#### 枚举值映射

**评估类型 (assessment_type)**
- 1 → INITIAL (首次评估)
- 2 → REVIEW (常规评估)
- 3 → EMERGENCY (即时评估)
- 4 → RECHECK (复评)
- 5 → EXIT (退出评估)

**任务状态 (status)**
- 0 → PENDING (待开始)
- 1 → IN_PROGRESS (进行中)
- 2 → COMPLETED (已完成)
- 3 → CANCELLED (已取消)
- 4 → PAUSED (已暂停)

### 2. AeaAssessmentRecord (评估记录)

#### 字段映射关系

| 实体类字段 | 数据库字段 | 数据类型 | 映射注解 | 说明 |
|-----------|-----------|----------|----------|------|
| `id` | `id` | Long | @TableId | 主键ID |
| `taskId` | `task_id` | Long | 自动映射 | 任务ID |
| `questionnaireId` | `questionnaire_id` | Long | 自动映射 | 问卷ID |
| `questionId` | `question_id` | Long | 自动映射 | 问题ID |
| `optionId` | `option_id` | Long | 自动映射 | 选项ID |
| `answerContent` | `answer_content` | String | 自动映射 | 答案内容 |
| `score` | `score` | Integer | 自动映射 | 得分 |
| `assessorId` | `assessor_id` | Long | 自动映射 | 评估员ID |
| `answerTime` | `assessment_time` | LocalDateTime | @TableField | 评估时间 |
| `answerStatus` | `answer_status` | String | 自动映射 | 答题状态 |
| `questionType` | `question_type` | String | 自动映射 | 问题类型 |
| `questionCategory` | `question_category` | String | 自动映射 | 问题分类 |
| `isRequired` | `is_required` | Integer | 自动映射 | 是否必答 |
| `answerRemark` | `answer_remark` | String | 自动映射 | 答案备注 |

### 3. AeaAssessmentResult (评估结果)

#### 字段映射关系

| 实体类字段 | 数据库字段 | 数据类型 | 映射注解 | 说明 |
|-----------|-----------|----------|----------|------|
| `id` | `id` | Long | @TableId | 主键ID |
| `taskId` | `task_id` | Long | 自动映射 | 任务ID |
| `elderId` | `elder_id` | Long | 自动映射 | 老人ID |
| `resultCode` | `result_code` | String | 自动映射 | 结果编号 |
| `assessmentDate` | `assessment_date` | LocalDateTime | 自动映射 | 评估日期 |
| `selfCareScore` | `self_care_score` | Integer | 自动映射 | 自理能力得分 |
| `basicMobilityScore` | `basic_mobility_score` | Integer | @TableField | 基础运动能力得分 |
| `mentalStateScore` | `mental_state_score` | Integer | 自动映射 | 精神状态得分 |
| `perceptionSocialScore` | `perception_social_score` | Integer | 自动映射 | 感知觉与社会参与得分 |
| `totalScore` | `total_score` | Integer | 自动映射 | 总分 |
| `preliminaryLevel` | `preliminary_level` | Integer | 自动映射 | 初步评估等级 |
| `adjustmentReason` | `adjustment_reason` | String | 自动映射 | 等级调整原因 |
| `finalLevel` | `final_level` | Integer | @TableField | 最终评估等级 |
| `assessmentConclusion` | `assessment_conclusion` | String | 自动映射 | 评估结论 |
| `recommendations` | `recommendations` | String | @TableField | 建议措施 |
| `primaryAssessorSignature` | `primary_assessor_signature` | String | 自动映射 | 主评估员签名 |
| `secondaryAssessorSignature` | `secondary_assessor_signature` | String | 自动映射 | 副评估员签名 |
| `informationProviderSignature` | `information_provider_signature` | String | 自动映射 | 信息提供者签名 |
| `assessorName` | `assessor_name` | String | 自动映射 | 主评估员姓名 |
| `deputyAssessorName` | `deputy_assessor_name` | String | 自动映射 | 副评估员姓名 |
| `informationProviderName` | `information_provider_name` | String | 自动映射 | 信息提供者姓名 |
| `assessmentLocation` | `assessment_location` | String | 自动映射 | 评估地点 |

#### 评估等级映射

**评估等级 (final_level)**
- 1 → EXCELLENT (能力完好)
- 2 → MILD_IMPAIRMENT (轻度受损)
- 3 → MODERATE_IMPAIRMENT (中度受损)
- 4 → SEVERE_IMPAIRMENT (重度受损)
- 5 → COMPLETE_LOSS (完全丧失)

### 4. AeaAssessmentReport (评估报告)

#### 字段映射关系

| 实体类字段 | 数据库字段 | 数据类型 | 映射注解 | 说明 |
|-----------|-----------|----------|----------|------|
| `id` | `id` | Long | @TableId | 主键ID |
| `reportCode` | `report_code` | String | 自动映射 | 报告编号 |
| `taskId` | `task_id` | Long | 自动映射 | 任务ID |
| `resultId` | `result_id` | Long | 自动映射 | 评估结果ID |
| `elderId` | `elder_id` | Long | 自动映射 | 老人ID |
| `reportTitle` | `report_title` | String | 自动映射 | 报告标题 |
| `reportType` | `report_type` | Integer | 自动映射 | 报告类型 |
| `templateId` | `template_id` | Long | 自动映射 | 模板ID |
| `reportContent` | `report_content` | String | 自动映射 | 报告内容 |
| `reportSummary` | `report_summary` | String | 自动映射 | 报告摘要 |
| `pdfFilePath` | `pdf_file_url` | String | @TableField | PDF文件地址 |
| `pdfFileName` | `pdf_file_name` | String | 自动映射 | PDF文件名 |
| `fileSize` | `pdf_file_size` | Long | @TableField | PDF文件大小 |
| `reportStatus` | `generation_status` | Integer | @TableField | 生成状态 |
| `generateTime` | `generation_time` | LocalDateTime | @TableField | 生成时间 |
| `failureReason` | `error_message` | String | @TableField | 错误信息 |
| `downloadCount` | `download_count` | Integer | 自动映射 | 下载次数 |

## 🔄 数据类型转换处理

### 1. 枚举类型转换

在Service层需要处理数据库整型值与业务枚举值的转换：

```java
// 评估类型转换
public String convertAssessmentType(Integer dbValue) {
    switch (dbValue) {
        case 1: return "INITIAL";
        case 2: return "REVIEW";
        case 3: return "EMERGENCY";
        case 4: return "RECHECK";
        case 5: return "EXIT";
        default: return "UNKNOWN";
    }
}

// 任务状态转换
public String convertTaskStatus(Integer dbValue) {
    switch (dbValue) {
        case 0: return "PENDING";
        case 1: return "IN_PROGRESS";
        case 2: return "COMPLETED";
        case 3: return "CANCELLED";
        case 4: return "PAUSED";
        default: return "UNKNOWN";
    }
}
```

### 2. 分数类型统一

所有分数字段统一使用Integer类型，避免BigDecimal的复杂性。

### 3. 时间字段处理

使用LocalDateTime类型处理所有时间字段，确保时区一致性。

## 📝 使用注意事项

### 1. @TableField注解使用

当实体类字段名与数据库字段名不一致时，必须使用@TableField注解指定映射关系。

### 2. 枚举值处理

在Bo和Vo类中，可以保持String类型的枚举值，在Service层进行转换。

### 3. 数据验证

在Bo类中添加适当的验证注解，确保数据完整性。

### 4. 索引优化

数据库表已添加必要的索引，查询时应充分利用这些索引。

## 🚀 后续优化建议

1. **创建枚举类**：为评估类型、任务状态等创建专门的枚举类
2. **类型转换器**：实现MyBatis的TypeHandler进行自动类型转换
3. **数据校验**：在实体类中添加更详细的数据校验规则
4. **缓存优化**：对常用的字典数据进行缓存处理

## 📊 测试验证

修复完成后，建议进行以下测试：

1. **单元测试**：验证实体类字段映射正确性
2. **集成测试**：验证CRUD操作正常
3. **数据一致性测试**：验证数据库与实体类数据一致
4. **性能测试**：验证查询性能符合预期
