package org.dromara.cyly.aea.refactor.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 评估结果对象 aea_assessment_result
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("aea_assessment_result")
public class AeaAssessmentResult extends BaseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 结果ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 老人ID
     */
    private Long elderId;

    /**
     * 结果编号
     */
    private String resultCode;

    /**
     * 评估日期
     */
    private LocalDateTime assessmentDate;

    /**
     * 自理能力得分
     */
    private Integer selfCareScore;

    /**
     * 基础运动能力得分
     */
    @TableField("basic_mobility_score")
    private Integer basicMobilityScore;

    /**
     * 精神状态得分
     */
    private Integer mentalStateScore;

    /**
     * 感知觉与社会参与得分
     */
    private Integer perceptionSocialScore;

    /**
     * 总分
     */
    private Integer totalScore;

    /**
     * 初步评估等级
     */
    private Integer preliminaryLevel;

    /**
     * 等级调整原因
     */
    private String adjustmentReason;

    /**
     * 最终评估等级
     */
    @TableField("final_level")
    private Integer finalLevel;

    /**
     * 评估结论
     */
    private String assessmentConclusion;

    /**
     * 建议措施
     */
    @TableField("recommendations")
    private String recommendations;

    /**
     * 主评估员签名
     */
    private String primaryAssessorSignature;

    /**
     * 副评估员签名
     */
    private String secondaryAssessorSignature;

    /**
     * 信息提供者签名
     */
    private String informationProviderSignature;

    /**
     * 主评估员姓名
     */
    private String assessorName;

    /**
     * 副评估员姓名
     */
    private String deputyAssessorName;

    /**
     * 信息提供者姓名
     */
    private String informationProviderName;

    /**
     * 评估地点
     */
    private String assessmentLocation;

    /**
     * 创建部门
     */
    private Long createDept;

}