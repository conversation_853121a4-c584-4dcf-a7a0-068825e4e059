package org.dromara.cyly.aea.refactor.domain.vo;

import org.dromara.cyly.aea.refactor.domain.AeaAssessmentRecord;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.math.BigDecimal;

/**
 * 评估记录视图对象 aea_assessment_record
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = AeaAssessmentRecord.class)
public class AeaAssessmentRecordVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ExcelProperty(value = "主键ID")
    private Long id;

    /**
     * 任务ID
     */
    @ExcelProperty(value = "任务ID")
    private Long taskId;

    /**
     * 问题ID
     */
    @ExcelProperty(value = "问题ID")
    private Long questionId;

    /**
     * 选项ID
     */
    @ExcelProperty(value = "选项ID")
    private Long optionId;

    /**
     * 得分
     */
    @ExcelProperty(value = "得分")
    private Integer score;

    /**
     * 答案内容
     */
    @ExcelProperty(value = "答案内容")
    private String answerContent;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private LocalDateTime createTime;

}