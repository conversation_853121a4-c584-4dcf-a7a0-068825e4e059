package org.dromara.cyly.aea.refactor.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 评估报告对象 aea_assessment_report
 *
 * <AUTHOR>
 * @date 2024-12-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("aea_assessment_report")
public class AeaAssessmentReport extends BaseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 报告ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 模板ID
     */
    private Long templateId;

    /**
     * 报告编号
     */
    private String reportCode;

    /**
     * 报告标题
     */
    private String reportTitle;

    /**
     * PDF文件路径
     */
    private String pdfFilePath;

    /**
     * PDF文件名
     */
    private String pdfFileName;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 报告状态：生成中、已生成、生成失败
     */
    private String reportStatus;

    /**
     * 生成状态：PENDING-待生成，GENERATING-生成中，SUCCESS-生成成功，FAILED-生成失败
     */
    private String generateStatus;

    /**
     * 生成时间
     */
    private LocalDateTime generateTime;

    /**
     * 失败原因
     */
    private String failureReason;

    /**
     * 下载次数
     */
    private Integer downloadCount;

    /**
     * 创建部门
     */
    private Long createDept;

}