package io.github.linpeilie;

import org.dromara.cyly.aea.refactor.domain.AeaAssessmentRecordToAeaAssessmentRecordVoMapper__3;
import org.dromara.cyly.aea.refactor.domain.AeaElderHealthInfoToAeaElderHealthInfoVoMapper__3;
import org.dromara.cyly.aea.refactor.domain.AeaElderPersonalInfoToAeaElderPersonalInfoVoMapper__3;
import org.dromara.cyly.aea.refactor.domain.AeaElderRelationshipToAeaElderRelationshipVoMapper__3;
import org.dromara.cyly.aea.refactor.domain.AeaElderToAeaElderVoMapper__3;
import org.dromara.cyly.aea.refactor.domain.AeaEvaluateReportToAeaEvaluateReportVoMapper__3;
import org.dromara.cyly.aea.refactor.domain.AeaGbMedicineToAeaGbMedicineVoMapper__3;
import org.dromara.cyly.aea.refactor.domain.AeaTaskEvaluateExecutionRecordToAeaTaskEvaluateExecutionRecordVoMapper__3;
import org.dromara.cyly.aea.refactor.domain.AeaTaskEvaluateInfoToAeaTaskEvaluateInfoVoMapper__3;
import org.dromara.cyly.aea.refactor.domain.AeaTaskToAeaTaskVoMapper__3;
import org.dromara.cyly.aea.refactor.domain.bo.AeaAssessmentReportBoToAeaAssessmentReportMapper__3;
import org.dromara.cyly.aea.refactor.domain.bo.AeaAssessmentResultBoToAeaAssessmentResultMapper__23;
import org.dromara.cyly.aea.refactor.domain.bo.AeaAssessmentResultBoToAeaAssessmentResultMapper__3;
import org.dromara.cyly.aea.refactor.domain.bo.AeaGbMedicineBoToAeaGbMedicineMapper__3;
import org.dromara.cyly.aea.refactor.domain.bo.AeaReportTemplateBoToAeaReportTemplateMapper__3;
import org.dromara.cyly.aea.refactor.domain.vo.AeaAssessmentRecordVoToAeaAssessmentRecordMapper__3;
import org.dromara.cyly.aea.refactor.domain.vo.AeaElderHealthInfoVoToAeaElderHealthInfoMapper__3;
import org.dromara.cyly.aea.refactor.domain.vo.AeaElderPersonalInfoVoToAeaElderPersonalInfoMapper__3;
import org.dromara.cyly.aea.refactor.domain.vo.AeaElderRelationshipVoToAeaElderRelationshipMapper__3;
import org.dromara.cyly.aea.refactor.domain.vo.AeaElderVoToAeaElderMapper__3;
import org.dromara.cyly.aea.refactor.domain.vo.AeaEvaluateReportVoToAeaEvaluateReportMapper__3;
import org.dromara.cyly.aea.refactor.domain.vo.AeaGbMedicineVoToAeaGbMedicineMapper__3;
import org.dromara.cyly.aea.refactor.domain.vo.AeaTaskEvaluateExecutionRecordVoToAeaTaskEvaluateExecutionRecordMapper__3;
import org.dromara.cyly.aea.refactor.domain.vo.AeaTaskEvaluateInfoVoToAeaTaskEvaluateInfoMapper__3;
import org.dromara.cyly.aea.refactor.domain.vo.AeaTaskVoToAeaTaskMapper__3;
import org.mapstruct.Builder;
import org.mapstruct.MapperConfig;
import org.mapstruct.ReportingPolicy;

@MapperConfig(
    componentModel = "spring-lazy",
    uses = {ConverterMapperAdapter__356.class, AeaElderToAeaElderVoMapper__3.class, AeaAssessmentResultBoToAeaAssessmentResultMapper__3.class, AeaAssessmentRecordToAeaAssessmentRecordVoMapper__3.class, AeaAssessmentResultBoToAeaAssessmentResultMapper__23.class, AeaAssessmentReportBoToAeaAssessmentReportMapper__3.class, AeaTaskEvaluateExecutionRecordVoToAeaTaskEvaluateExecutionRecordMapper__3.class, AeaGbMedicineBoToAeaGbMedicineMapper__3.class, AeaGbMedicineToAeaGbMedicineVoMapper__3.class, AeaGbMedicineVoToAeaGbMedicineMapper__3.class, AeaReportTemplateBoToAeaReportTemplateMapper__3.class, AeaAssessmentRecordVoToAeaAssessmentRecordMapper__3.class, AeaElderPersonalInfoToAeaElderPersonalInfoVoMapper__3.class, AeaTaskEvaluateInfoToAeaTaskEvaluateInfoVoMapper__3.class, AeaTaskVoToAeaTaskMapper__3.class, AeaTaskEvaluateInfoVoToAeaTaskEvaluateInfoMapper__3.class, AeaElderRelationshipToAeaElderRelationshipVoMapper__3.class, AeaElderHealthInfoVoToAeaElderHealthInfoMapper__3.class, AeaElderRelationshipVoToAeaElderRelationshipMapper__3.class, AeaEvaluateReportVoToAeaEvaluateReportMapper__3.class, AeaTaskToAeaTaskVoMapper__3.class, AeaElderPersonalInfoVoToAeaElderPersonalInfoMapper__3.class, AeaElderHealthInfoToAeaElderHealthInfoVoMapper__3.class, AeaElderVoToAeaElderMapper__3.class, AeaEvaluateReportToAeaEvaluateReportVoMapper__3.class, AeaTaskEvaluateExecutionRecordToAeaTaskEvaluateExecutionRecordVoMapper__3.class},
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    builder = @Builder(buildMethod = "build", disableBuilder = true)
)
public interface AutoMapperConfig__356 {
}
