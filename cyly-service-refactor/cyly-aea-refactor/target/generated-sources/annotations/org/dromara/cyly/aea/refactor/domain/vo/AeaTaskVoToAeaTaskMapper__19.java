package org.dromara.cyly.aea.refactor.domain.vo;

import io.github.linpeilie.AutoMapperConfig__356;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.AeaTask;
import org.dromara.cyly.aea.refactor.domain.AeaTaskToAeaTaskVoMapper__19;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__356.class,
    uses = {AeaTaskToAeaTaskVoMapper__19.class},
    imports = {}
)
public interface AeaTaskVoToAeaTaskMapper__19 extends BaseMapper<AeaTaskVo, AeaTask> {
}
