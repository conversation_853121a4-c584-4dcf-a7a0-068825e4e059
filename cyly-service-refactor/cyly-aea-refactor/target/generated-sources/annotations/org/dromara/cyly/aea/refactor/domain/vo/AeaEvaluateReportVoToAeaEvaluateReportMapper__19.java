package org.dromara.cyly.aea.refactor.domain.vo;

import io.github.linpeilie.AutoMapperConfig__356;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.AeaEvaluateReport;
import org.dromara.cyly.aea.refactor.domain.AeaEvaluateReportToAeaEvaluateReportVoMapper__19;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__356.class,
    uses = {AeaEvaluateReportToAeaEvaluateReportVoMapper__19.class},
    imports = {}
)
public interface AeaEvaluateReportVoToAeaEvaluateReportMapper__19 extends BaseMapper<AeaEvaluateReportVo, AeaEvaluateReport> {
}
