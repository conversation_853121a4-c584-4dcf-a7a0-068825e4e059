package org.dromara.cyly.aea.refactor.domain;

import io.github.linpeilie.AutoMapperConfig__356;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.bo.AeaGbMedicineBoToAeaGbMedicineMapper__21;
import org.dromara.cyly.aea.refactor.domain.vo.AeaGbMedicineVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaGbMedicineVoToAeaGbMedicineMapper__21;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__356.class,
    uses = {AeaGbMedicineVoToAeaGbMedicineMapper__21.class,AeaGbMedicineBoToAeaGbMedicineMapper__21.class},
    imports = {}
)
public interface AeaGbMedicineToAeaGbMedicineVoMapper__21 extends BaseMapper<AeaGbMedicine, AeaGbMedicineVo> {
}
