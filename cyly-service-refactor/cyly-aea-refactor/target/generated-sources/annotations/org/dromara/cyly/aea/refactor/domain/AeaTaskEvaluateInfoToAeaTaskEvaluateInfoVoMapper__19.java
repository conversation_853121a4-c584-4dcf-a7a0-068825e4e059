package org.dromara.cyly.aea.refactor.domain;

import io.github.linpeilie.AutoMapperConfig__356;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.vo.AeaTaskEvaluateInfoVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaTaskEvaluateInfoVoToAeaTaskEvaluateInfoMapper__19;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__356.class,
    uses = {AeaTaskEvaluateInfoVoToAeaTaskEvaluateInfoMapper__19.class},
    imports = {}
)
public interface AeaTaskEvaluateInfoToAeaTaskEvaluateInfoVoMapper__19 extends BaseMapper<AeaTaskEvaluateInfo, AeaTaskEvaluateInfoVo> {
}
