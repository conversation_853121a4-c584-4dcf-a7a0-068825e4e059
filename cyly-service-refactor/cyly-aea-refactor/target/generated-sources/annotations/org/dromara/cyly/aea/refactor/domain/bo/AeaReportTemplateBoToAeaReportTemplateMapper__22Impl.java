package org.dromara.cyly.aea.refactor.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.cyly.aea.refactor.domain.AeaReportTemplate;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T10:19:19+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AeaReportTemplateBoToAeaReportTemplateMapper__22Impl implements AeaReportTemplateBoToAeaReportTemplateMapper__22 {

    @Override
    public AeaReportTemplate convert(AeaReportTemplateBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AeaReportTemplate aeaReportTemplate = new AeaReportTemplate();

        aeaReportTemplate.setCreateBy( arg0.getCreateBy() );
        aeaReportTemplate.setCreateTime( arg0.getCreateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            aeaReportTemplate.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        aeaReportTemplate.setSearchValue( arg0.getSearchValue() );
        aeaReportTemplate.setUpdateBy( arg0.getUpdateBy() );
        aeaReportTemplate.setUpdateTime( arg0.getUpdateTime() );
        aeaReportTemplate.setCreateDept( arg0.getCreateDept() );
        aeaReportTemplate.setId( arg0.getId() );
        if ( arg0.getIsDefault() != null ) {
            aeaReportTemplate.setIsDefault( String.valueOf( arg0.getIsDefault() ) );
        }
        if ( arg0.getStatus() != null ) {
            aeaReportTemplate.setStatus( String.valueOf( arg0.getStatus() ) );
        }
        aeaReportTemplate.setTemplateContent( arg0.getTemplateContent() );
        aeaReportTemplate.setTemplateName( arg0.getTemplateName() );
        aeaReportTemplate.setTemplateType( arg0.getTemplateType() );

        return aeaReportTemplate;
    }

    @Override
    public AeaReportTemplate convert(AeaReportTemplateBo arg0, AeaReportTemplate arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setId( arg0.getId() );
        if ( arg0.getIsDefault() != null ) {
            arg1.setIsDefault( String.valueOf( arg0.getIsDefault() ) );
        }
        else {
            arg1.setIsDefault( null );
        }
        if ( arg0.getStatus() != null ) {
            arg1.setStatus( String.valueOf( arg0.getStatus() ) );
        }
        else {
            arg1.setStatus( null );
        }
        arg1.setTemplateContent( arg0.getTemplateContent() );
        arg1.setTemplateName( arg0.getTemplateName() );
        arg1.setTemplateType( arg0.getTemplateType() );

        return arg1;
    }
}
