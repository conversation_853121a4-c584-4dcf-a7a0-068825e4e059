package org.dromara.cyly.aea.refactor.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.cyly.aea.refactor.domain.AeaAssessmentResult;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T10:19:19+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AeaAssessmentResultBoToAeaAssessmentResultMapper__23Impl implements AeaAssessmentResultBoToAeaAssessmentResultMapper__23 {

    @Override
    public AeaAssessmentResult convert(AeaAssessmentResultBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AeaAssessmentResult aeaAssessmentResult = new AeaAssessmentResult();

        aeaAssessmentResult.setCreateBy( arg0.getCreateBy() );
        aeaAssessmentResult.setCreateTime( arg0.getCreateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            aeaAssessmentResult.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        aeaAssessmentResult.setSearchValue( arg0.getSearchValue() );
        aeaAssessmentResult.setUpdateBy( arg0.getUpdateBy() );
        aeaAssessmentResult.setUpdateTime( arg0.getUpdateTime() );
        aeaAssessmentResult.setAssessmentConclusion( arg0.getAssessmentConclusion() );
        aeaAssessmentResult.setCreateDept( arg0.getCreateDept() );
        aeaAssessmentResult.setId( arg0.getId() );
        if ( arg0.getMentalStateScore() != null ) {
            aeaAssessmentResult.setMentalStateScore( arg0.getMentalStateScore().intValue() );
        }
        if ( arg0.getPerceptionSocialScore() != null ) {
            aeaAssessmentResult.setPerceptionSocialScore( arg0.getPerceptionSocialScore().intValue() );
        }
        if ( arg0.getSelfCareScore() != null ) {
            aeaAssessmentResult.setSelfCareScore( arg0.getSelfCareScore().intValue() );
        }
        aeaAssessmentResult.setTaskId( arg0.getTaskId() );
        if ( arg0.getTotalScore() != null ) {
            aeaAssessmentResult.setTotalScore( arg0.getTotalScore().intValue() );
        }

        return aeaAssessmentResult;
    }

    @Override
    public AeaAssessmentResult convert(AeaAssessmentResultBo arg0, AeaAssessmentResult arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setAssessmentConclusion( arg0.getAssessmentConclusion() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setId( arg0.getId() );
        if ( arg0.getMentalStateScore() != null ) {
            arg1.setMentalStateScore( arg0.getMentalStateScore().intValue() );
        }
        else {
            arg1.setMentalStateScore( null );
        }
        if ( arg0.getPerceptionSocialScore() != null ) {
            arg1.setPerceptionSocialScore( arg0.getPerceptionSocialScore().intValue() );
        }
        else {
            arg1.setPerceptionSocialScore( null );
        }
        if ( arg0.getSelfCareScore() != null ) {
            arg1.setSelfCareScore( arg0.getSelfCareScore().intValue() );
        }
        else {
            arg1.setSelfCareScore( null );
        }
        arg1.setTaskId( arg0.getTaskId() );
        if ( arg0.getTotalScore() != null ) {
            arg1.setTotalScore( arg0.getTotalScore().intValue() );
        }
        else {
            arg1.setTotalScore( null );
        }

        return arg1;
    }
}
