package org.dromara.cyly.aea.refactor.domain.vo;

import io.github.linpeilie.AutoMapperConfig__356;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.AeaEvaluateReport;
import org.dromara.cyly.aea.refactor.domain.AeaEvaluateReportToAeaEvaluateReportVoMapper__21;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__356.class,
    uses = {AeaEvaluateReportToAeaEvaluateReportVoMapper__21.class},
    imports = {}
)
public interface AeaEvaluateReportVoToAeaEvaluateReportMapper__21 extends BaseMapper<AeaEvaluateReportVo, AeaEvaluateReport> {
}
