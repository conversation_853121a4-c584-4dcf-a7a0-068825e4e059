package org.dromara.cyly.aea.refactor.domain;

import java.time.LocalDateTime;
import java.time.ZoneId;
import javax.annotation.processing.Generated;
import org.dromara.cyly.aea.refactor.domain.vo.AeaAssessmentRecordVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T10:03:53+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AeaAssessmentRecordToAeaAssessmentRecordVoMapper__16Impl implements AeaAssessmentRecordToAeaAssessmentRecordVoMapper__16 {

    @Override
    public AeaAssessmentRecordVo convert(AeaAssessmentRecord arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AeaAssessmentRecordVo aeaAssessmentRecordVo = new AeaAssessmentRecordVo();

        aeaAssessmentRecordVo.setAnswerContent( arg0.getAnswerContent() );
        if ( arg0.getCreateTime() != null ) {
            aeaAssessmentRecordVo.setCreateTime( LocalDateTime.ofInstant( arg0.getCreateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        aeaAssessmentRecordVo.setId( arg0.getId() );
        aeaAssessmentRecordVo.setOptionId( arg0.getOptionId() );
        aeaAssessmentRecordVo.setQuestionId( arg0.getQuestionId() );
        aeaAssessmentRecordVo.setScore( arg0.getScore() );
        aeaAssessmentRecordVo.setTaskId( arg0.getTaskId() );

        return aeaAssessmentRecordVo;
    }

    @Override
    public AeaAssessmentRecordVo convert(AeaAssessmentRecord arg0, AeaAssessmentRecordVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setAnswerContent( arg0.getAnswerContent() );
        if ( arg0.getCreateTime() != null ) {
            arg1.setCreateTime( LocalDateTime.ofInstant( arg0.getCreateTime().toInstant(), ZoneId.of( "UTC" ) ) );
        }
        else {
            arg1.setCreateTime( null );
        }
        arg1.setId( arg0.getId() );
        arg1.setOptionId( arg0.getOptionId() );
        arg1.setQuestionId( arg0.getQuestionId() );
        arg1.setScore( arg0.getScore() );
        arg1.setTaskId( arg0.getTaskId() );

        return arg1;
    }
}
