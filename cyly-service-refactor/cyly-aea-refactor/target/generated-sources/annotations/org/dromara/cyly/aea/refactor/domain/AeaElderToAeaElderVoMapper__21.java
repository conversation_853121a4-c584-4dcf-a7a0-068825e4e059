package org.dromara.cyly.aea.refactor.domain;

import io.github.linpeilie.AutoMapperConfig__356;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.vo.AeaElderVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaElderVoToAeaElderMapper__21;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__356.class,
    uses = {AeaElderVoToAeaElderMapper__21.class},
    imports = {}
)
public interface AeaElderToAeaElderVoMapper__21 extends BaseMapper<AeaElder, AeaElderVo> {
}
