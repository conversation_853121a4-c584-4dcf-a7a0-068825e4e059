package org.dromara.cyly.aea.refactor.domain;

import javax.annotation.processing.Generated;
import org.dromara.cyly.aea.refactor.domain.vo.AeaEvaluateReportVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T10:19:19+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AeaEvaluateReportToAeaEvaluateReportVoMapper__21Impl implements AeaEvaluateReportToAeaEvaluateReportVoMapper__21 {

    @Override
    public AeaEvaluateReportVo convert(AeaEvaluateReport arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AeaEvaluateReportVo aeaEvaluateReportVo = new AeaEvaluateReportVo();

        aeaEvaluateReportVo.setAdjustmentBasis( arg0.getAdjustmentBasis() );
        aeaEvaluateReportVo.setAnswerId( arg0.getAnswerId() );
        aeaEvaluateReportVo.setAssessorEvaluateUserId( arg0.getAssessorEvaluateUserId() );
        aeaEvaluateReportVo.setAssessorSign( arg0.getAssessorSign() );
        aeaEvaluateReportVo.setBaseScore( arg0.getBaseScore() );
        aeaEvaluateReportVo.setCode( arg0.getCode() );
        aeaEvaluateReportVo.setContent( arg0.getContent() );
        aeaEvaluateReportVo.setCreateBy( arg0.getCreateBy() );
        aeaEvaluateReportVo.setCreateTime( arg0.getCreateTime() );
        aeaEvaluateReportVo.setDeputyEvaluateUserId( arg0.getDeputyEvaluateUserId() );
        aeaEvaluateReportVo.setDeputySign( arg0.getDeputySign() );
        aeaEvaluateReportVo.setElderId( arg0.getElderId() );
        aeaEvaluateReportVo.setEvaluateUserId( arg0.getEvaluateUserId() );
        aeaEvaluateReportVo.setFeelScore( arg0.getFeelScore() );
        aeaEvaluateReportVo.setFinalLevel( arg0.getFinalLevel() );
        aeaEvaluateReportVo.setFirstLevel( arg0.getFirstLevel() );
        aeaEvaluateReportVo.setId( arg0.getId() );
        aeaEvaluateReportVo.setInformationProviderSign( arg0.getInformationProviderSign() );
        aeaEvaluateReportVo.setIsDel( arg0.getIsDel() );
        aeaEvaluateReportVo.setLocation( arg0.getLocation() );
        aeaEvaluateReportVo.setMentionScore( arg0.getMentionScore() );
        aeaEvaluateReportVo.setReasonCode( arg0.getReasonCode() );
        aeaEvaluateReportVo.setReportUrl( arg0.getReportUrl() );
        aeaEvaluateReportVo.setScore( arg0.getScore() );
        aeaEvaluateReportVo.setSelfScore( arg0.getSelfScore() );
        aeaEvaluateReportVo.setTaskId( arg0.getTaskId() );
        aeaEvaluateReportVo.setTotalScore( arg0.getTotalScore() );
        aeaEvaluateReportVo.setUpdateBy( arg0.getUpdateBy() );
        aeaEvaluateReportVo.setUpdateTime( arg0.getUpdateTime() );

        return aeaEvaluateReportVo;
    }

    @Override
    public AeaEvaluateReportVo convert(AeaEvaluateReport arg0, AeaEvaluateReportVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setAdjustmentBasis( arg0.getAdjustmentBasis() );
        arg1.setAnswerId( arg0.getAnswerId() );
        arg1.setAssessorEvaluateUserId( arg0.getAssessorEvaluateUserId() );
        arg1.setAssessorSign( arg0.getAssessorSign() );
        arg1.setBaseScore( arg0.getBaseScore() );
        arg1.setCode( arg0.getCode() );
        arg1.setContent( arg0.getContent() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setDeputyEvaluateUserId( arg0.getDeputyEvaluateUserId() );
        arg1.setDeputySign( arg0.getDeputySign() );
        arg1.setElderId( arg0.getElderId() );
        arg1.setEvaluateUserId( arg0.getEvaluateUserId() );
        arg1.setFeelScore( arg0.getFeelScore() );
        arg1.setFinalLevel( arg0.getFinalLevel() );
        arg1.setFirstLevel( arg0.getFirstLevel() );
        arg1.setId( arg0.getId() );
        arg1.setInformationProviderSign( arg0.getInformationProviderSign() );
        arg1.setIsDel( arg0.getIsDel() );
        arg1.setLocation( arg0.getLocation() );
        arg1.setMentionScore( arg0.getMentionScore() );
        arg1.setReasonCode( arg0.getReasonCode() );
        arg1.setReportUrl( arg0.getReportUrl() );
        arg1.setScore( arg0.getScore() );
        arg1.setSelfScore( arg0.getSelfScore() );
        arg1.setTaskId( arg0.getTaskId() );
        arg1.setTotalScore( arg0.getTotalScore() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );

        return arg1;
    }
}
