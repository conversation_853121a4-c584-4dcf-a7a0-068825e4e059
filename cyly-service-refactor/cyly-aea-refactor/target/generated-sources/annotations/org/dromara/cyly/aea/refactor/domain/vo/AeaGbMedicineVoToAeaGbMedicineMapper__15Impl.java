package org.dromara.cyly.aea.refactor.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.cyly.aea.refactor.domain.AeaGbMedicine;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T09:51:46+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AeaGbMedicineVoToAeaGbMedicineMapper__15Impl implements AeaGbMedicineVoToAeaGbMedicineMapper__15 {

    @Override
    public AeaGbMedicine convert(AeaGbMedicineVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AeaGbMedicine aeaGbMedicine = new AeaGbMedicine();

        aeaGbMedicine.setCode( arg0.getCode() );
        aeaGbMedicine.setId( arg0.getId() );
        aeaGbMedicine.setIsDeleted( arg0.getIsDeleted() );
        aeaGbMedicine.setMedicineName( arg0.getMedicineName() );
        aeaGbMedicine.setPinyinCode( arg0.getPinyinCode() );

        return aeaGbMedicine;
    }

    @Override
    public AeaGbMedicine convert(AeaGbMedicineVo arg0, AeaGbMedicine arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCode( arg0.getCode() );
        arg1.setId( arg0.getId() );
        arg1.setIsDeleted( arg0.getIsDeleted() );
        arg1.setMedicineName( arg0.getMedicineName() );
        arg1.setPinyinCode( arg0.getPinyinCode() );

        return arg1;
    }
}
