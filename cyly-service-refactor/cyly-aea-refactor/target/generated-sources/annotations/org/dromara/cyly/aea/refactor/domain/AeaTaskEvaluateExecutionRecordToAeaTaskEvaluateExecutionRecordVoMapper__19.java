package org.dromara.cyly.aea.refactor.domain;

import io.github.linpeilie.AutoMapperConfig__356;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.vo.AeaTaskEvaluateExecutionRecordVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaTaskEvaluateExecutionRecordVoToAeaTaskEvaluateExecutionRecordMapper__19;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__356.class,
    uses = {AeaTaskEvaluateExecutionRecordVoToAeaTaskEvaluateExecutionRecordMapper__19.class},
    imports = {}
)
public interface AeaTaskEvaluateExecutionRecordToAeaTaskEvaluateExecutionRecordVoMapper__19 extends BaseMapper<AeaTaskEvaluateExecutionRecord, AeaTaskEvaluateExecutionRecordVo> {
}
