package org.dromara.cyly.aea.refactor.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.cyly.aea.refactor.domain.AeaGbMedicine;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T10:19:19+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AeaGbMedicineBoToAeaGbMedicineMapper__21Impl implements AeaGbMedicineBoToAeaGbMedicineMapper__21 {

    @Override
    public AeaGbMedicine convert(AeaGbMedicineBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AeaGbMedicine aeaGbMedicine = new AeaGbMedicine();

        aeaGbMedicine.setCreateBy( arg0.getCreateBy() );
        aeaGbMedicine.setCreateDept( arg0.getCreateDept() );
        aeaGbMedicine.setCreateTime( arg0.getCreateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            aeaGbMedicine.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        aeaGbMedicine.setSearchValue( arg0.getSearchValue() );
        aeaGbMedicine.setUpdateBy( arg0.getUpdateBy() );
        aeaGbMedicine.setUpdateTime( arg0.getUpdateTime() );
        aeaGbMedicine.setCode( arg0.getCode() );
        aeaGbMedicine.setId( arg0.getId() );
        aeaGbMedicine.setIsDeleted( arg0.getIsDeleted() );
        aeaGbMedicine.setMedicineName( arg0.getMedicineName() );
        aeaGbMedicine.setPinyinCode( arg0.getPinyinCode() );

        return aeaGbMedicine;
    }

    @Override
    public AeaGbMedicine convert(AeaGbMedicineBo arg0, AeaGbMedicine arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateTime( arg0.getCreateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setCode( arg0.getCode() );
        arg1.setId( arg0.getId() );
        arg1.setIsDeleted( arg0.getIsDeleted() );
        arg1.setMedicineName( arg0.getMedicineName() );
        arg1.setPinyinCode( arg0.getPinyinCode() );

        return arg1;
    }
}
