package org.dromara.cyly.aea.refactor.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.cyly.aea.refactor.domain.AeaElder;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T10:14:26+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AeaElderVoToAeaElderMapper__20Impl implements AeaElderVoToAeaElderMapper__20 {

    @Override
    public AeaElder convert(AeaElderVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AeaElder aeaElder = new AeaElder();

        aeaElder.setBirthday( arg0.getBirthday() );
        aeaElder.setCreateBy( arg0.getCreateBy() );
        aeaElder.setCreateTime( arg0.getCreateTime() );
        aeaElder.setDateOfDeath( arg0.getDateOfDeath() );
        aeaElder.setDeptId( arg0.getDeptId() );
        aeaElder.setGender( arg0.getGender() );
        aeaElder.setHouseHoldType( arg0.getHouseHoldType() );
        aeaElder.setId( arg0.getId() );
        aeaElder.setIdCardAddressDetail( arg0.getIdCardAddressDetail() );
        aeaElder.setIdCardAddressId( arg0.getIdCardAddressId() );
        aeaElder.setIdCardNum( arg0.getIdCardNum() );
        aeaElder.setIdCardType( arg0.getIdCardType() );
        aeaElder.setIdPhotos( arg0.getIdPhotos() );
        aeaElder.setIsDel( arg0.getIsDel() );
        aeaElder.setIsDied( arg0.getIsDied() );
        aeaElder.setKind( arg0.getKind() );
        aeaElder.setMobilePhone( arg0.getMobilePhone() );
        aeaElder.setName( arg0.getName() );
        aeaElder.setNationality( arg0.getNationality() );
        aeaElder.setRecentPhoto( arg0.getRecentPhoto() );
        aeaElder.setResidenceAddressDetail( arg0.getResidenceAddressDetail() );
        aeaElder.setResidenceAddressId( arg0.getResidenceAddressId() );
        aeaElder.setSource( arg0.getSource() );
        aeaElder.setTelephone( arg0.getTelephone() );
        aeaElder.setUpdateBy( arg0.getUpdateBy() );
        aeaElder.setUpdateTime( arg0.getUpdateTime() );
        aeaElder.setWechatPhone( arg0.getWechatPhone() );

        return aeaElder;
    }

    @Override
    public AeaElder convert(AeaElderVo arg0, AeaElder arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setBirthday( arg0.getBirthday() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setDateOfDeath( arg0.getDateOfDeath() );
        arg1.setDeptId( arg0.getDeptId() );
        arg1.setGender( arg0.getGender() );
        arg1.setHouseHoldType( arg0.getHouseHoldType() );
        arg1.setId( arg0.getId() );
        arg1.setIdCardAddressDetail( arg0.getIdCardAddressDetail() );
        arg1.setIdCardAddressId( arg0.getIdCardAddressId() );
        arg1.setIdCardNum( arg0.getIdCardNum() );
        arg1.setIdCardType( arg0.getIdCardType() );
        arg1.setIdPhotos( arg0.getIdPhotos() );
        arg1.setIsDel( arg0.getIsDel() );
        arg1.setIsDied( arg0.getIsDied() );
        arg1.setKind( arg0.getKind() );
        arg1.setMobilePhone( arg0.getMobilePhone() );
        arg1.setName( arg0.getName() );
        arg1.setNationality( arg0.getNationality() );
        arg1.setRecentPhoto( arg0.getRecentPhoto() );
        arg1.setResidenceAddressDetail( arg0.getResidenceAddressDetail() );
        arg1.setResidenceAddressId( arg0.getResidenceAddressId() );
        arg1.setSource( arg0.getSource() );
        arg1.setTelephone( arg0.getTelephone() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setWechatPhone( arg0.getWechatPhone() );

        return arg1;
    }
}
