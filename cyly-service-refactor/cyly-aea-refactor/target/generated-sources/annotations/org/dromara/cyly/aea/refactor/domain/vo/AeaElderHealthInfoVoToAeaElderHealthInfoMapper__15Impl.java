package org.dromara.cyly.aea.refactor.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.cyly.aea.refactor.domain.AeaElderHealthInfo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T09:55:29+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AeaElderHealthInfoVoToAeaElderHealthInfoMapper__15Impl implements AeaElderHealthInfoVoToAeaElderHealthInfoMapper__15 {

    @Override
    public AeaElderHealthInfo convert(AeaElderHealthInfoVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AeaElderHealthInfo aeaElderHealthInfo = new AeaElderHealthInfo();

        aeaElderHealthInfo.setAboBloodType( arg0.getAboBloodType() );
        aeaElderHealthInfo.setAeaElderId( arg0.getAeaElderId() );
        aeaElderHealthInfo.setCanPhoneCommunicate( arg0.getCanPhoneCommunicate() );
        aeaElderHealthInfo.setChronicDiseases( arg0.getChronicDiseases() );
        aeaElderHealthInfo.setCreateBy( arg0.getCreateBy() );
        aeaElderHealthInfo.setCreateTime( arg0.getCreateTime() );
        aeaElderHealthInfo.setDeptId( arg0.getDeptId() );
        aeaElderHealthInfo.setDietHabits( arg0.getDietHabits() );
        aeaElderHealthInfo.setDietPreference( arg0.getDietPreference() );
        aeaElderHealthInfo.setDisabilityCategory( arg0.getDisabilityCategory() );
        aeaElderHealthInfo.setDisabilityLevel( arg0.getDisabilityLevel() );
        aeaElderHealthInfo.setDisabilityType( arg0.getDisabilityType() );
        aeaElderHealthInfo.setDrinkingHabits( arg0.getDrinkingHabits() );
        aeaElderHealthInfo.setDrugAllergy( arg0.getDrugAllergy() );
        aeaElderHealthInfo.setExposureHistory( arg0.getExposureHistory() );
        aeaElderHealthInfo.setGeneticDiseases( arg0.getGeneticDiseases() );
        aeaElderHealthInfo.setHasPensionNeed( arg0.getHasPensionNeed() );
        aeaElderHealthInfo.setIsDel( arg0.getIsDel() );
        aeaElderHealthInfo.setIsDisabled( arg0.getIsDisabled() );
        aeaElderHealthInfo.setIsOccupationalInjury( arg0.getIsOccupationalInjury() );
        aeaElderHealthInfo.setLongTermMedication( arg0.getLongTermMedication() );
        aeaElderHealthInfo.setMajorDiseases( arg0.getMajorDiseases() );
        aeaElderHealthInfo.setMedicalHistory( arg0.getMedicalHistory() );
        aeaElderHealthInfo.setNursingLevel( arg0.getNursingLevel() );
        aeaElderHealthInfo.setPensionNeed( arg0.getPensionNeed() );
        aeaElderHealthInfo.setRecentHospitalized( arg0.getRecentHospitalized() );
        aeaElderHealthInfo.setRecentMedicalVisit( arg0.getRecentMedicalVisit() );
        aeaElderHealthInfo.setRhBloodType( arg0.getRhBloodType() );
        aeaElderHealthInfo.setSmokingStatus( arg0.getSmokingStatus() );
        aeaElderHealthInfo.setSpecialConditions( arg0.getSpecialConditions() );
        aeaElderHealthInfo.setUpdateBy( arg0.getUpdateBy() );
        aeaElderHealthInfo.setUpdateTime( arg0.getUpdateTime() );

        return aeaElderHealthInfo;
    }

    @Override
    public AeaElderHealthInfo convert(AeaElderHealthInfoVo arg0, AeaElderHealthInfo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setAboBloodType( arg0.getAboBloodType() );
        arg1.setAeaElderId( arg0.getAeaElderId() );
        arg1.setCanPhoneCommunicate( arg0.getCanPhoneCommunicate() );
        arg1.setChronicDiseases( arg0.getChronicDiseases() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setDeptId( arg0.getDeptId() );
        arg1.setDietHabits( arg0.getDietHabits() );
        arg1.setDietPreference( arg0.getDietPreference() );
        arg1.setDisabilityCategory( arg0.getDisabilityCategory() );
        arg1.setDisabilityLevel( arg0.getDisabilityLevel() );
        arg1.setDisabilityType( arg0.getDisabilityType() );
        arg1.setDrinkingHabits( arg0.getDrinkingHabits() );
        arg1.setDrugAllergy( arg0.getDrugAllergy() );
        arg1.setExposureHistory( arg0.getExposureHistory() );
        arg1.setGeneticDiseases( arg0.getGeneticDiseases() );
        arg1.setHasPensionNeed( arg0.getHasPensionNeed() );
        arg1.setIsDel( arg0.getIsDel() );
        arg1.setIsDisabled( arg0.getIsDisabled() );
        arg1.setIsOccupationalInjury( arg0.getIsOccupationalInjury() );
        arg1.setLongTermMedication( arg0.getLongTermMedication() );
        arg1.setMajorDiseases( arg0.getMajorDiseases() );
        arg1.setMedicalHistory( arg0.getMedicalHistory() );
        arg1.setNursingLevel( arg0.getNursingLevel() );
        arg1.setPensionNeed( arg0.getPensionNeed() );
        arg1.setRecentHospitalized( arg0.getRecentHospitalized() );
        arg1.setRecentMedicalVisit( arg0.getRecentMedicalVisit() );
        arg1.setRhBloodType( arg0.getRhBloodType() );
        arg1.setSmokingStatus( arg0.getSmokingStatus() );
        arg1.setSpecialConditions( arg0.getSpecialConditions() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );

        return arg1;
    }
}
