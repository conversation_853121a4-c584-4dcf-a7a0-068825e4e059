package org.dromara.cyly.aea.refactor.domain.vo;

import io.github.linpeilie.AutoMapperConfig__356;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.AeaElderRelationship;
import org.dromara.cyly.aea.refactor.domain.AeaElderRelationshipToAeaElderRelationshipVoMapper__20;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__356.class,
    uses = {AeaElderRelationshipToAeaElderRelationshipVoMapper__20.class},
    imports = {}
)
public interface AeaElderRelationshipVoToAeaElderRelationshipMapper__20 extends BaseMapper<AeaElderRelationshipVo, AeaElderRelationship> {
}
