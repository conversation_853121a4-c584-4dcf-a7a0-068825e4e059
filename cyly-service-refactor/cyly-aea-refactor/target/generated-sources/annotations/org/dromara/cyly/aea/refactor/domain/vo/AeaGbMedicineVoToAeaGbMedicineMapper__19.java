package org.dromara.cyly.aea.refactor.domain.vo;

import io.github.linpeilie.AutoMapperConfig__356;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.AeaGbMedicine;
import org.dromara.cyly.aea.refactor.domain.AeaGbMedicineToAeaGbMedicineVoMapper__19;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__356.class,
    uses = {AeaGbMedicineToAeaGbMedicineVoMapper__19.class},
    imports = {}
)
public interface AeaGbMedicineVoToAeaGbMedicineMapper__19 extends BaseMapper<AeaGbMedicineVo, AeaGbMedicine> {
}
