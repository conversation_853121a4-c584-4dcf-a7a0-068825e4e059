package org.dromara.cyly.aea.refactor.domain.vo;

import io.github.linpeilie.AutoMapperConfig__356;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.AeaElderPersonalInfo;
import org.dromara.cyly.aea.refactor.domain.AeaElderPersonalInfoToAeaElderPersonalInfoVoMapper__20;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__356.class,
    uses = {AeaElderPersonalInfoToAeaElderPersonalInfoVoMapper__20.class},
    imports = {}
)
public interface AeaElderPersonalInfoVoToAeaElderPersonalInfoMapper__20 extends BaseMapper<AeaElderPersonalInfoVo, AeaElderPersonalInfo> {
}
