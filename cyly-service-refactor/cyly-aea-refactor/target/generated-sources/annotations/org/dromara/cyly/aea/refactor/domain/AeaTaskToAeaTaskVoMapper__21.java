package org.dromara.cyly.aea.refactor.domain;

import io.github.linpeilie.AutoMapperConfig__356;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.vo.AeaTaskVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaTaskVoToAeaTaskMapper__21;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__356.class,
    uses = {AeaTaskVoToAeaTaskMapper__21.class},
    imports = {}
)
public interface AeaTaskToAeaTaskVoMapper__21 extends BaseMapper<AeaTask, AeaTaskVo> {
}
