package org.dromara.cyly.aea.refactor.domain.vo;

import io.github.linpeilie.AutoMapperConfig__356;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.AeaAssessmentRecord;
import org.dromara.cyly.aea.refactor.domain.AeaAssessmentRecordToAeaAssessmentRecordVoMapper__26;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__356.class,
    uses = {AeaAssessmentRecordToAeaAssessmentRecordVoMapper__26.class},
    imports = {}
)
public interface AeaAssessmentRecordVoToAeaAssessmentRecordMapper__26 extends BaseMapper<AeaAssessmentRecordVo, AeaAssessmentRecord> {
}
