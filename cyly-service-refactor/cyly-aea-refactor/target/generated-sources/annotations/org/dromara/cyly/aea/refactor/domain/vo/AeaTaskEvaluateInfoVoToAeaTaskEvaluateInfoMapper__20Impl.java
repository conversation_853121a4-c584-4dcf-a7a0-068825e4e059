package org.dromara.cyly.aea.refactor.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.cyly.aea.refactor.domain.AeaTaskEvaluateInfo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T10:14:26+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AeaTaskEvaluateInfoVoToAeaTaskEvaluateInfoMapper__20Impl implements AeaTaskEvaluateInfoVoToAeaTaskEvaluateInfoMapper__20 {

    @Override
    public AeaTaskEvaluateInfo convert(AeaTaskEvaluateInfoVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AeaTaskEvaluateInfo aeaTaskEvaluateInfo = new AeaTaskEvaluateInfo();

        aeaTaskEvaluateInfo.setAssessorEvaluateUserId( arg0.getAssessorEvaluateUserId() );
        aeaTaskEvaluateInfo.setCreateBy( arg0.getCreateBy() );
        aeaTaskEvaluateInfo.setCreateTime( arg0.getCreateTime() );
        aeaTaskEvaluateInfo.setElderId( arg0.getElderId() );
        aeaTaskEvaluateInfo.setEndTime( arg0.getEndTime() );
        aeaTaskEvaluateInfo.setId( arg0.getId() );
        aeaTaskEvaluateInfo.setIsDel( arg0.getIsDel() );
        aeaTaskEvaluateInfo.setQuestionnaireId( arg0.getQuestionnaireId() );
        aeaTaskEvaluateInfo.setStartTime( arg0.getStartTime() );
        aeaTaskEvaluateInfo.setStatus( arg0.getStatus() );
        aeaTaskEvaluateInfo.setTaskId( arg0.getTaskId() );
        aeaTaskEvaluateInfo.setTotalScore( arg0.getTotalScore() );
        aeaTaskEvaluateInfo.setUpdateBy( arg0.getUpdateBy() );
        aeaTaskEvaluateInfo.setUpdateTime( arg0.getUpdateTime() );
        aeaTaskEvaluateInfo.setViceEvaluateUserId( arg0.getViceEvaluateUserId() );

        return aeaTaskEvaluateInfo;
    }

    @Override
    public AeaTaskEvaluateInfo convert(AeaTaskEvaluateInfoVo arg0, AeaTaskEvaluateInfo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setAssessorEvaluateUserId( arg0.getAssessorEvaluateUserId() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setElderId( arg0.getElderId() );
        arg1.setEndTime( arg0.getEndTime() );
        arg1.setId( arg0.getId() );
        arg1.setIsDel( arg0.getIsDel() );
        arg1.setQuestionnaireId( arg0.getQuestionnaireId() );
        arg1.setStartTime( arg0.getStartTime() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setTaskId( arg0.getTaskId() );
        arg1.setTotalScore( arg0.getTotalScore() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setViceEvaluateUserId( arg0.getViceEvaluateUserId() );

        return arg1;
    }
}
