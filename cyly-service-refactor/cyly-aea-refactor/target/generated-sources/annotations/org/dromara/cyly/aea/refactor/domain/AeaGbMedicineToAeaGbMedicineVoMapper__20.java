package org.dromara.cyly.aea.refactor.domain;

import io.github.linpeilie.AutoMapperConfig__356;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.bo.AeaGbMedicineBoToAeaGbMedicineMapper__20;
import org.dromara.cyly.aea.refactor.domain.vo.AeaGbMedicineVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaGbMedicineVoToAeaGbMedicineMapper__20;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__356.class,
    uses = {AeaGbMedicineVoToAeaGbMedicineMapper__20.class,AeaGbMedicineBoToAeaGbMedicineMapper__20.class},
    imports = {}
)
public interface AeaGbMedicineToAeaGbMedicineVoMapper__20 extends BaseMapper<AeaGbMedicine, AeaGbMedicineVo> {
}
