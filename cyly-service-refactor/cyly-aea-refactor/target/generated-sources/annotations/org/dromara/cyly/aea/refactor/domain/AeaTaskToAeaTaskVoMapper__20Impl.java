package org.dromara.cyly.aea.refactor.domain;

import javax.annotation.processing.Generated;
import org.dromara.cyly.aea.refactor.domain.vo.AeaTaskVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T10:14:26+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AeaTaskToAeaTaskVoMapper__20Impl implements AeaTaskToAeaTaskVoMapper__20 {

    @Override
    public AeaTaskVo convert(AeaTask arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AeaTaskVo aeaTaskVo = new AeaTaskVo();

        aeaTaskVo.setAuditStatus( arg0.getAuditStatus() );
        aeaTaskVo.setCreateBy( arg0.getCreateBy() );
        aeaTaskVo.setCreateTime( arg0.getCreateTime() );
        aeaTaskVo.setDeptId( arg0.getDeptId() );
        aeaTaskVo.setEndTime( arg0.getEndTime() );
        aeaTaskVo.setExpectedEndTime( arg0.getExpectedEndTime() );
        aeaTaskVo.setExpectedStartTime( arg0.getExpectedStartTime() );
        aeaTaskVo.setId( arg0.getId() );
        aeaTaskVo.setIsDel( arg0.getIsDel() );
        aeaTaskVo.setStartTime( arg0.getStartTime() );
        aeaTaskVo.setStatus( arg0.getStatus() );
        aeaTaskVo.setTaskType( arg0.getTaskType() );
        aeaTaskVo.setUpdateBy( arg0.getUpdateBy() );
        aeaTaskVo.setUpdateTime( arg0.getUpdateTime() );

        return aeaTaskVo;
    }

    @Override
    public AeaTaskVo convert(AeaTask arg0, AeaTaskVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setAuditStatus( arg0.getAuditStatus() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setDeptId( arg0.getDeptId() );
        arg1.setEndTime( arg0.getEndTime() );
        arg1.setExpectedEndTime( arg0.getExpectedEndTime() );
        arg1.setExpectedStartTime( arg0.getExpectedStartTime() );
        arg1.setId( arg0.getId() );
        arg1.setIsDel( arg0.getIsDel() );
        arg1.setStartTime( arg0.getStartTime() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setTaskType( arg0.getTaskType() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );

        return arg1;
    }
}
