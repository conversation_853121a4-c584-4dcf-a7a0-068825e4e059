package org.dromara.cyly.aea.refactor.domain.bo;

import io.github.linpeilie.AutoMapperConfig__356;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.AeaAssessmentResult;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__356.class,
    uses = {},
    imports = {}
)
public interface AeaAssessmentResultBoToAeaAssessmentResultMapper__22 extends BaseMapper<AeaAssessmentResultBo, AeaAssessmentResult> {
}
