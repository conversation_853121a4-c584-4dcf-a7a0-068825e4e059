package org.dromara.cyly.aea.refactor.domain.vo;

import io.github.linpeilie.AutoMapperConfig__356;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.AeaElderPersonalInfo;
import org.dromara.cyly.aea.refactor.domain.AeaElderPersonalInfoToAeaElderPersonalInfoVoMapper__21;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__356.class,
    uses = {AeaElderPersonalInfoToAeaElderPersonalInfoVoMapper__21.class},
    imports = {}
)
public interface AeaElderPersonalInfoVoToAeaElderPersonalInfoMapper__21 extends BaseMapper<AeaElderPersonalInfoVo, AeaElderPersonalInfo> {
}
