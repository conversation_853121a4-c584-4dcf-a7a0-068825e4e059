package org.dromara.cyly.aea.refactor.domain;

import javax.annotation.processing.Generated;
import org.dromara.cyly.aea.refactor.domain.vo.AeaElderVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T10:03:53+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AeaElderToAeaElderVoMapper__15Impl implements AeaElderToAeaElderVoMapper__15 {

    @Override
    public AeaElderVo convert(AeaElder arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AeaElderVo aeaElderVo = new AeaElderVo();

        aeaElderVo.setBirthday( arg0.getBirthday() );
        aeaElderVo.setCreateBy( arg0.getCreateBy() );
        aeaElderVo.setCreateTime( arg0.getCreateTime() );
        aeaElderVo.setDateOfDeath( arg0.getDateOfDeath() );
        aeaElderVo.setDeptId( arg0.getDeptId() );
        aeaElderVo.setGender( arg0.getGender() );
        aeaElderVo.setHouseHoldType( arg0.getHouseHoldType() );
        aeaElderVo.setId( arg0.getId() );
        aeaElderVo.setIdCardAddressDetail( arg0.getIdCardAddressDetail() );
        aeaElderVo.setIdCardAddressId( arg0.getIdCardAddressId() );
        aeaElderVo.setIdCardNum( arg0.getIdCardNum() );
        aeaElderVo.setIdCardType( arg0.getIdCardType() );
        aeaElderVo.setIdPhotos( arg0.getIdPhotos() );
        aeaElderVo.setIsDel( arg0.getIsDel() );
        aeaElderVo.setIsDied( arg0.getIsDied() );
        aeaElderVo.setKind( arg0.getKind() );
        aeaElderVo.setMobilePhone( arg0.getMobilePhone() );
        aeaElderVo.setName( arg0.getName() );
        aeaElderVo.setNationality( arg0.getNationality() );
        aeaElderVo.setRecentPhoto( arg0.getRecentPhoto() );
        aeaElderVo.setResidenceAddressDetail( arg0.getResidenceAddressDetail() );
        aeaElderVo.setResidenceAddressId( arg0.getResidenceAddressId() );
        aeaElderVo.setSource( arg0.getSource() );
        aeaElderVo.setTelephone( arg0.getTelephone() );
        aeaElderVo.setUpdateBy( arg0.getUpdateBy() );
        aeaElderVo.setUpdateTime( arg0.getUpdateTime() );
        aeaElderVo.setWechatPhone( arg0.getWechatPhone() );

        return aeaElderVo;
    }

    @Override
    public AeaElderVo convert(AeaElder arg0, AeaElderVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setBirthday( arg0.getBirthday() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setDateOfDeath( arg0.getDateOfDeath() );
        arg1.setDeptId( arg0.getDeptId() );
        arg1.setGender( arg0.getGender() );
        arg1.setHouseHoldType( arg0.getHouseHoldType() );
        arg1.setId( arg0.getId() );
        arg1.setIdCardAddressDetail( arg0.getIdCardAddressDetail() );
        arg1.setIdCardAddressId( arg0.getIdCardAddressId() );
        arg1.setIdCardNum( arg0.getIdCardNum() );
        arg1.setIdCardType( arg0.getIdCardType() );
        arg1.setIdPhotos( arg0.getIdPhotos() );
        arg1.setIsDel( arg0.getIsDel() );
        arg1.setIsDied( arg0.getIsDied() );
        arg1.setKind( arg0.getKind() );
        arg1.setMobilePhone( arg0.getMobilePhone() );
        arg1.setName( arg0.getName() );
        arg1.setNationality( arg0.getNationality() );
        arg1.setRecentPhoto( arg0.getRecentPhoto() );
        arg1.setResidenceAddressDetail( arg0.getResidenceAddressDetail() );
        arg1.setResidenceAddressId( arg0.getResidenceAddressId() );
        arg1.setSource( arg0.getSource() );
        arg1.setTelephone( arg0.getTelephone() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setWechatPhone( arg0.getWechatPhone() );

        return arg1;
    }
}
