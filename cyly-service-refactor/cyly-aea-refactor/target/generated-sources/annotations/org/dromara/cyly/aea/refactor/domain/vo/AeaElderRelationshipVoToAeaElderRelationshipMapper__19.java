package org.dromara.cyly.aea.refactor.domain.vo;

import io.github.linpeilie.AutoMapperConfig__356;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.AeaElderRelationship;
import org.dromara.cyly.aea.refactor.domain.AeaElderRelationshipToAeaElderRelationshipVoMapper__19;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__356.class,
    uses = {AeaElderRelationshipToAeaElderRelationshipVoMapper__19.class},
    imports = {}
)
public interface AeaElderRelationshipVoToAeaElderRelationshipMapper__19 extends BaseMapper<AeaElderRelationshipVo, AeaElderRelationship> {
}
