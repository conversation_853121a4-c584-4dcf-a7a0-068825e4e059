package org.dromara.cyly.aea.refactor.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.cyly.aea.refactor.domain.AeaTask;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T09:51:46+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AeaTaskVoToAeaTaskMapper__15Impl implements AeaTaskVoToAeaTaskMapper__15 {

    @Override
    public AeaTask convert(AeaTaskVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AeaTask aeaTask = new AeaTask();

        aeaTask.setAuditStatus( arg0.getAuditStatus() );
        aeaTask.setCreateBy( arg0.getCreateBy() );
        aeaTask.setCreateTime( arg0.getCreateTime() );
        aeaTask.setDeptId( arg0.getDeptId() );
        aeaTask.setEndTime( arg0.getEndTime() );
        aeaTask.setExpectedEndTime( arg0.getExpectedEndTime() );
        aeaTask.setExpectedStartTime( arg0.getExpectedStartTime() );
        aeaTask.setId( arg0.getId() );
        aeaTask.setIsDel( arg0.getIsDel() );
        aeaTask.setStartTime( arg0.getStartTime() );
        aeaTask.setStatus( arg0.getStatus() );
        aeaTask.setTaskType( arg0.getTaskType() );
        aeaTask.setUpdateBy( arg0.getUpdateBy() );
        aeaTask.setUpdateTime( arg0.getUpdateTime() );

        return aeaTask;
    }

    @Override
    public AeaTask convert(AeaTaskVo arg0, AeaTask arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setAuditStatus( arg0.getAuditStatus() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setDeptId( arg0.getDeptId() );
        arg1.setEndTime( arg0.getEndTime() );
        arg1.setExpectedEndTime( arg0.getExpectedEndTime() );
        arg1.setExpectedStartTime( arg0.getExpectedStartTime() );
        arg1.setId( arg0.getId() );
        arg1.setIsDel( arg0.getIsDel() );
        arg1.setStartTime( arg0.getStartTime() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setTaskType( arg0.getTaskType() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );

        return arg1;
    }
}
