package org.dromara.cyly.aea.refactor.domain;

import javax.annotation.processing.Generated;
import org.dromara.cyly.aea.refactor.domain.vo.AeaTaskEvaluateInfoVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T10:14:26+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AeaTaskEvaluateInfoToAeaTaskEvaluateInfoVoMapper__20Impl implements AeaTaskEvaluateInfoToAeaTaskEvaluateInfoVoMapper__20 {

    @Override
    public AeaTaskEvaluateInfoVo convert(AeaTaskEvaluateInfo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AeaTaskEvaluateInfoVo aeaTaskEvaluateInfoVo = new AeaTaskEvaluateInfoVo();

        aeaTaskEvaluateInfoVo.setAssessorEvaluateUserId( arg0.getAssessorEvaluateUserId() );
        aeaTaskEvaluateInfoVo.setCreateBy( arg0.getCreateBy() );
        aeaTaskEvaluateInfoVo.setCreateTime( arg0.getCreateTime() );
        aeaTaskEvaluateInfoVo.setElderId( arg0.getElderId() );
        aeaTaskEvaluateInfoVo.setEndTime( arg0.getEndTime() );
        aeaTaskEvaluateInfoVo.setId( arg0.getId() );
        aeaTaskEvaluateInfoVo.setIsDel( arg0.getIsDel() );
        aeaTaskEvaluateInfoVo.setQuestionnaireId( arg0.getQuestionnaireId() );
        aeaTaskEvaluateInfoVo.setStartTime( arg0.getStartTime() );
        aeaTaskEvaluateInfoVo.setStatus( arg0.getStatus() );
        aeaTaskEvaluateInfoVo.setTaskId( arg0.getTaskId() );
        aeaTaskEvaluateInfoVo.setTotalScore( arg0.getTotalScore() );
        aeaTaskEvaluateInfoVo.setUpdateBy( arg0.getUpdateBy() );
        aeaTaskEvaluateInfoVo.setUpdateTime( arg0.getUpdateTime() );
        aeaTaskEvaluateInfoVo.setViceEvaluateUserId( arg0.getViceEvaluateUserId() );

        return aeaTaskEvaluateInfoVo;
    }

    @Override
    public AeaTaskEvaluateInfoVo convert(AeaTaskEvaluateInfo arg0, AeaTaskEvaluateInfoVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setAssessorEvaluateUserId( arg0.getAssessorEvaluateUserId() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setElderId( arg0.getElderId() );
        arg1.setEndTime( arg0.getEndTime() );
        arg1.setId( arg0.getId() );
        arg1.setIsDel( arg0.getIsDel() );
        arg1.setQuestionnaireId( arg0.getQuestionnaireId() );
        arg1.setStartTime( arg0.getStartTime() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setTaskId( arg0.getTaskId() );
        arg1.setTotalScore( arg0.getTotalScore() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setViceEvaluateUserId( arg0.getViceEvaluateUserId() );

        return arg1;
    }
}
