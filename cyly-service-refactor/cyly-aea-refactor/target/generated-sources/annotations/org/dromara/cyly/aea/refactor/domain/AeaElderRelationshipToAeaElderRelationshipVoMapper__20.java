package org.dromara.cyly.aea.refactor.domain;

import io.github.linpeilie.AutoMapperConfig__356;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.vo.AeaElderRelationshipVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaElderRelationshipVoToAeaElderRelationshipMapper__20;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__356.class,
    uses = {AeaElderRelationshipVoToAeaElderRelationshipMapper__20.class},
    imports = {}
)
public interface AeaElderRelationshipToAeaElderRelationshipVoMapper__20 extends BaseMapper<AeaElderRelationship, AeaElderRelationshipVo> {
}
