package org.dromara.cyly.aea.refactor.domain;

import io.github.linpeilie.AutoMapperConfig__356;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.bo.AeaGbMedicineBoToAeaGbMedicineMapper__19;
import org.dromara.cyly.aea.refactor.domain.vo.AeaGbMedicineVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaGbMedicineVoToAeaGbMedicineMapper__19;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__356.class,
    uses = {AeaGbMedicineVoToAeaGbMedicineMapper__19.class,AeaGbMedicineBoToAeaGbMedicineMapper__19.class},
    imports = {}
)
public interface AeaGbMedicineToAeaGbMedicineVoMapper__19 extends BaseMapper<AeaGbMedicine, AeaGbMedicineVo> {
}
