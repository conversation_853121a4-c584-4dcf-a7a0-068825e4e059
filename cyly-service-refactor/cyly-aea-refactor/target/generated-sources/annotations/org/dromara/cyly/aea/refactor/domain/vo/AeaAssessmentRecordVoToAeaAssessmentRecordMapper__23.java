package org.dromara.cyly.aea.refactor.domain.vo;

import io.github.linpeilie.AutoMapperConfig__356;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.AeaAssessmentRecord;
import org.dromara.cyly.aea.refactor.domain.AeaAssessmentRecordToAeaAssessmentRecordVoMapper__23;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__356.class,
    uses = {AeaAssessmentRecordToAeaAssessmentRecordVoMapper__23.class},
    imports = {}
)
public interface AeaAssessmentRecordVoToAeaAssessmentRecordMapper__23 extends BaseMapper<AeaAssessmentRecordVo, AeaAssessmentRecord> {
}
