package org.dromara.cyly.xchat.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.cyly.xchat.domain.ChatLog;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T09:20:59+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ChatLogBoToChatLogMapperImpl implements ChatLogBoToChatLogMapper {

    @Override
    public ChatLog convert(ChatLogBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ChatLog chatLog = new ChatLog();

        chatLog.setCreateBy( arg0.getCreateBy() );
        chatLog.setCreateDept( arg0.getCreateDept() );
        chatLog.setCreateTime( arg0.getCreateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            chatLog.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        chatLog.setSearchValue( arg0.getSearchValue() );
        chatLog.setUpdateBy( arg0.getUpdateBy() );
        chatLog.setUpdateTime( arg0.getUpdateTime() );
        chatLog.setClassType( arg0.getClassType() );
        chatLog.setContent( arg0.getContent() );
        chatLog.setExt( arg0.getExt() );
        chatLog.setFromAccount( arg0.getFromAccount() );
        chatLog.setFromDeviceId( arg0.getFromDeviceId() );
        chatLog.setFromNick( arg0.getFromNick() );
        chatLog.setId( arg0.getId() );
        chatLog.setIdClient( arg0.getIdClient() );
        chatLog.setIdServer( arg0.getIdServer() );
        chatLog.setScene( arg0.getScene() );
        chatLog.setStatus( arg0.getStatus() );
        chatLog.setToAccept( arg0.getToAccept() );
        chatLog.setToAvatar( arg0.getToAvatar() );
        chatLog.setToName( arg0.getToName() );
        chatLog.setType( arg0.getType() );

        return chatLog;
    }

    @Override
    public ChatLog convert(ChatLogBo arg0, ChatLog arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateTime( arg0.getCreateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setClassType( arg0.getClassType() );
        arg1.setContent( arg0.getContent() );
        arg1.setExt( arg0.getExt() );
        arg1.setFromAccount( arg0.getFromAccount() );
        arg1.setFromDeviceId( arg0.getFromDeviceId() );
        arg1.setFromNick( arg0.getFromNick() );
        arg1.setId( arg0.getId() );
        arg1.setIdClient( arg0.getIdClient() );
        arg1.setIdServer( arg0.getIdServer() );
        arg1.setScene( arg0.getScene() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setToAccept( arg0.getToAccept() );
        arg1.setToAvatar( arg0.getToAvatar() );
        arg1.setToName( arg0.getToName() );
        arg1.setType( arg0.getType() );

        return arg1;
    }
}
