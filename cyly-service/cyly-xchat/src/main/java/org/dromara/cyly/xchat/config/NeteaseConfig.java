package org.dromara.cyly.xchat.config;

import lombok.Data;
import org.dromara.cyly.xchat.auth.BasicCredentials;
import org.dromara.cyly.xchat.client.NeteaseClient;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Spring配置类，用于配置网易云服务的凭证信息。
 * 通过{@link ConfigurationProperties}注解绑定配置文件中前缀为"netease"的属性，
 * 包含应用的API密钥和密钥凭证。
 */
@Configuration
@ConfigurationProperties(prefix = "netease")
@Data
public class NeteaseConfig {
    private String appKey;
    private String appSecret;

    /**
     * 创建并返回一个NeteaseClient实例，使用配置的凭证进行身份验证。
     * @return 配置好的NeteaseClient对象，用于与网易云服务进行交互。
     */
    @Bean
    public NeteaseClient neteaseClient() {
        BasicCredentials basicCredentials = new BasicCredentials(getAppKey(), getAppSecret());
        return new NeteaseClient(basicCredentials);
    }
}
