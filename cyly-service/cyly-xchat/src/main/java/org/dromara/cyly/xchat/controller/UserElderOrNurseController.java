package org.dromara.cyly.xchat.controller;


import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.satoken.utils.AppLoginHelper;
import org.dromara.common.web.core.BaseController;
import org.dromara.cyly.xchat.domain.bo.UserElderBo;
import org.dromara.cyly.xchat.domain.bo.UserNurseBo;
import org.dromara.cyly.xchat.service.IUserElderOrNurseService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @version 1.0
 * @author: xjf
 * @date: 2024/11/25 14:33
 * 描述：用户与长者与护理员绑定控制层
 */
@RestController
@Validated
@RequiredArgsConstructor
@RequestMapping("/userElderOrNurse")
public class UserElderOrNurseController extends BaseController {
    private final IUserElderOrNurseService userElderOrNurseService;

    /**
     * 添加亲属与长者绑定
     *
     * @param userElderBo 亲属与长者信息对象
     * @return 结果
     */
    @PostMapping("/usrapi/addAppUserElder")
    public R<Boolean> addUserElder(@Validated(AddGroup.class) @RequestBody UserElderBo userElderBo) {
        // 插入用户长者关联信息，并根据插入结果更新登录用户信息
        boolean flag = userElderOrNurseService.insertUserElder(userElderBo, AppLoginHelper.getUserId());
        // 返回插入操作的结果
        if (flag){
            return R.ok("添加成功");
        }else {
            return R.fail("添加失败");
        }
    }

    /**
     * 添加用户与护理员绑定
     *
     * @param userNurseBo 用户与护理员对象
     * @return 结果
     */
    @PostMapping("/usrapi/addAppUserNurse")
    public R<Boolean> addUserElder(@Validated(AddGroup.class) @RequestBody UserNurseBo userNurseBo) {
        // 插入用户护士关联信息，并根据插入结果更新登录用户信息
        boolean flag = userElderOrNurseService.insertAppUserNurse(userNurseBo, AppLoginHelper.getUserId());
        // 返回插入操作的结果
        if (flag){
            return R.ok("添加成功");
        }else {
            return R.fail("添加失败");
        }
    }
}
