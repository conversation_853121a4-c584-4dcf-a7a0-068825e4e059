package org.dromara.cyly.xchat.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.cyly.xchat.domain.ChatUserIm;
import org.dromara.cyly.xchat.domain.bo.ChatUserImBo;
import org.dromara.cyly.xchat.domain.vo.ChatUserImVo;
import org.dromara.cyly.xchat.mapper.ChatUserImMapper;
import org.dromara.cyly.xchat.service.IChatUserImService;
import org.dromara.xchat.api.model.RemoteNeteaseClient;
import org.dromara.system.api.RemoteAppUserService;
import org.dromara.system.api.domain.vo.RemoteAppUserVo;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * IM用户信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-26
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ChatUserImServiceImpl implements IChatUserImService {
    private final ChatUserImMapper chatUserImMapper;

    @DubboReference
    private RemoteNeteaseClient remoteNeteaseClient;
    @DubboReference
    private RemoteAppUserService remoteAppUserService;

    /**
     * 查询IM用户信息
     *
     * @param accid IM用户信息主键
     * @return IM用户信息
     */
    @Override
    public ChatUserIm selectChatUserImByAccid(String accid) {
        return chatUserImMapper.selectById(accid);
    }

    /**
     * 根据提供的业务对象选择聊天用户IM视图对象
     * 此方法用于封装查询逻辑，将查询条件封装在业务对象中，通过映射转换为查询条件，最终获取对应的视图对象
     *
     * @param bo 聊天用户IM业务对象，包含查询所需的各种条件字段
     * @return 返回查询到的聊天用户IM视图对象，如果没有找到匹配的记录，则返回null
     */
    @Override
    public ChatUserImVo selectChatUserImVo(ChatUserImBo bo){
        // 构建查询包装器，将业务对象中的查询条件转换为数据库查询条件
        LambdaQueryWrapper<ChatUserIm> lqw = buildQueryWrapper(bo);
        // 使用构建好的查询条件，从数据库中选择并返回唯一的聊天用户IM视图对象
        return chatUserImMapper.selectVoOne(lqw);
    }



    private LambdaQueryWrapper<ChatUserIm> buildQueryWrapper(ChatUserImBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ChatUserIm> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getAccid()), ChatUserIm::getAccid, bo.getAccid());
        lqw.eq(StringUtils.isNotBlank(bo.getName()), ChatUserIm::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getMobile()), ChatUserIm::getMobile, bo.getMobile());
        lqw.eq(StringUtils.isNotBlank(bo.getEmail()), ChatUserIm::getEmail, bo.getEmail());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ChatUserIm::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getIcon()), ChatUserIm::getIcon, bo.getIcon());
        lqw.eq(StringUtils.isNotBlank(bo.getSign()), ChatUserIm::getSign, bo.getSign());
        lqw.eq(StringUtils.isNotBlank(bo.getUserType()), ChatUserIm::getUserType, bo.getUserType());
        lqw.eq(StringUtils.isNotBlank(bo.getNickName()), ChatUserIm::getNickName, bo.getNickName());
        lqw.eq(StringUtils.isNotBlank(bo.getBirth()), ChatUserIm::getBirth, bo.getBirth());
        lqw.eq(StringUtils.isNotBlank(bo.getUserType()), ChatUserIm::getUserType, bo.getUserType());
        lqw.eq(bo.getAppUserId()!=null, ChatUserIm::getUserId, bo.getAppUserId());
        return lqw;
    }

    /**
     * 查询IM用户信息列表
     *
     * @param ChatUserIm IM用户信息
     * @return IM用户信息
     */
    @Override
    public TableDataInfo<ChatUserIm> selectChatUserImList(ChatUserIm ChatUserIm, PageQuery pageQuery) {
        Page<ChatUserIm> ChatUserImPage = chatUserImMapper.selectSysUserImList(pageQuery.build(), ChatUserIm);
        return TableDataInfo.build(ChatUserImPage);
    }

    /**
     * 获取im用户及app用户信息
     */
    @Override
    public ChatUserImVo getUserImInfo(String accId, Long appUserId) {
        // 获取im用户及app用户信息
        ChatUserImVo ChatUserImVo = chatUserImMapper.selectVoById(accId);
        RemoteAppUserVo appUserVo = remoteAppUserService.selectVoByAppUserId(appUserId);
        if (ChatUserImVo!=null && appUserVo!=null){
            MapstructUtils.convert(ChatUserImVo, appUserVo);
            ChatUserImVo.setAppUser(appUserVo);
        }
        return ChatUserImVo;
    }

    /**
     * 新增IM用户信息
     *
     * @param ChatUserIm IM用户信息
     * @return 结果
     */
    @Override
    public int insertChatUserIm(ChatUserIm ChatUserIm) {
        ChatUserIm.setCreateTime(DateUtils.getNowDate());
        return chatUserImMapper.insert(ChatUserIm);
    }

    /**
     * 修改IM用户信息
     *
     * @param ChatUserIm IM用户信息
     * @return 结果
     */
    @Override
    public int updateChatUserIm(ChatUserIm ChatUserIm) {
        ChatUserIm.setUpdateTime(DateUtils.getNowDate());
        return chatUserImMapper.updateById(ChatUserIm);
    }

    /**
     * 批量删除IM用户信息
     *
     * @param accids 需要删除的IM用户信息主键
     * @return 结果
     */
    @Override
    public int deleteChatUserImByAccids(String[] accids) {
        return chatUserImMapper.deleteByIds(Arrays.asList(accids));
    }

    /**
     * 删除IM用户信息信息
     *
     * @param accid IM用户信息主键
     * @return 结果
     */
    @Override
    public int deleteChatUserImByAccid(String accid) {
        return chatUserImMapper.deleteById(accid);
    }

    /**
     * 根据手机号码查询用户信息
     *
     * @param phone 手机号码，用于查询用户信息
     * @return 返回一个封装了查询结果的R对象如果找到对应的用户信息，则返回成功结果；
     *         否则，返回失败结果，并附带错误信息“未查询到用户！”
     */
    @Override
    public ChatUserImVo selectUserByPhoneNumber(String phone) {
        // 创建一个LambdaQueryWrapper对象，用于构建查询条件
        LambdaQueryWrapper<ChatUserIm> queryWrapper = new LambdaQueryWrapper<>();
        // 设置查询条件：根据手机号码查询
        queryWrapper.eq(ChatUserIm::getMobile, phone);
        // 调用Mapper方法，执行查询，获取用户信息
        return chatUserImMapper.selectVoOne(queryWrapper);
    }

    /**
     * 选择所有聊天用户IM列表
     *
     * @param deptId 部门ID，用于筛选属于特定部门的用户
     * @param accid 账号ID，用于排除指定账号的用户
     * @return 返回一个ChatUserImVo对象列表，包含符合条件的用户信息，如果列表为空则返回null
     */
    @Override
    public List<ChatUserImVo> selectAllChatUserImList(Long deptId, String accid) {
        // 创建一个Lambda查询包装器，用于构建查询条件
        LambdaQueryWrapper<ChatUserIm> queryWrapper = new LambdaQueryWrapper<>();
        // 添加查询条件：等于部门ID
        queryWrapper.eq(ChatUserIm::getDeptId, deptId);
        // 添加查询条件：不等于账号ID，即排除指定的用户
        queryWrapper.ne(ChatUserIm::getAccid, accid);
        // 调用Mapper方法，获取ChatUserImVo对象列表
        List<ChatUserImVo> chatUserImList = chatUserImMapper.selectVoList(queryWrapper);
        // 检查列表是否非空，如果列表存在且不为空，则返回列表，否则返回null
        if (chatUserImList != null && !chatUserImList.isEmpty()){
            return chatUserImList;
        }
        return null;
    }


    /**
     * 刷新用户token
     * @param accId 用户账号ID，用于标识需要刷新token的用户
     * @return 包含操作结果的Map对象，通常包含以下键：
     *         - code: 状态码（200表示成功，500表示内部错误）
     *         - message: 错误信息（当code非200时存在）
     *         - 其他可能的数据字段（如token信息）
     */
    @Override
    public Map<String, Object> refreshToken(String accId) {
        Map<String, Object> map;
        try {
            // 调用远程服务刷新用户token
            map = remoteNeteaseClient.refreshUserToken(accId);

            // 检查响应状态码是否为成功（200）
            if (map != null && Objects.equals(map.get("code"), 200)) {
                Object infoObj = map.get("info");
                ChatUserIm ChatUserIm = new ChatUserIm();
                ChatUserIm.setAccid(accId);

                // 根据info字段类型解析并设置用户token
                if (infoObj instanceof String) {
                    ChatUserIm.setToken((String) infoObj);
                } else if (infoObj instanceof Map) {
                    Map<String, Object> infoMap = (Map<String, Object>) infoObj;
                    ChatUserIm.setToken((String) infoMap.get("token"));
                } else {
                    log.error("响应中检测到未知的'info'类型：{}", infoObj.getClass().getName());
                    return Map.of("code", 500, "message", "内部错误");
                }

                // 尝试更新数据库中用户的token信息
                int rowsAffected = chatUserImMapper.updateById(ChatUserIm);
                if (rowsAffected == 0) {
                    log.warn("账号ID[{}]的token更新失败", accId);
                }
            } else {
                // 处理非成功的响应状态码
                log.error("token刷新失败，状态码：{}", map != null ? map.get("code") : "未知");
            }
        } catch (Exception e) {
            // 捕获并记录刷新过程中发生的异常
            log.error("账号ID[{}]的token刷新发生异常", accId, e);
            return Map.of("code", 500, "message", "内部错误");
        }
        return map != null ? map : new HashMap<>();
    }

}
