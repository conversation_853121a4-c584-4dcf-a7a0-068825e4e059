package org.dromara.cyly.xchat.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.satoken.utils.AppLoginHelper;
import org.dromara.cyly.xchat.client.ImTokenServer;
import org.dromara.cyly.xchat.domain.bo.ChatUserImBo;
import org.dromara.cyly.xchat.domain.vo.ChatUserImVo;
import org.dromara.cyly.xchat.service.IChatUserImService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;

/**
 * IM音视频token服务
 * &#064;  2025年4月27日 21:20:32
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@Slf4j
@RequestMapping("/imToken")
public class ImTokenController {

    private final ImTokenServer imTokenServer;
    private final IChatUserImService chatUserImService;


    /**
     * 获取音视频通话Token(用户层)
     *
     * @param channelName 频道名称
     * @param ttlSec     token有效期（秒）
     */
    @GetMapping("/usrapi/getToken")
    public R<String> getToken(@NotBlank(message = "频道名称不能为空") @RequestParam("channelName") String channelName,
                             @Min(value = 1, message = "有效期必须大于0") @RequestParam(value = "ttlSec", required = false, defaultValue = "3600") Integer ttlSec) {
        try {
            String token = imTokenServer.getToken(channelName, selectAccIdByUserId(), ttlSec);
            return R.ok(token);
        } catch (Exception e) {
            log.error("获取Token失败",e);
            return R.fail("获取Token失败：" + e.getMessage());
        }
    }

    /**
     * 获取权限密钥(用户层)
     *
     * @param channelName 频道名称
     * @param permSecret  权限密钥
     * @param privilege  权限值（8位二进制数，每位代表一个权限）
     * @param ttlSec     有效期（秒）
     */
    @GetMapping("/usrapi/getPermissionKey")
    public R<String> getPermissionKey(@NotBlank(message = "频道名称不能为空") @RequestParam("channelName") String channelName,
                                    @NotBlank(message = "权限密钥不能为空") @RequestParam("permSecret") String permSecret,
                                    @NotNull(message = "权限值不能为空") @RequestParam("privilege") Byte privilege,
                                    @Min(value = 1, message = "有效期必须大于0") @RequestParam(value = "ttlSec", required = false, defaultValue = "3600") Long ttlSec) {
        try {
            String permissionKey = imTokenServer.getPermissionKey(channelName, permSecret, selectAccIdByUserId(), privilege, ttlSec);
            return R.ok(permissionKey);
        } catch (Exception e) {
            return R.fail("获取权限密钥失败：" + e.getMessage());
        }
    }

    public Long selectAccIdByUserId() {
        ChatUserImBo chatUserImBo = new ChatUserImBo();
        chatUserImBo.setAppUserId(AppLoginHelper.getUserId());
        ChatUserImVo chatUserImVo = chatUserImService.selectChatUserImVo(chatUserImBo);
        try {
            if (StringUtils.isNotEmpty(chatUserImVo.getAccid())){
                log.info("获取accId成功:{}",  chatUserImVo.getAccid());
                return Long.parseLong(chatUserImVo.getAccid());
            }
        }catch (Exception e){
            log.error("获取accId失败",e);
            throw  new NullPointerException("获取accId失败");
        }
        return null;
    }
}
