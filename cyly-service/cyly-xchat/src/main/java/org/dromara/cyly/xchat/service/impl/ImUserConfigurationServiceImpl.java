package org.dromara.cyly.xchat.service.impl;

import org.dromara.common.core.utils.DateUtils;
import org.dromara.cyly.xchat.domain.ImUserConfiguration;
import org.dromara.cyly.xchat.mapper.ImUserConfigurationMapper;
import org.dromara.cyly.xchat.service.IImUserConfigurationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 云信账号配置项. 该所有1示true，0示falseService业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-26
 */
@Service
public class ImUserConfigurationServiceImpl implements IImUserConfigurationService
{
    @Autowired
    private ImUserConfigurationMapper imUserConfigurationMapper;

    /**
     * 查询云信账号配置项. 该所有1示true，0示false
     *
     * @param id 云信账号配置项. 该所有1示true，0示false主键
     * @return 云信账号配置项. 该所有1示true，0示false
     */
    @Override
    public ImUserConfiguration selectImUserConfigurationById(Long id)
    {
        return imUserConfigurationMapper.selectImUserConfigurationById(id);
    }

    /**
     * 查询云信账号配置项. 该所有1示true，0示false列表
     *
     * @param imUserConfiguration 云信账号配置项. 该所有1示true，0示false
     * @return 云信账号配置项. 该所有1示true，0示false
     */
    @Override
    public List<ImUserConfiguration> selectImUserConfigurationList(ImUserConfiguration imUserConfiguration)
    {
        return imUserConfigurationMapper.selectImUserConfigurationList(imUserConfiguration);
    }

    /**
     * 新增云信账号配置项. 该所有1示true，0示false
     *
     * @param imUserConfiguration 云信账号配置项. 该所有1示true，0示false
     * @return 结果
     */
    @Override
    public int insertImUserConfiguration(ImUserConfiguration imUserConfiguration)
    {
        imUserConfiguration.setCreateTime(DateUtils.getNowDate());
        return imUserConfigurationMapper.insertImUserConfiguration(imUserConfiguration);
    }

    /**
     * 修改云信账号配置项. 该所有1示true，0示false
     *
     * @param imUserConfiguration 云信账号配置项. 该所有1示true，0示false
     * @return 结果
     */
    @Override
    public int updateImUserConfiguration(ImUserConfiguration imUserConfiguration)
    {
        imUserConfiguration.setUpdateTime(DateUtils.getNowDate());
        return imUserConfigurationMapper.updateImUserConfiguration(imUserConfiguration);
    }

    /**
     * 批量删除云信账号配置项. 该所有1示true，0示false
     *
     * @param ids 需要删除的云信账号配置项. 该所有1示true，0示false主键
     * @return 结果
     */
    @Override
    public int deleteImUserConfigurationByIds(Long[] ids)
    {
        return imUserConfigurationMapper.deleteImUserConfigurationByIds(ids);
    }

    /**
     * 删除云信账号配置项. 该所有1示true，0示false信息
     *
     * @param id 云信账号配置项. 该所有1示true，0示false主键
     * @return 结果
     */
    @Override
    public int deleteImUserConfigurationById(Long id)
    {
        return imUserConfigurationMapper.deleteImUserConfigurationById(id);
    }
}
