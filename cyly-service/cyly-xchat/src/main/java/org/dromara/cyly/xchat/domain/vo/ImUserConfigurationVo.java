package org.dromara.cyly.xchat.domain.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class ImUserConfigurationVo {
	@JsonProperty("serial_version_uid")
	private long serialVersionUID;

	@JsonProperty("id")
	private Long id;

	@JsonProperty("enabled")
	private Integer enabled;

	@JsonProperty("p2p_chat_banned")
	private Integer p2pChatBanned;

	@JsonProperty("team_chat_banned")
	private Integer teamChatBanned;

	@JsonProperty("chatroom_chat_banned")
	private Integer chatroomChatBanned;

	@JsonProperty("qchat_chat_banned")
	private Integer qchatChatBanned;

}
