package org.dromara.cyly.xchat.client;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.dromara.common.core.constant.CacheConstants;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.cyly.xchat.auth.ClientConfiguration;
import org.dromara.cyly.xchat.auth.Credentials;
import org.dromara.cyly.xchat.config.Config;
import org.dromara.cyly.xchat.config.exception.VcloudException;
import org.dromara.cyly.xchat.config.sms.HttpPostBuilder;
import org.dromara.cyly.xchat.domain.ChatUserIm;
import org.dromara.cyly.xchat.domain.RegisterInfoResponse;
import org.dromara.xchat.api.domain.vo.LoginUserResource;
import org.dromara.cyly.xchat.config.sms.HttpClientBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * <p>Title: VcloudClient</p>
 * <p>Description: 提供给用户进行点播相关操作的视频云客户端</p>
 * <p>Company: com.netease.vcloud</p>
 * @date       2016-7-1
 */
public class NeteaseClient {

    private static final Logger logger = LoggerFactory.getLogger(NeteaseClient.class);
    private static final String TEMPLATE_ID = "********";
    private static final String ACCOUNT_TEMPLATE_ID = "********";

    private final Credentials credentials;
    private final ClientConfiguration clientConfiguration;

    /**
     * 初始化配置参数
     */
    private void init() {
        Config.setConnectionTimeout(clientConfiguration.getConnectionTimeout());
        Config.setSocketTimeout(clientConfiguration.getSocketTimeout());
        Config.setAppKey(credentials.appKey());
        Config.setAppSecret(credentials.appSecret());
    }

    /**
     *
     * <p>Description: 传入访问服务的凭据类和客户端配置类构造视频云客户端</p>
     * @param credentials            传入访问服务的凭据类
     * @param clientConfiguration    客户端配置类
     */
    public NeteaseClient(Credentials credentials,
                         ClientConfiguration clientConfiguration) {
        this.credentials = credentials;
        this.clientConfiguration = clientConfiguration;
        init();
    }

    /**
     *
     * <p>Description:  传入访问服务的凭据类构造视频云客户端，客户端配置会采用默认配置</p>
     * @param credentials 传入访问服务的凭据类
     */
    public NeteaseClient(Credentials credentials) {
        this.credentials = credentials;
        this.clientConfiguration = new ClientConfiguration();
        init();
    }
    /**
     * 执行HTTP POST请求并解析响应
     *
     * @param url    请求地址
     * @param params 请求参数列表
     * @return 响应数据Map对象
     */
    private Map executeHttpPost(String url, List<NameValuePair> params)  {
        String stringBody = null;
        try {
            HttpClient httpClient = HttpClientBuilder.getHttpClient();
            HttpPost httpPost = HttpPostBuilder.getHttpPost(url);
            httpPost.setEntity(new UrlEncodedFormEntity(params, "utf-8"));

            HttpResponse response = httpClient.execute(httpPost);
            stringBody = EntityUtils.toString(response.getEntity(), "utf-8");
            logger.debug("API Response: {}", stringBody);
        } catch (Exception e) {
            logger.error("HTTP request failed: ", e);
        }
        return JSON.parseObject(stringBody, Map.class);
    }

    /**
     * 发送验证码短信
     *
     * @param code  验证码
     * @param phone 接收验证码的手机号
     * @return API响应状态码
     */
    public String sendVerificationCode(String code, String phone){
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("templateid", TEMPLATE_ID));
        params.add(new BasicNameValuePair("mobile", phone));
        params.add(new BasicNameValuePair("codeLen", "6"));
        params.add(new BasicNameValuePair("authCode", code));

        Map response = executeHttpPost(Config.getSendSmsInterfaceName(), params);
        return String.valueOf(response.get("code"));
    }

    /**
     * 创建账号并发送短信验证
     *
     * @param code  验证码
     * @param phone 目标手机号
     * @return API响应状态码
     */
    public String addAccounts(String code, String phone) {
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("mobile", phone));
        params.add(new BasicNameValuePair("codeLen", "6"));
        params.add(new BasicNameValuePair("authCode", code));
        params.add(new BasicNameValuePair("templateid", ACCOUNT_TEMPLATE_ID));

        Map<String, Object> response = executeHttpPost(Config.getSendSmsInterfaceName(), params);
        return String.valueOf(response.get("code"));
    }

    /**
     * 用户注册接口
     *
     * @param manageUser 登录用户信息对象
     * @return 包含token的ChatUserIm对象
     * @throws VcloudException 请求失败时抛出异常
     */
    public ChatUserIm register(LoginUserResource manageUser) throws VcloudException {
        ChatUserIm ChatUserIm = createChatUserIm(manageUser);

        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("name", ChatUserIm.getNickName()));
        params.add(new BasicNameValuePair("accid", ChatUserIm.getAccid()));

        Map<String, Object> response = executeHttpPost(Config.getRegister(), params);

        // 检查是否已注册，若已存在则刷新token
        if (isAlreadyRegistered(response)) {
            response = refreshUserToken(ChatUserIm.getAccid());
        }

        String info = String.valueOf(response.get("info"));
        if (StringUtils.isEmpty(info)) {
            throw new ServiceException(String.valueOf(response.get("desc")));
        }

        RegisterInfoResponse registerInfoResponse = JSONObject.parseObject(info, RegisterInfoResponse.class);
        ChatUserIm.setToken(registerInfoResponse.getToken());
        return ChatUserIm;
    }

    /**
     * 根据用户信息创建ChatUserIm对象
     *
     * @param manageUser 登录用户信息对象
     * @return 初始化的ChatUserIm对象
     */
    private ChatUserIm createChatUserIm(LoginUserResource manageUser) {
        ChatUserIm ChatUserIm = new ChatUserIm();
        ChatUserIm.setMobile(manageUser.getPhoneNumber());
        ChatUserIm.setName(manageUser.getUsername());
        ChatUserIm.setNickName(manageUser.getNickname());
        ChatUserIm.setAccid(manageUser.getPhoneNumber());
        ChatUserIm.setDeptId(manageUser.getDeptId());
        return ChatUserIm;
    }

    /**
     * 判断用户是否已注册
     *
     * @param response API响应数据
     * @return true表示已注册，false表示未注册
     */
    private boolean isAlreadyRegistered(Map<String, Object> response) {
        return response.get("code") != null
                && (Integer) response.get("code") == 414
                && StringUtils.equals((String) response.get("desc"), "already register");
    }

    /**
     * 刷新用户token
     *
     * @param accId 用户账号ID
     * @return API响应数据
     */
    public Map refreshUserToken(String accId) {
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("accid", accId));
        return executeHttpPost(Config.getRefreshToken(), params);
    }

    /**
     * 添加单个好友关系
     *
     * @param accid   当前用户账号ID
     * @param faccid  目标用户账号ID
     * @throws VcloudException 请求失败时抛出异常
     */
    public void addFriend(String accid, String faccid) throws VcloudException{
        List<NameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("accid", accid));
        params.add(new BasicNameValuePair("faccid", faccid));
        params.add(new BasicNameValuePair("type", "1"));

        Map response = executeHttpPost(Config.getFriendAdd(), params);

        String code = String.valueOf(response.get("code"));
        if (StringUtils.isNotEmpty(code) && code.equals("200")) {
            logger.info("账号：{}添加好友：{}成功", accid, faccid);
        }
    }

    /**
     * 批量添加好友关系（异步执行）
     *
     * @param accid   当前用户账号ID
     * @param ChatUserImList 需要添加的好友列表
     * @param userId  用户ID（用于缓存状态标记）
     */
    @Async
    public void addFriend(String accid, List<ChatUserIm> ChatUserImList, Long userId) {
        String key = CacheConstants.SYC_FRIEND_STATUS + userId;
        RedisUtils.setCacheObject(key, false);

        try {
            if (CollectionUtils.isNotEmpty(ChatUserImList)) {
                ChatUserImList.forEach(ChatUserIm -> {
                    try {
                        addFriend(accid, ChatUserIm.getAccid());
                    } catch (VcloudException e) {
                        throw new RuntimeException(e);
                    }
                });
            }
        } finally {
            RedisUtils.setCacheObject(key, true);
        }
    }
}
