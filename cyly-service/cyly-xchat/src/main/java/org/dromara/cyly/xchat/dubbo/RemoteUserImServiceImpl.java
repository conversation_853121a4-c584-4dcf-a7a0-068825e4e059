package org.dromara.cyly.xchat.dubbo;

import cn.hutool.core.bean.BeanUtil;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.dromara.cyly.xchat.domain.ChatUserIm;
import org.dromara.cyly.xchat.domain.vo.ChatUserImVo;
import org.dromara.cyly.xchat.service.IChatUserImService;
import org.dromara.xchat.api.model.RemoteChatUserImService;
import org.dromara.xchat.api.domain.vo.RemoteChatUserImVo;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * 用户服务
 *
 * <AUTHOR> Li
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteUserImServiceImpl implements RemoteChatUserImService {

    private final IChatUserImService userImService;

    @Override
    public RemoteChatUserImVo selectUserByPhoneNumber(String phoneNumber) {
        ChatUserImVo chatUserImVo = userImService.selectUserByPhoneNumber(phoneNumber);
        return BeanUtil.copyProperties(chatUserImVo, RemoteChatUserImVo.class);
    }

    @Override
    public void insertChatUserIm(RemoteChatUserImVo convert) {
        userImService.insertChatUserIm(BeanUtil.copyProperties(convert, ChatUserIm.class));
    }

    @Override
    public List<RemoteChatUserImVo> selectAllSysUserImList(Long deptId, String accid) {
        List<ChatUserImVo> sysUserIms = userImService.selectAllChatUserImList(deptId, accid);
        return BeanUtil.copyToList(sysUserIms, RemoteChatUserImVo.class);
    }

}
