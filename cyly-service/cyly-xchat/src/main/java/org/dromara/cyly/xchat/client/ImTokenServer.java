package org.dromara.cyly.xchat.client;

import cn.hutool.json.JSONUtil;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.dromara.cyly.xchat.config.NeteaseConfig;
import org.dromara.system.api.RemoteAppUserService;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.zip.Deflater;

/**
 * im音视频token
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class ImTokenServer {
    private final NeteaseConfig netease;
    private static final int DEFAULT_TTL_SEC = 3600;

    /**
     * 生成指定频道和用户的token。
     * @param channelName 频道名称
     * @param uid 用户ID
     * @param ttlSec token有效期（秒），应小于86400秒（1天）
     * @return token字符串
     * @throws Exception 生成过程中可能抛出的异常
     */
    public String getToken(String channelName, long uid, int ttlSec) throws Exception {
        return getTokenWithCurrentTime(channelName, uid, ttlSec, System.currentTimeMillis());
    }

    /**
     * 使用指定时间生成token（主要用于测试）。
     * @param channelName 频道名称
     * @param uid 用户ID
     * @param ttlSec token有效期（秒）
     * @param curTimeMs 当前时间戳（毫秒）
     * @return token字符串
     * @throws Exception 生成过程中可能抛出的异常
     */
    private String getTokenWithCurrentTime(String channelName, long uid, int ttlSec, long curTimeMs) throws Exception {
        if (ttlSec <= 0) {
            ttlSec = DEFAULT_TTL_SEC;
        }
        DynamicToken tokenModel = new DynamicToken();
        tokenModel.signature = sha1(String.format("%s%d%d%d%s%s",
            netease.getAppKey(), uid, curTimeMs, ttlSec, channelName, netease.getAppSecret()));
        tokenModel.curTime = curTimeMs;
        tokenModel.ttl = ttlSec;

        String signature = JSONUtil.toJsonStr(tokenModel);
        return Base64.getEncoder().encodeToString(signature.getBytes(StandardCharsets.UTF_8));
    }


    /**
     * 生成权限密钥
     *
     * @param channelName 频道名称
     * @param permSecret 权限密钥
     * @param uid 用户ID
     * @param privilege 权限值（8位二进制数，每位代表一个权限）
     * @param ttlSec 有效期（秒）
     * @return 权限密钥字符串
     */
    public String getPermissionKey(String channelName, String permSecret, long uid, byte privilege, long ttlSec)
        throws NoSuchAlgorithmException, InvalidKeyException {
        return getPermissionKeyWithCurrentTime(channelName, permSecret, uid, privilege, ttlSec,
            System.currentTimeMillis() / 1000);
    }

    /**
     * 生成包含当前时间的权限密钥字符串，经过签名、序列化、压缩和编码处理
     *
     * @param channelName  频道名称
     * @param permSecret   用于签名的权限密钥
     * @param uid          用户唯一标识
     * @param privilege    权限级别（如读写权限标识）
     * @param ttlSec       密钥有效期（单位：秒）
     * @param curTime      当前时间戳（通常为Unix时间戳）
     * @return 经过Base64编码和压缩的权限密钥JSON字符串
     * @throws NoSuchAlgorithmException 签名算法不支持时抛出
     * @throws InvalidKeyException      签名密钥无效时抛出
     */
    private String getPermissionKeyWithCurrentTime(String channelName, String permSecret, long uid, byte privilege,
                                                  long ttlSec, long curTime)
        throws NoSuchAlgorithmException, InvalidKeyException {
        PermissionKey permKey = new PermissionKey();
        permKey.appKey = netease.getAppKey();
        permKey.uid = uid;
        permKey.cname = channelName;
        permKey.privilege = privilege;
        permKey.expireTime = ttlSec;
        permKey.curTime = curTime;

        permKey.checksum = hmacsha256(netease.getAppKey(), String.valueOf(uid), String.valueOf(curTime),
            String.valueOf(ttlSec), channelName, permSecret, String.valueOf(privilege));

        String jsonStr = JSONUtil.toJsonStr(permKey);
        byte[] compressedData = compress(jsonStr.getBytes(StandardCharsets.UTF_8));
        return new String(base64EncodeUrl(compressedData));
    }


    /**
     * 使用Deflater压缩算法对输入字节数组进行压缩。
     * @param data 需要压缩的原始字节数组
     * @return 压缩后的字节数组
     */
    private static byte[] compress(byte[] data) {
        try {
            Deflater deflater = new Deflater(6);
            deflater.setInput(data);
            deflater.finish();

            byte[] buffer = new byte[2048];
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream(data.length);
            // 循环压缩数据直到完成，将压缩结果写入输出流
            while (!deflater.finished()) {
                int count = deflater.deflate(buffer);
                outputStream.write(buffer, 0, count);
            }
            outputStream.close();

            byte[] compressedData = outputStream.toByteArray();
            deflater.end();
            return compressedData;
        } catch (java.io.IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 生成HMAC-SHA256签名。
     * @param appidStr 应用ID字符串
     * @param uidStr 用户唯一标识字符串
     * @param curTimeStr 当前时间戳字符串
     * @param expireTimeStr 过期时间戳字符串
     * @param cname 域名或名称
     * @param permSecret 权限密钥字符串
     * @param privilegeStr 权限信息字符串
     * @return Base64编码的HMAC-SHA256签名结果
     * @throws NoSuchAlgorithmException 如果HMAC-SHA256算法不可用
     * @throws InvalidKeyException 如果密钥无效
     */
    private static String hmacsha256(String appidStr, String uidStr, String curTimeStr, String expireTimeStr,
                                     String cname, String permSecret, String privilegeStr)
        throws NoSuchAlgorithmException, InvalidKeyException {
        String contentToBeSigned = "appkey:" + appidStr + "\n";
        contentToBeSigned += "uid:" + uidStr + "\n";
        contentToBeSigned += "curTime:" + curTimeStr + "\n";
        contentToBeSigned += "expireTime:" + expireTimeStr + "\n";
        contentToBeSigned += "cname:" + cname + "\n";
        contentToBeSigned += "privilege:" + privilegeStr + "\n";

        SecretKeySpec keySpec = new SecretKeySpec(permSecret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(keySpec);
        byte[] result = mac.doFinal(contentToBeSigned.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(result);
    }

    /**
     * 计算输入字符串的SHA-1哈希值，并返回十六进制字符串表示。
     * @param input 需要计算哈希的输入字符串
     * @return SHA-1哈希值的十六进制字符串
     * @throws NoSuchAlgorithmException 如果SHA-1算法不可用
     */
    private String sha1(String input) throws NoSuchAlgorithmException {
        MessageDigest mDigest = MessageDigest.getInstance("SHA-1");
        byte[] result = mDigest.digest(input.getBytes(StandardCharsets.UTF_8));
        StringBuilder sb = new StringBuilder();
        for (byte b : result) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }

    /**
     * 对输入字节数组进行Base64编码，并替换字符以适应URL编码规范。
     * @param input 需要编码的原始字节数组
     * @return 替换后的Base64编码字节数组（将'+'替换为'*'，'/'替换为'-'，'='替换为'_'）
     */
    private static byte[] base64EncodeUrl(byte[] input) {
        byte[] base64 = Base64.getEncoder().encode(input);
        // 替换Base64编码中的特殊字符以适应URL编码要求
        for (int i = 0; i < base64.length; ++i)
            switch (base64[i]) {
                case '+':
                    base64[i] = '*';
                    break;
                case '/':
                    base64[i] = '-';
                    break;
                case '=':
                    base64[i] = '_';
                    break;
                default:
                    break;
            }
        return base64;
    }

    /**
     * 动态令牌类，包含签名（signature）、当前时间戳（curTime）和生存时间（ttl）。
     */
    public static final class DynamicToken {
        public String signature;
        public long curTime;
        public int ttl;
    }

    /**
     * 权限密钥类，包含应用密钥（appKey）、用户ID（uid）、域名（cname）、权限标识（privilege）、过期时间（expireTime）、当前时间（curTime）和校验和（checksum）。
     */
    public static final class PermissionKey {
        public String appKey;
        public long uid;
        public String cname;
        public byte privilege;
        public long expireTime;
        public long curTime;
        public String checksum;
    }
}
