package org.dromara.cyly.xchat.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.cyly.xchat.domain.ChatLog;
import org.dromara.cyly.xchat.domain.bo.ChatLogBo;
import org.dromara.cyly.xchat.domain.vo.ChatLogVo;
import org.dromara.cyly.xchat.mapper.ChatLogMapper;
import org.dromara.cyly.xchat.service.IChatLogService;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 消息记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-09
 */
@RequiredArgsConstructor
@Service
public class ChatLogServiceImpl implements IChatLogService {

    private final ChatLogMapper baseMapper;

    /**
     * 查询消息记录
     *
     * @param Id 主键
     * @return 消息记录
     */
    @Override
    public ChatLogVo queryById(String Id){
        return baseMapper.selectVoById(Id);
    }

    /**
     * 分页查询消息记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 消息记录分页列表
     */
    @Override
    public TableDataInfo<ChatLogVo> queryPageList(ChatLogBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ChatLog> lqw = buildQueryWrapper(bo);
        Page<ChatLogVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的消息记录列表
     *
     * @param bo 查询条件
     * @return 消息记录列表
     */
    @Override
    public List<ChatLogVo> queryList(ChatLogBo bo) {
        LambdaQueryWrapper<ChatLog> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ChatLog> buildQueryWrapper(ChatLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ChatLog> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getIdClient()), ChatLog::getIdClient, bo.getIdClient());
        lqw.eq(StringUtils.isNotBlank(bo.getIdServer()), ChatLog::getIdServer, bo.getIdServer());
        lqw.eq(StringUtils.isNotBlank(bo.getScene()), ChatLog::getScene, bo.getScene());
        lqw.eq(StringUtils.isNotBlank(bo.getToAccept()), ChatLog::getToAccept, bo.getToAccept());
        lqw.like(StringUtils.isNotBlank(bo.getToName()), ChatLog::getToName, bo.getToName());
        lqw.eq(StringUtils.isNotBlank(bo.getToAvatar()), ChatLog::getToAvatar, bo.getToAvatar());
        lqw.eq(StringUtils.isNotBlank(bo.getFromAccount()), ChatLog::getFromAccount, bo.getFromAccount());
        lqw.eq(StringUtils.isNotBlank(bo.getFromNick()), ChatLog::getFromNick, bo.getFromNick());
        lqw.eq(bo.getFromDeviceId() != null, ChatLog::getFromDeviceId, bo.getFromDeviceId());
        lqw.eq(StringUtils.isNotBlank(bo.getType()), ChatLog::getType, bo.getType());
        lqw.eq(StringUtils.isNotBlank(bo.getContent()), ChatLog::getContent, bo.getContent());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), ChatLog::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getClassType()), ChatLog::getClass, bo.getClass());
        lqw.eq(StringUtils.isNotBlank(bo.getExt()), ChatLog::getExt, bo.getExt());
        lqw.orderByDesc(ChatLog::getCreateTime);
        return lqw;
    }

    /**
     * 新增消息记录
     *
     * @param bo 消息记录
     * @return 是否新增成功
     */
    @Override
    public ChatLog insertByBo(ChatLogBo bo) {
        ChatLog add = MapstructUtils.convert(bo, ChatLog.class);
        validEntityBeforeSave(add); // 确保此处验证失败抛出异常
        if (add!=null && add.getClassType() == null){
            add.setClassType("org.dromara.system.domain.ChatLog");
        }
        boolean flag = baseMapper.insert(add) > 0;
        if (flag && add!=null && add.getId() != null) {
            bo.setId(add.getId());
        }
        return flag ? add : null; // 根据业务需求选择返回null或抛出异常
    }

    /**
     * 修改消息记录
     *
     * @param bo 消息记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ChatLogBo bo) {
        if (bo == null) {
            throw new IllegalArgumentException("ChatLogBo cannot be null");
        }
        ChatLog update = new ChatLog();
        // 使用 defaultIfEmpty 简化空值处理
        update.setStatus(StringUtils.defaultIfEmpty(bo.getStatus(), "recall"));
        validEntityBeforeSave(update);
        LambdaQueryWrapper<ChatLog> lqw = Wrappers.lambdaQuery();
        // 确保 idClient 是唯一标识，否则需调整查询条件
        return baseMapper.update(update, lqw.eq(ChatLog::getIdClient, bo.getIdClient())) > 0;
    }


    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ChatLog entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除消息记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
