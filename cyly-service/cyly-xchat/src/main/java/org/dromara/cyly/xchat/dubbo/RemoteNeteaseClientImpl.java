package org.dromara.cyly.xchat.dubbo;

import cn.hutool.core.bean.BeanUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.dromara.cyly.xchat.client.NeteaseClient;
import org.dromara.cyly.xchat.config.exception.VcloudException;
import org.dromara.cyly.xchat.domain.ChatUserIm;
import org.dromara.xchat.api.domain.vo.RemoteChatUserImVo;
import org.dromara.xchat.api.model.RemoteNeteaseClient;
import org.dromara.xchat.api.domain.vo.LoginUserResource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 21:32
 */
@Slf4j
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteNeteaseClientImpl implements RemoteNeteaseClient {
    private final NeteaseClient neteaseClient;


    @Override
    public RemoteChatUserImVo register(LoginUserResource userResource) {
        try {
            ChatUserIm register = neteaseClient.register(userResource);
            return BeanUtil.copyProperties(register, RemoteChatUserImVo.class);
        } catch (VcloudException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 发送手机号和验证到im云信
     * @param smsCode 验证码
     * @param phoneNumber 手机号码
     * @return 状态码
     */
    @Override
    public String sendVerificationCode(String smsCode, String phoneNumber) {
        return neteaseClient.sendVerificationCode(smsCode, phoneNumber);
    }

    @Override
    public Map<String, Object> refreshUserToken(String accId) {
        return neteaseClient.refreshUserToken(accId);
    }

    @Override
    public void addFriend(String accid, List<RemoteChatUserImVo> convert, Long userId) {
        neteaseClient.addFriend(accid, BeanUtil.copyToList(convert, ChatUserIm.class), userId);
    }
}
