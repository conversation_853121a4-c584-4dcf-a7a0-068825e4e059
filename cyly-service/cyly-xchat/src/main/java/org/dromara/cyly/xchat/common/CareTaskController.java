package org.dromara.cyly.xchat.common;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.dromara.common.satoken.utils.AdminLoginHelper;
import org.dromara.common.web.core.BaseController;
import org.dromara.cyly.xchat.domain.UserNurseInfo;
import org.dromara.cyly.xchat.mapper.UserNurseInfoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;


/**
 * 解析护理照顾计划
 *
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/careTaskRecord")
@Configuration
public class CareTaskController extends BaseController {
//    @DubboReference
//    private final ElderInfoMapper elderInfoMapper;

    private final UserNurseInfoMapper userNurseInfoMapper;

    /**
     * 根据护理员id获取照护任务列表成功
     *
     */
    @PostMapping("/list")
    public JSONObject getCareTaskListByCaregiver() {
        LambdaQueryWrapper<UserNurseInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserNurseInfo::getUserId, AdminLoginHelper.getLoginUser());
        UserNurseInfo userNurseInfo;
        try {
            userNurseInfo = userNurseInfoMapper.selectOne(wrapper);
        }catch (Exception e){
            throw new RuntimeException("未绑定护理员");
        }
        if (userNurseInfo.getNurseId()!=null){
            // 目标URL
            String url = "https://4008890919.com:10010/careTaskRecord/list";
            // 使用HttpUtil创建GET请求并设置必要的请求头
            HttpResponse response = HttpRequest.post(url)
                    .header("Content-Type", MediaType.MULTIPART_FORM_DATA_VALUE)
                    .header("Accept", "application/json")
                    .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:131.0) Gecko/20100101 Firefox/131.0")
                    .header("Host", "4008890919.com:10010")
                    .form("caregiverId", userNurseInfo.getNurseId())
                    // 如果需要身份验证，添加相应的头部
                    // .header("Authorization", "Bearer YOUR_ACCESS_TOKEN")
                    .execute();
            // 获取响应状态码
            int statusCode = response.getStatus();
            return jsonObjectUtil(response, statusCode);
        }
        else {
//            logger.error("未绑定护理员");
            return null;
        }




    }

    /**
     * 长者照顾时间线
     * @return 结果
     */
    @PostMapping("/list/elder/{elderId}")
    public JSONObject getCareTaskListByElder(@PathVariable("elderId") Long elderId) {
//        ElderInfo elderInfo;
        try {
//             elderInfo = elderInfoMapper.selectById(elderId);

        }catch (Exception e){
            throw new RuntimeException("未找到该老人");
        }


        // 目标URL
        String url = "https://4008890919.com:10010/careTaskRecord/list/elder";
        // 使用HttpUtil创建GET请求并设置必要的请求头
        HttpResponse response = HttpRequest.post(url)
                .header("Content-Type",  MediaType.MULTIPART_FORM_DATA_VALUE)
                .header("Accept", "application/json")
                .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:131.0) Gecko/20100101 Firefox/131.0")
                .header("Host", "4008890919.com")
//                .header("accessToken", "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIzMSIsImlhdCI6MTczNDE0NjI1NywiZXhwIjoxNzM0MTQ4MDU3fQ.06jnzYIn4k9UW0bbxiCOEMSsuXLm6mKUga6J1ptIa3w eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjMyYzIzNDliLTBmYTAtNDdiOC04ZjFjLTA2ODY0MTBkMzYyYSJ9.I5plT_qVRjP0euDQcJmhV0asdaKDNpd_926nZExNgM-1fcAxIF0ORVrg5kh_1WWcwybOZgMPQaHMeF3vbhwKSQ")
//                .form("elderName", elderInfo.getName())
//                .form("idCard", elderInfo.getIdNumber())
                // 如果需要身份验证，添加相应的头部
                .execute();

        // 获取响应状态码
        int statusCode = response.getStatus();
        return jsonObjectUtil(response, statusCode);
    }

    /**
     * 获取照顾任务详情
     * id 照顾任务id
     */
    @GetMapping("/{id}")
    public JSONObject get(@PathVariable("id") String id) {
        // 目标URL
        String url = "https://4008890919.com:10010/careTaskRecord/" + id;
        // 使用HttpUtil创建GET请求并设置必要的请求头
        HttpResponse response = HttpRequest.get(url)
//                .header("Content-Type", MediaType.MULTIPART_FORM_DATA_VALUE)
                .header("Accept", "application/json")
                .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:131.0) Gecko/20100101 Firefox/131.0")
                .header("Host", "4008890919.com:10010")
                // 如果需要身份验证，添加相应的头部
                // .header("Authorization", "Bearer YOUR_ACCESS_TOKEN")
                .execute();

        // 获取响应状态码
        int statusCode = response.getStatus();
        return jsonObjectUtil(response, statusCode);
    }

    /**
     * 开始执行照顾任务
     * @param id 照顾任务id
     * @return 结果
     */
    @PostMapping("/start/executing/{id}")
    private JSONObject startExecutingById(@PathVariable("id") String id) {
        // 目标URL
        String url = "https://4008890919.com:10010/careTaskRecord/start/executing/" + id;
        // 使用HttpUtil创建GET请求并设置必要的请求头
        HttpResponse response = HttpRequest.post(url)
                .header("Content-Type", MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                .header("Accept", "application/json")
                .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:131.0) Gecko/20100101 Firefox/131.0")
                .header("Host", "4008890919.com:10010")

                // 如果需要身份验证，添加相应的头部
                // .header("Authorization", "Bearer YOUR_ACCESS_TOKEN")
                .execute();

        // 获取响应状态码
        int statusCode = response.getStatus();
        return jsonObjectUtil(response, statusCode);
    }

    private JSONObject jsonObjectUtil(HttpResponse response, int statusCode) {
        try {
            if (statusCode == 200) {
                // 获取响应体并解析为JSON对象
                return JSONObject.parseObject(response.body());
            } else {
                // 构建错误响应
                JSONObject errorResponse = new JSONObject();
                errorResponse.put("success", false);
                errorResponse.put("message", "服务器返回了非200的状态码：" + statusCode);
                errorResponse.put("data", null);
                errorResponse.put("code", statusCode);
                return errorResponse;
            }
        } catch (Exception e) {
            // 捕获并构建异常响应
            JSONObject errorResponse = new JSONObject();
            errorResponse.put("success", false);
            errorResponse.put("message", "请求过程中发生异常：" + e.getMessage());
            errorResponse.put("data", null);
            errorResponse.put("code", 500);
            return errorResponse;
        }
    }
}
