package org.dromara.cyly.xchat.controller;

import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.satoken.utils.AppLoginHelper;
import org.dromara.cyly.xchat.domain.vo.ChatUserImVo;
import org.dromara.cyly.xchat.service.IChatUserImService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * im用户管理
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/imUser")
public class ChatUserImController {
    private final IChatUserImService chatUserImService;

    /**
     * 刷新token
     */
    @PostMapping("/usrapi/refreshToken/{accId}")
    public R<Map<String, Object>> refreshToken(@PathVariable("accId") String accId) {
        return R.ok(chatUserImService.refreshToken(accId));
    }

    /**
     * 获取im用户及app用户信息
     */
    @GetMapping("/usrapi/getImUserInfo/{accId}")
    public R<ChatUserImVo> getUserImInfo(@PathVariable String accId){
        return R.ok(chatUserImService.getUserImInfo(accId, AppLoginHelper.getUserId()));
    }
}
