package org.dromara.cyly.xchat.service;

import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.cyly.xchat.domain.ChatUserIm;
import org.dromara.cyly.xchat.domain.bo.ChatUserImBo;
import org.dromara.cyly.xchat.domain.vo.ChatUserImVo;

import java.util.List;
import java.util.Map;

/**
 * IM用户信息Service接口
 *
 * <AUTHOR>
 * @date 2024-03-26
 */
public interface IChatUserImService {


    ChatUserIm selectChatUserImByAccid(String accid);

    ChatUserImVo selectChatUserImVo(ChatUserImBo bo);

    TableDataInfo<ChatUserIm> selectChatUserImList(ChatUserIm ChatUserIm, PageQuery pageQuery);

    ChatUserImVo getUserImInfo(String accId, Long appUserId);

    int insertChatUserIm(ChatUserIm ChatUserIm);

    int updateChatUserIm(ChatUserIm ChatUserIm);

    int deleteChatUserImByAccids(String[] accids);

    int deleteChatUserImByAccid(String accid);

    ChatUserImVo selectUserByPhoneNumber(String phone);

    List<ChatUserImVo> selectAllChatUserImList(Long deptId, String accid);

    Map<String, Object> refreshToken(String accId);
}
