package org.dromara.cyly.xchat.domain.bo;


import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;


/**
 * @version 1.0
 * @author: xjf
 * @date: 2024/11/25 15:32
 * 描述：亲属与长者绑定实体类
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserElderBo extends BaseEntity {
    /*长者名字*/
    @NotNull(message = "长者名字不能为空", groups = { AddGroup.class, EditGroup.class })
    private String elderName;
    /*长者身份证号*/
    @NotNull(message = "长者身份证号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String elderIdCard;
    /*家属名字*/
    @NotNull(message = "家属名字不能为空", groups = { AddGroup.class, EditGroup.class })
    private String kinName;
    /*家属身份证号*/
    @NotNull(message = "家属身份证号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String kinIdCard;
    /*亲属关系*/
    @NotNull(message = "亲属关系不能为空", groups = { AddGroup.class, EditGroup.class })
    private String kinType;
}
