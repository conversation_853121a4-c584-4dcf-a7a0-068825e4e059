package org.dromara.cyly.xchat.service;


import org.dromara.cyly.xchat.domain.ImUserConfiguration;

import java.util.List;


/**
 * 云信账号配置项. 该所有1示true，0示falseService接口
 *
 * <AUTHOR>
 * @date 2024-03-26
 */
public interface IImUserConfigurationService
{
    /**
     * 查询云信账号配置项. 该所有1示true，0示false
     *
     * @param id 云信账号配置项. 该所有1示true，0示false主键
     * @return 云信账号配置项. 该所有1示true，0示false
     */
    ImUserConfiguration selectImUserConfigurationById(Long id);

    /**
     * 查询云信账号配置项. 该所有1示true，0示false列表
     *
     * @param imUserConfiguration 云信账号配置项. 该所有1示true，0示false
     * @return 云信账号配置项. 该所有1示true，0示false集合
     */
    List<ImUserConfiguration> selectImUserConfigurationList(ImUserConfiguration imUserConfiguration);

    /**
     * 新增云信账号配置项. 该所有1示true，0示false
     *
     * @param imUserConfiguration 云信账号配置项. 该所有1示true，0示false
     * @return 结果
     */
    int insertImUserConfiguration(ImUserConfiguration imUserConfiguration);

    /**
     * 修改云信账号配置项. 该所有1示true，0示false
     *
     * @param imUserConfiguration 云信账号配置项. 该所有1示true，0示false
     * @return 结果
     */
    int updateImUserConfiguration(ImUserConfiguration imUserConfiguration);

    /**
     * 批量删除云信账号配置项. 该所有1示true，0示false
     *
     * @param ids 需要删除的云信账号配置项. 该所有1示true，0示false主键集合
     * @return 结果
     */
    int deleteImUserConfigurationByIds(Long[] ids);

    /**
     * 删除云信账号配置项. 该所有1示true，0示false信息
     *
     * @param id 云信账号配置项. 该所有1示true，0示false主键
     * @return 结果
     */
    int deleteImUserConfigurationById(Long id);
}
