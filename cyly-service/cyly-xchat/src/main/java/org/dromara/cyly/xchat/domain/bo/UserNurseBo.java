package org.dromara.cyly.xchat.domain.bo;


import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;



/**
 * @version 1.0
 * @author: xjf
 * @date: 2024/11/25 15:34
 * 描述：用户与护理员绑定实体
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserNurseBo extends BaseEntity {
    /*护理员名字*/
    @NotNull(message = "护理员名字不能为空", groups = { AddGroup.class, EditGroup.class })
    private String nurseName;
    /*护理员身份证号*/
    @NotNull(message = "护理员身份证号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String nurseIdCard;
}
