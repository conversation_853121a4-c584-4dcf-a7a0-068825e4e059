package org.dromara.cyly.xchat.dubbo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.cyly.xchat.domain.UserNurseInfo;
import org.dromara.cyly.xchat.domain.vo.UserNurseInfoVo;
import org.dromara.cyly.xchat.mapper.UserNurseInfoMapper;
import org.dromara.system.api.RemoteAppUserService;
import org.dromara.xchat.api.RemoteUserNurseInfoService;
import org.dromara.xchat.api.domain.vo.RemoteUserNurseInfoVo;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 用户与护理员与长者信息关联服务实现
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteUserNurseInfoServiceImpl implements RemoteUserNurseInfoService {

    private final UserNurseInfoMapper userNurseInfoMapper;

    @DubboReference
    private RemoteAppUserService appUserService;

    /**
     * 根据用户ID查询用户与护理员与长者信息关联
     *
     * @param userId 用户ID
     * @return 用户与护理员与长者信息关联
     */
    @Override
    public RemoteUserNurseInfoVo getUserNurseInfoByUserId(Long userId) {
        String identity = appUserService.selectChatIdentityById(userId);
        if (identity == null) {
            throw new RuntimeException("用户不存在");
        }
        if (Objects.equals(identity, "1")) {
            UserNurseInfoVo userNurseInfoVo = userNurseInfoMapper.selectVoOne(new LambdaQueryWrapper<UserNurseInfo>()
                .eq(UserNurseInfo::getUserId, userId)
                .isNotNull(UserNurseInfo::getNurseId));
            return MapstructUtils.convert(userNurseInfoVo, RemoteUserNurseInfoVo.class);
        } else if (Objects.equals(identity, "0")) {
            UserNurseInfoVo userNurseInfoVo = userNurseInfoMapper.selectVoOne(new LambdaQueryWrapper<UserNurseInfo>()
                .eq(UserNurseInfo::getUserId, userId)
                .isNotNull(UserNurseInfo::getElderId));
            return MapstructUtils.convert(userNurseInfoVo, RemoteUserNurseInfoVo.class);
        } else {
            throw new RuntimeException("用户不存在");
        }
    }
}
