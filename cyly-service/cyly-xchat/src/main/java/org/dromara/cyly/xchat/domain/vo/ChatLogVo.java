package org.dromara.cyly.xchat.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.cyly.xchat.domain.ChatLog;

import java.io.Serial;
import java.io.Serializable;



/**
 * 消息记录视图对象 chat_log
 *
 * <AUTHOR>
 * @date 2025-03-09
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = ChatLog.class)
public class ChatLogVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String id;

    /**
     * 客户端生成的消息id1
     */
    @ExcelProperty(value = "客户端生成的消息id1")
    private String idClient;

    /**
     * 服务端生成的消息1
     */
    @ExcelProperty(value = "服务端生成的消息1")
    private String idServer;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String scene;

    /**
     * 接受者id
     */
    @ExcelProperty(value = "接受者id")
    private String toAccept;

    /**
     * 接受者姓名
     */
    @ExcelProperty(value = "接受者姓名")
    private String toName;

    /**
     * 接受者用户头像
     */
    @ExcelProperty(value = "接受者用户头像")
    private String toAvatar;

    /**
     * 发送至账号
     */
    @ExcelProperty(value = "发送至账号")
    private String fromAccount;

    /**
     * 发送者昵称
     */
    @ExcelProperty(value = "发送者昵称")
    private String fromNick;

    /**
     * 设备标识
     */
    @ExcelProperty(value = "设备标识")
    private String fromDeviceId;

    /**
     * 类型
     */
    @ExcelProperty(value = "类型")
    private String type;

    /**
     * 消息内容
     */
    @ExcelProperty(value = "消息内容")
    private String content;

    /**
     * 消息状态
     */
    @ExcelProperty(value = "消息状态")
    private String status;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String classType;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String ext;


}
