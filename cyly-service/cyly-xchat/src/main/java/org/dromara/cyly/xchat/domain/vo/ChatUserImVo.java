package org.dromara.cyly.xchat.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.cyly.xchat.domain.ChatUserIm;
import org.dromara.system.api.domain.vo.RemoteAppUserVo;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 聊天用户信息
 */
@Data
@AutoMapper(target = ChatUserIm.class)
public class ChatUserImVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /** 云信 IM 账号 */
    private String accid;

    /** 登录密钥token */
    private String token;

    /** 部门ID */
    private Long deptId;

    /** 名称 */
    private String name;

    /** 用户昵称 */
    private String nickName;

    /** 用户签名 */
    private String sign;

    /** 用户类型（00系统用户） */
    private String userType;

    /** 用户邮箱 */
    private String email;

    /** 用户生日 */
    private String birth;

    /** 手机号码 */
    private String mobile;

    /** 用户性别（0未知 1男 2女）- 这里参考的是IM的，后台管理的是 （0男 1女 2未知） */
    private Integer gender;

    /** 用户头像 URL */
    private String icon;

    /** 密码 */
    private String password;

    /** 帐号状态（0正常 1停用） */
    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 最后登录IP */
    private String loginIp;

    /** 最后登录时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date loginDate;

    /** 用户资料扩展字段 */
    private String ex;

    private String remark;

    private Long userId;

    /**
     * app用户
     */
    private RemoteAppUserVo appUser;
}
