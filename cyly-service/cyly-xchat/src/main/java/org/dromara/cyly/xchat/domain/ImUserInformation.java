package org.dromara.cyly.xchat.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;

/**
 * im用户信息对象 im_user_information
 *
 * <AUTHOR>
 * @date 2024-03-26
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ImUserInformation extends BaseEntity
{
    @Serial
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 用户昵称 */
//     @Excel(name = "用户昵称")
    private String name;

    /** 用户头像的 URL 地址 */
//     @Excel(name = "用户头像的 URL 地址")
    private String avatar;

    /** 用户签名。 */
//     @Excel(name = "用户签名。")
    private String sign;

    /** 用户邮箱地址。 */
//     @Excel(name = "用户邮箱地址。")
    private String email;

    /** 用户生日，例如 "xxxx-xx-xx" */
//     @Excel(name = "用户生日，例如 xxxx-xx-xx")
    private String birthday;

    /** 用户手机号码。 */
//     @Excel(name = "用户手机号码。")
    private String mobile;

    /** 用户性别，0-未知，1-男，2-女。 */
//     @Excel(name = "用户性别，0-未知，1-男，2-女。")
    private Integer gender;

    /** 预留给开发者的扩展字段，建议封装成 JSON 格式，{key:value} */
//     @Excel(name = "预留给开发者的扩展字段，建议封装成 JSON 格式，{key:value}")
    private String extension;

    /** 安全通业务ID */
//     @Excel(name = "安全通业务ID")
    private String antispamBusinessId;


    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("avatar", getAvatar())
            .append("sign", getSign())
            .append("email", getEmail())
            .append("birthday", getBirthday())
            .append("mobile", getMobile())
            .append("gender", getGender())
            .append("extension", getExtension())
            .append("antispamBusinessId", getAntispamBusinessId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
