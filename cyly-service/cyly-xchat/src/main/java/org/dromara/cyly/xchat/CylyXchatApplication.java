package org.dromara.cyly.xchat;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

@SpringBootApplication
@EnableDubbo
public class CylyXchatApplication {
    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(CylyXchatApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  即时通信服务模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }
}
