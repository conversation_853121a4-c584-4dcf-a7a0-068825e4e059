package org.dromara.cyly.xchat.service;

import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.cyly.xchat.domain.ChatLog;
import org.dromara.cyly.xchat.domain.bo.ChatLogBo;
import org.dromara.cyly.xchat.domain.vo.ChatLogVo;

import java.util.Collection;
import java.util.List;

/**
 * 消息记录Service接口
 *
 * <AUTHOR>
 * @date 2025-03-09
 */
public interface IChatLogService {

    /**
     * 查询消息记录
     *
     * @param Id 主键
     * @return 消息记录
     */
    ChatLogVo queryById(String Id);

    /**
     * 分页查询消息记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 消息记录分页列表
     */
    TableDataInfo<ChatLogVo> queryPageList(ChatLogBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的消息记录列表
     *
     * @param bo 查询条件
     * @return 消息记录列表
     */
    List<ChatLogVo> queryList(ChatLogBo bo);

    /**
     * 新增消息记录
     *
     * @param bo 消息记录
     * @return 是否新增成功
     */
    ChatLog insertByBo(ChatLogBo bo);

    /**
     * 修改消息记录
     *
     * @param bo 消息记录
     * @return 是否修改成功
     */
    Boolean updateByBo(ChatLogBo bo);

    /**
     * 校验并批量删除消息记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);
}
