<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.cyly.xchat.mapper.ChatUserImMapper">

    <resultMap type="org.dromara.cyly.xchat.domain.ChatUserIm" id="SysUserImResult">
        <result property="accid" column="accid"/>
        <result property="token" column="token"/>
        <result property="deptId" column="dept_id"/>
        <result property="name" column="name"/>
        <result property="nickName" column="nick_name"/>
        <result property="sign" column="sign"/>
        <result property="userType" column="user_type"/>
        <result property="email" column="email"/>
        <result property="birth" column="birth"/>
        <result property="mobile" column="mobile"/>
        <result property="gender" column="gender"/>
        <result property="icon" column="icon"/>
        <result property="password" column="password"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="loginIp" column="login_ip"/>
        <result property="loginDate" column="login_date"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="ex" column="ex"/>
        <result property="userId" column="user_id"/>
    </resultMap>

    <sql id="selectSysUserImVo">
        select accid, token, dept_id, name, nick_name, sign, user_type, email,
        birth, mobile, gender, icon, password, status, del_flag, login_ip,
        login_date, create_by, create_time, update_by, update_time, remark,
        ex,user_id from chat_user_im
    </sql>

    <select id="selectSysUserImList" parameterType="org.dromara.cyly.xchat.domain.ChatUserIm" resultMap="SysUserImResult">
        <include refid="selectSysUserImVo"/>
        <where>
            <if test="token != null  and token != ''">and token = #{token}</if>
            <if test="deptId != null ">and dept_id = #{deptId}</if>
            <if test="name != null  and name != ''">and name like concat('%', #{name}, '%')</if>
            <if test="nickName != null  and nickName != ''">and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="sign != null  and sign != ''">and sign = #{sign}</if>
            <if test="userType != null  and userType != ''">and user_type = #{userType}</if>
            <if test="email != null  and email != ''">and email = #{email}</if>
            <if test="birth != null  and birth != ''">and birth = #{birth}</if>
            <if test="mobile != null  and mobile != ''">and mobile = #{mobile}</if>
            <if test="gender != null ">and gender = #{gender}</if>
            <if test="icon != null  and icon != ''">and icon = #{icon}</if>
            <if test="password != null  and password != ''">and password = #{password}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="loginIp != null  and loginIp != ''">and login_ip = #{loginIp}</if>
            <if test="loginDate != null ">and login_date = #{loginDate}</if>
            <if test="ex != null  and ex != ''">and ex = #{ex}</if>
        </where>
    </select>

    <select id="selectSysUserImByAccid" parameterType="String" resultMap="SysUserImResult">
        <include refid="selectSysUserImVo"/>
        where accid = #{accid}
    </select>

    <select id="selectSysUserImByMobile" parameterType="String" resultMap="SysUserImResult">
        SELECT
        im.accid,
        im.token,
        u.dept_id,
        u.user_name,
        u.nick_name,
        im.sign,
        u.user_type,
        u.email,
        im.birth,
        im.mobile,
        im.gender,
        im.icon,
        u.password,
        u.status,
        u.chat_identity,
        im.del_flag,
        im.login_ip,
        im.login_date,
        im.create_by,
        im.create_time,
        im.update_by,
        im.update_time,
        im.remark,
        im.ex,
        u.user_id
        FROM chat_user_im im
        LEFT JOIN app_user u ON u.user_id = im.user_id
        WHERE im.mobile = #{mobile}
    </select>

    <select id="selectAllSysUserImList" resultMap="SysUserImResult">
        SELECT
        accid,
        token,
        u.dept_id,
        NAME,
        u.nick_name,
        sign,
        u.user_type,
        u.email,
        birth,
        mobile,
        gender,
        icon,
        u.PASSWORD,
        u.STATUS,
        u.chat_identity,
        im.del_flag,
        im.login_ip,
        im.login_date,
        im.create_by,
        im.create_time,
        im.update_by,
        im.update_time,
        im.remark,
        im.ex,
        u.user_id
        FROM
        chat_user_im im
        left join app_user u on u.user_id = im.user_id
        where (u.dept_id = #{deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId},
        ancestors) ))
        and im.accid != #{accid}
    </select>

    <insert id="insertSysUserIm" parameterType="org.dromara.cyly.xchat.domain.ChatUserIm">
        insert into chat_user_im
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accid != null">accid,</if>
            <if test="token != null">token,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="name != null">name,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="sign != null">sign,</if>
            <if test="userType != null">user_type,</if>
            <if test="email != null">email,</if>
            <if test="birth != null">birth,</if>
            <if test="mobile != null and mobile != ''">mobile,</if>
            <if test="gender != null">gender,</if>
            <if test="icon != null">icon,</if>
            <if test="password != null">password,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="loginIp != null">login_ip,</if>
            <if test="loginDate != null">login_date,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="ex != null">ex,</if>
            <if test="userId != null">user_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accid != null">#{accid},</if>
            <if test="token != null">#{token},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="name != null">#{name},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="sign != null">#{sign},</if>
            <if test="userType != null">#{userType},</if>
            <if test="email != null">#{email},</if>
            <if test="birth != null">#{birth},</if>
            <if test="mobile != null and mobile != ''">#{mobile},</if>
            <if test="gender != null">#{gender},</if>
            <if test="icon != null">#{icon},</if>
            <if test="password != null">#{password},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="loginIp != null">#{loginIp},</if>
            <if test="loginDate != null">#{loginDate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="ex != null">#{ex},</if>
            <if test="userId != null">#{userId},</if>
        </trim>
    </insert>

    <update id="updateSysUserIm" parameterType="org.dromara.cyly.xchat.domain.ChatUserIm">
        update chat_user_im
        <trim prefix="SET" suffixOverrides=",">
            <if test="token != null">token = #{token},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="sign != null">sign = #{sign},</if>
            <if test="userType != null">user_type = #{userType},</if>
            <if test="email != null">email = #{email},</if>
            <if test="birth != null">birth = #{birth},</if>
            <if test="mobile != null and mobile != ''">mobile = #{mobile},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="icon != null">icon = #{icon},</if>
            <if test="password != null">password = #{password},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="loginIp != null">login_ip = #{loginIp},</if>
            <if test="loginDate != null">login_date = #{loginDate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="ex != null">ex = #{ex},</if>
        </trim>
        where accid = #{accid}
    </update>

    <delete id="deleteSysUserImByAccid" parameterType="String">
        delete from chat_user_im where accid = #{accid}
    </delete>

    <delete id="deleteSysUserImByAccids" parameterType="String">
        delete from chat_user_im where accid in
        <foreach item="accid" collection="array" open="(" separator="," close=")">
            #{accid}
        </foreach>
    </delete>
</mapper>
