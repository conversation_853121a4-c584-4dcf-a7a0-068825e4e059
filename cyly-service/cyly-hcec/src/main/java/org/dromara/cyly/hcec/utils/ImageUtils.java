package org.dromara.cyly.hcec.utils;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

/**
 * 图片工具类
 *
 * <AUTHOR>
public class ImageUtils {

    public static String getHashFromUrl(String imageUrl) throws IOException, NoSuchAlgorithmException {
        try (InputStream inputStream = new URL(imageUrl).openStream()) {
            MessageDigest digest = MessageDigest.getInstance("MD5");
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                digest.update(buffer, 0, bytesRead);
            }
            byte[] hash = digest.digest();
            return Base64.getEncoder().encodeToString(hash);
        }
    }

    public static boolean isSameImage(String oldImageUrl, String newImageUrl) throws IOException, NoSuchAlgorithmException {
        return getHashFromUrl(oldImageUrl).equals(getHashFromUrl(newImageUrl));
    }
}
