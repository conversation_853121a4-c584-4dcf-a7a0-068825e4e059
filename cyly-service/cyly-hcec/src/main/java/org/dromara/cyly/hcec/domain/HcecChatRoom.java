package org.dromara.cyly.hcec.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 聊天室信息对象 hcec_chat_room
 *
 * <AUTHOR>
 * @date 2025-02-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("hcec_chat_room")
public class HcecChatRoom extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 房间id
     */
    private String roomId;

    /**
     * 房间名字
     */
    private String roomName;

    /**
     * 房主id
     */
    private String homeownerId;

    /**
     * 房主名字
     */
    private String homeownerName;

    /**
     * 创建时间
     */
    private String creationTime;

    /**
     * 现在房间成员
     */
    private String nowRoomMembers;

    /**
     * 全部房间成员
     */
    private String allRoomMembers;

    /**
     * 房间状态（1表示房间正在使用，0表示关闭）
     */
    private String state;


}
