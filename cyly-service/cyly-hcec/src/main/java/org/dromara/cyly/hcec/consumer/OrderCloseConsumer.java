package org.dromara.cyly.hcec.consumer;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.MessageExt;
import org.dromara.common.rocketmq.annotation.RocketMqConsumer;
import org.dromara.common.rocketmq.constant.RocketMqConstants;
import org.dromara.common.rocketmq.dto.MessageDTO;
import org.dromara.common.rocketmq.listener.AbstractRocketMqListener;
import org.dromara.common.rocketmq.utils.DeadLetterUtils;
import org.dromara.common.rocketmq.utils.RocketMqProducerUtil;
import org.dromara.cyly.hcec.constant.RocketMqTopicConstants;
import org.dromara.cyly.hcec.service.IHcecOrdersService;
import org.springframework.stereotype.Component;

/**
 * 订单关闭消费者
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMqConsumer(
    consumerGroup = "order_close_consumer_group",
    topic = RocketMqTopicConstants.ORDER_TOPIC,
    tag = RocketMqTopicConstants.ORDER_CLOSE_TAG
)
public class OrderCloseConsumer extends AbstractRocketMqListener {

    private final IHcecOrdersService ordersService;

    @Override
    protected void handleMessage(MessageExt message) {
        // 获取消息内容（订单ID）
        String orderId = new String(message.getBody());
        log.info("接收到订单关闭消息，订单ID: {}, 重试次数: {}", orderId, message.getReconsumeTimes());

        // 检查是否需要发送到死信队列
        if (message.getReconsumeTimes() >= RocketMqConstants.DeadLetter.MAX_RETRY_TIMES) {
            log.warn("订单关闭消息重试次数已达上限，发送到死信队列，订单ID: {}, 重试次数: {}",
                orderId, message.getReconsumeTimes());
            DeadLetterUtils.sendToDeadLetter(message, orderId, "订单关闭重试次数达到上限");
            return;
        }

        try {
            // 调用订单服务关闭订单
            boolean result = ordersService.updateOrderStatus(Long.parseLong(orderId));
            if (result) {
                log.info("订单关闭成功，订单ID: {}", orderId);
            } else {
                log.warn("订单关闭失败，准备重新发送延迟消息，订单ID: {}", orderId);
                // 如果关闭失败，重新发送延迟消息（5分钟后再次尝试）
                sendDelayedMessage(orderId);
            }
        } catch (Exception e) {
            log.error("处理订单关闭消息异常，订单ID: {}, 异常信息: {}", orderId, e.getMessage(), e);

            // 如果重试次数接近上限，发送到死信队列
            if (message.getReconsumeTimes() >= RocketMqConstants.DeadLetter.MAX_RETRY_TIMES - 1) {
                log.warn("订单关闭处理异常且重试次数接近上限，发送到死信队列，订单ID: {}", orderId);
                DeadLetterUtils.sendToDeadLetter(message, orderId, e.getMessage());
            } else {
                // 否则重新发送延迟消息
                sendDelayedMessage(orderId);
            }
        }
    }


    @Override
    protected boolean shouldRetry(Exception e) {
        // 不使用RocketMQ的重试机制，而是通过重新发送延迟消息来实现重试
        return false;
    }

    /**
     * 发送延迟消息
     *
     * @param orderId 订单ID
     */
    private void sendDelayedMessage(String orderId) {
        // 构建延迟消息
        MessageDTO<String> messageDTO = MessageDTO.delay(
            RocketMqTopicConstants.ORDER_TOPIC,
            RocketMqTopicConstants.ORDER_CLOSE_TAG,
            orderId,
            RocketMqConstants.DelayLevel.LEVEL_9
        );

        // 异步发送延迟消息
        RocketMqProducerUtil.asyncSend(messageDTO, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                log.info("订单关闭延迟消息发送成功，订单ID: {}, 延迟级别: {}", orderId, RocketMqConstants.DelayLevel.LEVEL_9);
            }

            @Override
            public void onException(Throwable throwable) {
                log.error("订单关闭延迟消息发送失败，订单ID: {}, 延迟级别: {}, 异常信息: {}",
                    orderId, RocketMqConstants.DelayLevel.LEVEL_9, throwable.getMessage(), throwable);
            }
        });
    }
}
