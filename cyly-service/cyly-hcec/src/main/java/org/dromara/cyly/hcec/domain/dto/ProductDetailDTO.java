package org.dromara.cyly.hcec.domain.dto;


import lombok.Data;
import org.dromara.cyly.hcec.domain.HcecProductSku;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商品详情DTO
 */
@Data
public class ProductDetailDTO {
    private Long id;
    // 商品名称
    private String name;
    // 商品价格
    private BigDecimal price;
    // 商品描述
    private String description;
    // 库存数量
    private Integer stockQuantity;
    // 商品分类id
    private Integer categoryId;
    // 商品图片
    private List<String> imageUrls;
    // 商品规格
    private List<HcecProductSku> productSkus;
    //富文本
    private String context;
}
