package org.dromara.cyly.hcec.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.cyly.hcec.domain.HcecProductSku;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 商品sku视图对象 hcec_product_sku
 *
 * <AUTHOR>
 * @date 2025-02-07
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = HcecProductSku.class)
public class HcecProductSkuVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long productId;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String color;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String model;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long price;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long stockQuantity;

    /**
     * 库存锁定数量
     */
    @ExcelProperty(value = "库存锁定数量")
    private Long quantityLock;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Date created;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Date updated;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String colorSpec;


}
