package org.dromara.cyly.hcec.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class OrdersVo {
    @JsonSerialize(using = ToStringSerializer.class)
    private Long orderId;
    private Integer productQuantity;//商品数量
    private BigDecimal totalPrice;//总价
    private String address;//地址
    private String phone;//电话
    private String customerName;//老人名
    private Integer orderStatus;//订单状态
    private List<ProductVo> products; // 商品列表，类型为 ProductVo

    @Data
    public static class ProductVo { // 定义内部类 ProductVo
        private String name;        // 商品名称
        private BigDecimal price;   // 单商品总价
        private Integer buyNum; //购买数量
        private String imageUrl; //图片链接
    }
}
