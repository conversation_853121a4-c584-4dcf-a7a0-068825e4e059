package org.dromara.cyly.hcec.domain.bo;

import org.dromara.cyly.hcec.domain.HcecProduct;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 产品信息业务对象 hcec_product
 *
 * <AUTHOR>
 * @date 2025-02-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = HcecProduct.class, reverseConvertGenerate = false)
public class HcecProductBo extends BaseEntity {

    /**
     * 产品ID
     */
    @NotNull(message = "产品ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 产品的名称
     */
    @NotBlank(message = "产品的名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 产品的描述信息
     */
    @NotBlank(message = "产品的描述信息不能为空", groups = { AddGroup.class, EditGroup.class })
    private String description;

    /**
     * 产品分类id
     */
    @NotNull(message = "产品分类id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long categoryId;

    /**
     * 产品的售价
     */
    @NotNull(message = "产品的售价不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long price;

    /**
     * 提供产品的供应商的ID
     */
    @NotNull(message = "提供产品的供应商的ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long supplierId;

    /**
     * 产品信息变更时的时间
     */
    @NotNull(message = "产品信息变更时的时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date updated;


}
