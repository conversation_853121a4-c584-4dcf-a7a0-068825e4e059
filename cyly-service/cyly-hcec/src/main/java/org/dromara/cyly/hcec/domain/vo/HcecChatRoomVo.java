package org.dromara.cyly.hcec.domain.vo;

import org.dromara.cyly.hcec.domain.HcecChatRoom;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 聊天室信息视图对象 hcec_chat_room
 *
 * <AUTHOR>
 * @date 2025-02-02
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = HcecChatRoom.class)
public class HcecChatRoomVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识
     */
    @ExcelProperty(value = "唯一标识")
    private Long id;

    /**
     * 房间id
     */
    @ExcelProperty(value = "房间id")
    private String roomId;

    /**
     * 房间名字
     */
    @ExcelProperty(value = "房间名字")
    private String roomName;

    /**
     * 房主id
     */
    @ExcelProperty(value = "房主id")
    private String homeownerId;

    /**
     * 房主名字
     */
    @ExcelProperty(value = "房主名字")
    private String homeownerName;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private String creationTime;

    /**
     * 现在房间成员
     */
    @ExcelProperty(value = "现在房间成员")
    private String nowRoomMembers;

    /**
     * 全部房间成员
     */
    @ExcelProperty(value = "全部房间成员")
    private String allRoomMembers;

    /**
     * 房间状态（1表示房间正在使用，0表示关闭）
     */
    @ExcelProperty(value = "房间状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=表示房间正在使用，0表示关闭")
    private String state;

    /**
     * 房间当前成员数量（非数据库字段，由nowRoomMembers计算得出）
     */
    private Integer memberCount;

}
