package org.dromara.cyly.hcec.domain.vo;

import org.dromara.common.translation.annotation.Translation;
import org.dromara.common.translation.constant.TransConstant;
import org.dromara.cyly.hcec.domain.HcecUserCart;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;



/**
 * 用户购物车信息视图对象 hcec_user_cart
 *
 * <AUTHOR>
 * @date 2025-02-08
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = HcecUserCart.class)
public class HcecUserCartVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 用户id
     */
    @ExcelProperty(value = "用户id")
    private Long customerId;

    /**
     * 商品id
     */
    @ExcelProperty(value = "商品id")
    private Long productId;

    /**
     * 商品的skuid
     */
    @ExcelProperty(value = "商品的skuid")
    private Long productSkuId;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称")
    private String productName;

    /**
     * 图片
     */
    @ExcelProperty(value = "图片")
    private String image;

    /**
     * 图片Url
     */
    @Translation(type = TransConstant.OSS_ID_TO_URL, mapper = "image")
    private String imageUrl;
    /**
     * 购买数量
     */
    @ExcelProperty(value = "购买数量")
    private Long buyNum;

    /**
     * 价格
     */
    @ExcelProperty(value = "价格")
    private BigDecimal price;


}
