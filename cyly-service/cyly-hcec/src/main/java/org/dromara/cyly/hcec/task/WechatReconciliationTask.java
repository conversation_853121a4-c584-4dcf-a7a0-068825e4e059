package org.dromara.cyly.hcec.task;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.enums.FormatsType;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.cyly.hcec.service.IHcecWechatReconciliationService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;

/**
 * 微信支付对账定时任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WechatReconciliationTask {

    private final IHcecWechatReconciliationService wechatReconciliationService;

    /**
     * 每天凌晨2点执行对账任务
     * 对前一天的交易进行对账
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void executeReconciliation() {
        // 获取前一天的日期
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        Date yesterday = calendar.getTime();

        log.info("开始执行微信支付自动对账任务，对账日期: {}", DateUtils.parseDateToStr(FormatsType.YYYY_MM_DD, yesterday));

        try {
            // 下载对账单
            boolean downloadResult = wechatReconciliationService.downloadBill(yesterday);

            if (!downloadResult) {
                log.error("自动对账任务下载对账单失败，对账日期: {}", DateUtils.parseDateToStr(FormatsType.YYYY_MM_DD, yesterday));
                return;
            }

            // 等待一秒，确保数据已经写入数据库
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }

            // 执行对账
            String reconcileResult = wechatReconciliationService.reconcile(yesterday);

            log.info("自动对账任务执行完成，对账日期: {}, 对账结果: {}",
                DateUtils.parseDateToStr(FormatsType.YYYY_MM_DD, yesterday), reconcileResult);
        } catch (Exception e) {
            log.error("自动对账任务执行异常，对账日期: {}, 异常信息: {}",
                DateUtils.parseDateToStr(FormatsType.YYYY_MM_DD, yesterday), e.getMessage(), e);
        }
    }
}
