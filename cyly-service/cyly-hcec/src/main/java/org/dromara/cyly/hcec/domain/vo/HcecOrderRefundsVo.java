package org.dromara.cyly.hcec.domain.vo;

import org.dromara.cyly.hcec.domain.HcecOrderRefunds;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 订单退款信息视图对象 hcec_order_refunds
 *
 * <AUTHOR>
 * @date 2025-02-04
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = HcecOrderRefunds.class)
public class HcecOrderRefundsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 退款工单主键id
     */
    @ExcelProperty(value = "退款工单主键id")
    private Long id;

    /**
     * 微信支付退款号
     */
    @ExcelProperty(value = "微信支付退款号")
    private String refundId;

    /**
     * 订单id
     */
    @ExcelProperty(value = "订单id")
    private Long orderId;

    /**
     * 退款金额
     */
    @ExcelProperty(value = "退款金额")
    private Long returnAmount;

    /**
     * 退款姓名
     */
    @ExcelProperty(value = "退款姓名")
    private String returnName;

    /**
     * 退款电话
     */
    @ExcelProperty(value = "退款电话")
    private String returnPhone;

    /**
     * 退款原因
     */
    @ExcelProperty(value = "退款原因")
    private String reason;

    /**
     * 描述
     */
    @ExcelProperty(value = "描述")
    private String description;

    /**
     * 凭证图片，以逗号隔开
     */
    @ExcelProperty(value = "凭证图片，以逗号隔开")
    private String proofPics;

    /**
     * 退款状态:1退款中2退款成功3退款失败
     */
    @ExcelProperty(value = "退款状态:1退款中2退款成功3退款失败")
    private Long status;


}
