package org.dromara.cyly.hcec.domain.vo;

import org.dromara.cyly.hcec.domain.HcecProductDetail;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 商品详情视图对象 hcec_product_detail
 *
 * <AUTHOR>
 * @date 2025-02-07
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = HcecProductDetail.class)
public class HcecProductDetailVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long productId;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String context;

    /**
     *
     */
    @ExcelProperty(value = "")
    private String imageUrls;


}
