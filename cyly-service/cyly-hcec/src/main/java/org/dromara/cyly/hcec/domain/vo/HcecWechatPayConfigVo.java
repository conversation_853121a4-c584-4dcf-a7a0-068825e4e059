package org.dromara.cyly.hcec.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.cyly.hcec.domain.HcecWechatPayConfig;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 支付配置视图对象 hcec_wechat_pay_config
 *
 * <AUTHOR>
 * @date 2025-02-08
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = HcecWechatPayConfig.class)
public class HcecWechatPayConfigVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ExcelProperty(value = "主键id")
    private Long id;

    /**
     * 商户号
     */
    @ExcelProperty(value = "商户号")
    private String merchantId;

    /**
     * 公众号id
     */
    @ExcelProperty(value = "公众号id")
    private String appId;

    /**
     * 商户APIV3密钥
     */
    @ExcelProperty(value = "商户APIV3密钥")
    private String apiV3Key;

    /**
     * 商户证书序列号
     */
    @ExcelProperty(value = "商户证书序列号")
    private String merchantSerialNumber;

    /**
     * 商户API私钥路径
     */
    @ExcelProperty(value = "商户API私钥路径")
    private String privateKeyPath;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Date updeteTime;


}
