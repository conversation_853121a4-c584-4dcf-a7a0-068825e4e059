package org.dromara.cyly.hcec.domain.dto;


import lombok.Data;
import org.dromara.cyly.hcec.domain.HcecUserCart;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CreateOrdersDTO {
    /**
     * 产品ID：订单中产品的ID
     */
    private Long productSkuId;
    /**
     * 产品数量：订单中产品的数量
     */
    private Integer productQuantity;
    /**
     * 客户ID：下订单的客户ID
     */
    private Long customerId;
    /**
     * 社区联系方式：社区负责人的手机号
     */
    private String communityContact;

    /**
     * 总价：订单的总价
     */
    private BigDecimal totalPrice;

    /**
     * 发货地址
     */
    private String address;

    /**
     * 客户姓名：下单的老人的姓名
     */
    private String customerName;
    /**
     * 客户联系方式：下单老人的手机号
     */
    private String customerContact;
    /**
     * 订单状态：订单的当前状态（例如：待发货、已发货等）
     */
    private Integer orderStatus;
    /**
     * 支付方式：1、微信，2、支付宝
     */
    private Integer pay;

    private List<HcecUserCart> userCarts;
}
