package org.dromara.cyly.hcec.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.dromara.common.core.utils.Threads;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 线程池配置类
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class ThreadPoolConfig {

    /**
     * 获取服务器CPU核心数
     */
    private final int CPU_COUNT = Runtime.getRuntime().availableProcessors();

    /**
     * 创建支付任务调度线程池
     *
     * @return 线程池实例
     */
    @Bean(name = "paymentScheduledExecutorService")
    public ScheduledExecutorService paymentScheduledExecutorService() {
        // 支付任务是IO密集型，设置核心线程数为CPU核心数
        int corePoolSize = Math.max(2, CPU_COUNT);

        // 创建线程工厂
        ThreadFactory threadFactory = createThreadFactory("payment-schedule-pool");

        // 创建拒绝策略处理器
        RejectedExecutionHandler handler = createRejectedExecutionHandler("支付任务");

        // 创建线程池
        ScheduledThreadPoolExecutor executor = new ScheduledThreadPoolExecutor(
            corePoolSize,
            threadFactory,
            handler
        ) {
            @Override
            protected void afterExecute(Runnable r, Throwable t) {
                super.afterExecute(r, t);
                Threads.printException(r, t);
            }
        };

        // 设置线程池参数
        configureScheduledExecutor(executor);

        return executor;
    }

    /**
     * 创建退款任务调度线程池
     *
     * @return 线程池实例
     */
    @Bean(name = "refundScheduledExecutorService")
    public ScheduledExecutorService refundScheduledExecutorService() {
        // 退款任务是IO密集型，设置核心线程数为CPU核心数
        int corePoolSize = Math.max(2, CPU_COUNT);

        // 创建线程工厂
        ThreadFactory threadFactory = createThreadFactory("refund-schedule-pool");

        // 创建拒绝策略处理器
        RejectedExecutionHandler handler = createRejectedExecutionHandler("退款任务");

        // 创建线程池
        ScheduledThreadPoolExecutor executor = new ScheduledThreadPoolExecutor(
            corePoolSize,
            threadFactory,
            handler
        ) {
            @Override
            protected void afterExecute(Runnable r, Throwable t) {
                super.afterExecute(r, t);
                Threads.printException(r, t);
            }
        };

        // 设置线程池参数
        configureScheduledExecutor(executor);

        return executor;
    }

    /**
     * 创建通用任务调度线程池
     *
     * @return 线程池实例
     */
    @Bean(name = "commonScheduledExecutorService")
    public ScheduledExecutorService commonScheduledExecutorService() {
        // 通用任务，设置核心线程数为CPU核心数的一半
        int corePoolSize = Math.max(2, CPU_COUNT / 2);

        // 创建线程工厂
        ThreadFactory threadFactory = createThreadFactory("common-schedule-pool");

        // 创建拒绝策略处理器
        RejectedExecutionHandler handler = createRejectedExecutionHandler("通用任务");

        // 创建线程池
        ScheduledThreadPoolExecutor executor = new ScheduledThreadPoolExecutor(
            corePoolSize,
            threadFactory,
            handler
        ) {
            @Override
            protected void afterExecute(Runnable r, Throwable t) {
                super.afterExecute(r, t);
                Threads.printException(r, t);
            }
        };

        // 设置线程池参数
        configureScheduledExecutor(executor);

        return executor;
    }

    /**
     * 创建微信订单查询线程池
     *
     * @return 线程池实例
     */
    @Bean(name = "wechatOrderQueryExecutor")
    public ThreadPoolExecutor wechatOrderQueryExecutor() {
        // 订单查询是IO密集型，设置核心线程数为CPU核心数
        int corePoolSize = Math.max(2, CPU_COUNT);
        // 最大线程数为核心线程数的2倍
        int maximumPoolSize = corePoolSize * 2;
        // 空闲线程存活时间
        long keepAliveTime = 60L;
        // 使用有界队列，避免OOM
        int queueCapacity = 1000;

        // 创建线程工厂
        ThreadFactory threadFactory = createThreadFactory("wechat-order-query-pool");

        // 创建拒绝策略处理器
        RejectedExecutionHandler handler = createRejectedExecutionHandler("微信订单查询");

        // 创建线程池
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
            corePoolSize,
            maximumPoolSize,
            keepAliveTime,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(queueCapacity),
            threadFactory,
            handler
        ) {
            @Override
            protected void afterExecute(Runnable r, Throwable t) {
                super.afterExecute(r, t);
                Threads.printException(r, t);
            }
        };

        // 设置线程池参数
        configureThreadPoolExecutor(executor);

        return executor;
    }

    /**
     * 创建线程工厂
     *
     * @param namePrefix 线程名称前缀
     * @return 线程工厂
     */
    private ThreadFactory createThreadFactory(String namePrefix) {
        return new BasicThreadFactory.Builder()
            .namingPattern(namePrefix + "-%d")
            .daemon(true)
            .build();
    }

    /**
     * 创建拒绝策略处理器
     *
     * @param taskType 任务类型
     * @return 拒绝策略处理器
     */
    private RejectedExecutionHandler createRejectedExecutionHandler(String taskType) {
        return new ThreadPoolExecutor.CallerRunsPolicy() {
            @Override
            public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
                log.warn("[线程池] {} 线程池已满，任务被拒绝执行，采用调用者运行策略", taskType);
                super.rejectedExecution(r, e);
            }
        };
    }

    /**
     * 配置ScheduledThreadPoolExecutor参数
     *
     * @param executor 线程池执行器
     */
    private void configureScheduledExecutor(ScheduledThreadPoolExecutor executor) {
        // 设置线程池在关闭时等待所有任务完成
        executor.setExecuteExistingDelayedTasksAfterShutdownPolicy(false);
        executor.setRemoveOnCancelPolicy(true);
    }

    /**
     * 配置ThreadPoolExecutor参数
     *
     * @param executor 线程池执行器
     */
    private void configureThreadPoolExecutor(ThreadPoolExecutor executor) {
        // 设置核心线程预启动
        executor.prestartAllCoreThreads();
        // 允许核心线程超时
        executor.allowCoreThreadTimeOut(true);
    }
}
