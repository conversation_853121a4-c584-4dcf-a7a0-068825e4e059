package org.dromara.cyly.hcec.domain.bo;

import org.dromara.cyly.hcec.domain.HcecChatMessage;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 聊天室消息记录业务对象 hcec_chat_message
 *
 * <AUTHOR>
 * @date 2025-02-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = HcecChatMessage.class, reverseConvertGenerate = false)
public class HcecChatMessageBo extends BaseEntity {

    /**
     * 唯一标识符，主键
     */
    @NotNull(message = "唯一标识符，主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 聊天室ID：消息发生所在聊天室的ID
     */
    @NotNull(message = "聊天室ID：消息发生所在聊天室的ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long chatRoomId;

    /**
     * 发起人ID：发出消息用户的ID
     */
    @NotNull(message = "发起人ID：发出消息用户的ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long senderId;

    /**
     * 消息内容：记录消息内容（仅限文本）
     */
    @NotBlank(message = "消息内容：记录消息内容（仅限文本）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String messageContent;

    /**
     * 发出时间：消息发出的时间
     */
    @NotNull(message = "发出时间：消息发出的时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date sentTime;


}
