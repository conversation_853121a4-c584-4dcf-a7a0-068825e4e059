package org.dromara.cyly.hcec.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.redis.utils.IdempotentUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.satoken.utils.AppLoginHelper;
import org.dromara.common.rocketmq.constant.RocketMqConstants;
import org.dromara.common.rocketmq.dto.MessageDTO;
import org.dromara.common.rocketmq.utils.RocketMqProducerUtil;
import org.dromara.cyly.hcec.constant.RocketMqTopicConstants;
import org.dromara.cyly.hcec.domain.*;
import org.dromara.cyly.hcec.domain.dto.CreateOrdersDTO;
import org.dromara.cyly.hcec.domain.query.OrdersQuery;
import org.dromara.cyly.hcec.domain.vo.OrdersVo;
import org.dromara.cyly.hcec.enums.OrderRefundEnum;
import org.dromara.cyly.hcec.enums.OrderStatusEnum;
import org.dromara.cyly.hcec.enums.PayEnum;
import org.dromara.cyly.hcec.enums.PaymentStatusEnum;
import org.dromara.cyly.hcec.mapper.HcecOrderRefundsMapper;
import org.dromara.cyly.hcec.mapper.HcecProductMapper;
import org.dromara.cyly.hcec.mapper.HcecProductSkuMapper;
import org.dromara.cyly.hcec.mapper.HcecUserCartMapper;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.dromara.cyly.hcec.domain.bo.HcecOrdersBo;
import org.dromara.cyly.hcec.domain.vo.HcecOrdersVo;
import org.dromara.cyly.hcec.mapper.HcecOrdersMapper;
import org.dromara.cyly.hcec.service.IHcecOrdersService;
import org.dromara.cyly.hcec.mapper.HcecOrdersProductMapper;
import org.dromara.cyly.hcec.mapper.HcecProductImageMapper;
import org.springframework.transaction.annotation.Transactional;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.BiFunction;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 订单：记录订单的详细信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-04
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class HcecOrdersServiceImpl implements IHcecOrdersService {

    private final HcecOrdersMapper baseMapper;

    private final HcecProductMapper productMapper;

    private final HcecProductImageMapper productImageMapper;

    private final HcecUserCartMapper userCartMapper;

    private final HcecOrdersProductMapper ordersProductMapper;

    private final HcecProductSkuMapper productSkuMapper;

    private final HcecOrderRefundsMapper orderRefundsMapper;

    /**
     * 查询订单：记录订单的详细信息
     *
     * @param orderId 主键
     * @return 订单：记录订单的详细信息
     */
    @Override
    public HcecOrdersVo queryById(Long orderId){
        log.info("开始查询订单信息，订单ID: {}", orderId);
        HcecOrdersVo ordersVo = baseMapper.selectVoById(orderId);
        if (ordersVo != null) {
            log.info("查询订单信息成功，订单ID: {}, 客户名称: {}", orderId, ordersVo.getCustomerName());
        } else {
            log.warn("未找到订单信息，订单ID: {}", orderId);
        }
        return ordersVo;
    }

    /**
     * 分页查询订单：记录订单的详细信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 订单：记录订单的详细信息分页列表
     */
    @Override
    public TableDataInfo<HcecOrdersVo> queryPageList(HcecOrdersBo bo, PageQuery pageQuery) {
        log.info("开始分页查询订单信息列表，页码: {}, 每页数量: {}", pageQuery.getPageNum(), pageQuery.getPageSize());
        LambdaQueryWrapper<HcecOrders> lqw = buildQueryWrapper(bo);
        Page<HcecOrdersVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        log.info("分页查询订单信息列表完成，总记录数: {}, 总页数: {}", result.getTotal(), result.getPages());
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的订单：记录订单的详细信息列表
     *
     * @param bo 查询条件
     * @return 订单：记录订单的详细信息列表
     */
    @Override
    public List<HcecOrdersVo> queryList(HcecOrdersBo bo) {
        LambdaQueryWrapper<HcecOrders> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 构建查询条件包装器
     *
     * @param bo 查询条件业务对象
     * @return 查询条件包装器
     */
    private LambdaQueryWrapper<HcecOrders> buildQueryWrapper(HcecOrdersBo bo) {
        log.debug("开始构建订单查询条件");
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<HcecOrders> lqw = Wrappers.lambdaQuery();

        // 基本信息查询条件
        lqw.eq(bo.getTransactionId() != null, HcecOrders::getTransactionId, bo.getTransactionId());
        lqw.eq(bo.getProductQuantity() != null, HcecOrders::getProductQuantity, bo.getProductQuantity());
        lqw.eq(bo.getTotalPrice() != null, HcecOrders::getTotalPrice, bo.getTotalPrice());
        lqw.eq(StringUtils.isNotBlank(bo.getAddress()), HcecOrders::getAddress, bo.getAddress());

        // 客户信息查询条件
        lqw.eq(bo.getCustomerId() != null, HcecOrders::getCustomerId, bo.getCustomerId());
        lqw.eq(StringUtils.isNotBlank(bo.getCommunityContact()), HcecOrders::getCommunityContact, bo.getCommunityContact());
        lqw.like(StringUtils.isNotBlank(bo.getCustomerName()), HcecOrders::getCustomerName, bo.getCustomerName());
        lqw.eq(StringUtils.isNotBlank(bo.getCustomerContact()), HcecOrders::getCustomerContact, bo.getCustomerContact());

        // 订单状态查询条件
        lqw.eq(bo.getOrderStatus() != null, HcecOrders::getOrderStatus, bo.getOrderStatus());

        // 支付状态查询条件
        lqw.eq(bo.getPaymentStatus() != null, HcecOrders::getPaymentStatus, bo.getPaymentStatus());

        // 支付信息查询条件
        lqw.eq(bo.getPay() != null, HcecOrders::getPay, bo.getPay());
        lqw.eq(bo.getPayDate() != null, HcecOrders::getPayDate, bo.getPayDate());

        // 时间信息查询条件
        // 使用BaseEntity中的createTime替代creationDate
        lqw.eq(bo.getShipmentDate() != null, HcecOrders::getShipmentDate, bo.getShipmentDate());
        lqw.eq(bo.getDeliveryDate() != null, HcecOrders::getDeliveryDate, bo.getDeliveryDate());

        log.debug("订单查询条件构建完成");
        return lqw;
    }

    /**
     * 新增订单：记录订单的详细信息
     *
     * @param bo 订单：记录订单的详细信息
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(HcecOrdersBo bo) {
        HcecOrders add = MapstructUtils.convert(bo, HcecOrders.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setOrderId(add.getOrderId());
        }
        return flag;
    }

    /**
     * 修改订单：记录订单的详细信息
     *
     * @param bo 订单：记录订单的详细信息
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(HcecOrdersBo bo) {
        HcecOrders update = MapstructUtils.convert(bo, HcecOrders.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(HcecOrders entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除订单：记录订单的详细信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public boolean saveOrUpdateBatch(List<HcecOrders> orders) {
        return baseMapper.insertOrUpdateBatch(orders);
    }

    /**
     * 更新退款状态
     *
     * @param refundId 退款单ID
     * @param result 退款结果
     * @return 是否更新成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRefundStatus(String refundId, Map<String, Object> result) {
        try {
            log.info("开始更新退款状态，退款单ID: {}", refundId);

            // 获取退款信息
            String outRefundNo = (String) result.get("out_refund_no");
            String wechatRefundId = (String) result.get("refundId");

            if (outRefundNo == null || wechatRefundId == null) {
                log.error("退款信息不完整，无法更新退款状态，退款单ID: {}", refundId);
                return false;
            }

            // 先查询退款单信息
            HcecOrderRefunds orderRefunds = orderRefundsMapper.selectById(Long.parseLong(refundId));
            if (orderRefunds == null) {
                log.error("退款单不存在，无法更新退款状态，退款单ID: {}", refundId);
                return false;
            }

            // 从退款单中获取订单ID
            Long orderId = orderRefunds.getOrderId();
            if (orderId == null) {
                log.error("退款单中订单ID为空，无法更新退款状态，退款单ID: {}", refundId);
                return false;
            }

            log.debug("从退款单获取到订单ID: {}", orderId);

            // 查询订单
            HcecOrders order = baseMapper.selectById(orderId);
            if (order == null) {
                log.error("订单不存在，无法更新退款状态，订单ID: {}, 退款单ID: {}", orderId, refundId);
                return false;
            }

            // 更新订单状态为已退款
            order.setOrderStatus(OrderStatusEnum.REFUNDED.getCode());
            // 确保支付方式已设置为微信支付
            if (order.getPay() == null || order.getPay() != PayEnum.WECHAT.getCode()) {
                order.setPay(PayEnum.WECHAT.getCode());
            }

            // 更新订单
            int rows = baseMapper.updateById(order);

            // 同时更新退款单状态
            orderRefunds.setRefundId(wechatRefundId);
            // 设置为退款成功状态
            orderRefunds.setStatus(OrderRefundEnum.REFUNDED.getCode());
            log.debug("更新退款单状态为: {}", OrderRefundEnum.REFUNDED.getDescription());
            orderRefundsMapper.updateById(orderRefunds);

            log.info("订单退款状态更新成功，订单ID: {}, 退款单ID: {}", orderId, refundId);
            return rows > 0;
        } catch (Exception e) {
            log.error("更新退款状态异常，退款单ID: {}, 异常信息: {}", refundId, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Page<HcecOrders> pageList(OrdersQuery query) {
        // 查询订单分页数据
        List<Integer> orderStatus = new ArrayList<>();
        if (query.getOrderStatus() != null && !query.getOrderStatus().isEmpty()) {
            orderStatus = Arrays.stream(query.getOrderStatus().split(","))
                .map(String::trim)
                .map(Integer::parseInt)
                .collect(Collectors.toList());
        }

        Page<HcecOrders> ordersPage = baseMapper.selectPage(new Page<>(query.getPageNo(), query.getPageSize()), new LambdaQueryWrapper<HcecOrders>()
            .eq(query.getCustomerId() != null, HcecOrders::getCustomerId, query.getCustomerId())
            .like(query.getCustomerName() != null && !query.getCustomerName().isEmpty(), HcecOrders::getCustomerName, query.getCustomerName())
            .in(!orderStatus.isEmpty(), HcecOrders::getOrderStatus, orderStatus)
            .orderByDesc(HcecOrders::getCreateTime)
        );
        if (ordersPage.getRecords().isEmpty()) {
            return ordersPage;
        }
        ordersPage.getRecords().forEach(order -> {
            List<HcecOrdersProduct> ordersProducts = ordersProductMapper.selectList(new LambdaQueryWrapper<HcecOrdersProduct>()
                .eq(order.getOrderId() != null, HcecOrdersProduct::getOrderId, order.getOrderId())
            );
            order.setOrdersProduct(ordersProducts);
        });
        return ordersPage;
    }

    @Override
    public HcecOrders getOrderDetail(Long id) {
        log.info("开始获取订单详情，订单ID: {}", id);

        try {
            if (id == null) {
                log.error("获取订单详情失败，订单ID为空");
                throw new RuntimeException("id不能为空");
            }

            // 查询订单基本信息
            HcecOrders orders = baseMapper.selectById(id);
            if (orders == null) {
                log.warn("未找到订单信息，订单ID: {}", id);
                return null;
            }

            // 查询订单商品信息
            List<HcecOrdersProduct> ordersProducts = ordersProductMapper.selectList(new LambdaQueryWrapper<HcecOrdersProduct>()
                .eq(HcecOrdersProduct::getOrderId, orders.getOrderId())
            );
            log.debug("查询到订单商品数量: {}", ordersProducts.size());

            // 设置订单商品信息
            orders.setOrdersProduct(ordersProducts);

            log.info("获取订单详情成功，订单ID: {}, 客户名称: {}", id, orders.getCustomerName());
            return orders;
        } catch (Exception e) {
            log.error("获取订单详情异常，订单ID: {}, 异常信息: {}", id, e.getMessage(), e);
            throw e;
        }
    }


    /**
     * 预下单：创建订单
     *
     * @param createOrdersDTO 创建订单的请求参数
     * @return 订单号
     */
    @Override
    @Transactional
    public String preOrder(CreateOrdersDTO createOrdersDTO) {
        log.info("开始创建预订单");

        try {
            if (createOrdersDTO == null) {
                log.error("创建预订单失败，参数为空");
                return null;
            }

            List<HcecUserCart> userCarts = createOrdersDTO.getUserCarts();
            log.debug("购物车商品数量: {}", userCarts.size());

            // 创建订单
            HcecOrders orders = new HcecOrders();

            // 计算商品总数量
            int totalQuantity = createOrdersDTO.getUserCarts().stream()
                    .mapToInt(HcecUserCart::getBuyNum)
                    .sum();
            orders.setProductQuantity(totalQuantity);

            // 设置客户信息
            orders.setCustomerId(createOrdersDTO.getCustomerId());
            orders.setCommunityContact(createOrdersDTO.getCommunityContact());

            // 计算订单总价
            BigDecimal totalPrice = createOrdersDTO.getUserCarts().stream()
                    .map(cart -> cart.getPrice().multiply(BigDecimal.valueOf(cart.getBuyNum())))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            orders.setTotalPrice(totalPrice);

            // 设置收货信息
            orders.setAddress(createOrdersDTO.getAddress());
            orders.setCustomerName(createOrdersDTO.getCustomerName());
            orders.setCustomerContact(createOrdersDTO.getCustomerContact());

            // 设置订单状态为待付款
            orders.setOrderStatus(OrderStatusEnum.PENDING_PAYMENT.getCode());
            // 设置支付状态为未支付
            orders.setPaymentStatus(PaymentStatusEnum.UNPAID.getCode());
            // 设置支付方式为微信支付
            orders.setPay(PayEnum.WECHAT.getCode());

            log.debug("保存订单基本信息");
            baseMapper.insert(orders);
            log.info("订单基本信息保存成功，订单ID: {}", orders.getOrderId());

            // 处理订单商品信息
            log.debug("开始处理订单商品信息");
            createOrdersDTO.getUserCarts().forEach(cart -> {
                HcecOrdersProduct ordersProduct = new HcecOrdersProduct();
                ordersProduct.setOrderId(orders.getOrderId());
                ordersProduct.setProductId(cart.getProductId());
                ordersProduct.setProductName(cart.getProductName());
                ordersProduct.setProductSkuId(cart.getProductSkuId());
                ordersProduct.setBuyNum(cart.getBuyNum());
                ordersProduct.setImageUrl(cart.getImage());

                // 查询商品信息
                HcecProduct product = productMapper.selectById(cart.getProductId());
                if (product == null){
                    log.error("无效的商品ID: {}", cart.getProductId());
                    throw new IllegalArgumentException("无效的商品ID: " + cart.getProductId());
                }

                // 查询商品SKU信息
                HcecProductSku productSku = productSkuMapper.selectById(cart.getProductSkuId());
                if (productSku != null) {
                    int currentStock = productSku.getStockQuantity();
                    int buyNum = cart.getBuyNum();

                    // 检查库存是否足够
                    if (currentStock < buyNum) {
                        log.error("库存不足，无法购买商品: {}, 当前库存: {}, 购买数量: {}",
                            cart.getProductName(), currentStock, buyNum);
                        throw new IllegalArgumentException("库存不足，无法购买该商品: " + cart.getProductName());
                    }

                    log.debug("商品 {} 库存充足，当前库存: {}, 购买数量: {}",
                        cart.getProductName(), currentStock, buyNum);

                    // 减少库存并加锁库存
                    productSku.setStockQuantity(currentStock - buyNum);

                    // 处理 quantityLock，如果为 null 则初始化为 0
                    Integer quantityLock = productSku.getQuantityLock();
                    if (quantityLock == null) {
                        quantityLock = 0;
                    }

                    // 加锁库存
                    productSku.setQuantityLock(quantityLock + buyNum);

                    // 更新 SKU 信息
                    productSkuMapper.updateById(productSku);
                    log.debug("更新商品 {} 库存信息，剩余库存: {}, 锁定库存: {}",
                        cart.getProductName(), productSku.getStockQuantity(), productSku.getQuantityLock());

                    // 设置订单产品的其他信息
                    ordersProduct.setDescription(product.getDescription());
                    ordersProduct.setTotalPrice(productSku.getPrice().multiply(BigDecimal.valueOf(buyNum)));
                    ordersProduct.setUnitPrice(productSku.getPrice());

                    // 插入订单产品信息
                    ordersProductMapper.insert(ordersProduct);
                    log.debug("保存订单商品信息成功，商品名称: {}, 数量: {}", cart.getProductName(), buyNum);
                } else {
                    log.error("无效的商品SKU: {}", cart.getProductSkuId());
                    throw new IllegalArgumentException("无效的商品SKU: " + cart.getProductSkuId());
                }
            });

            // 清空购物车
            log.debug("清空购物车");
            userCartMapper.deleteByIds(userCarts.stream()
                .map(HcecUserCart::getId)
                .collect(Collectors.toList())
            );

            // 发送延迟消息，30分钟后自动关闭订单
            log.debug("发送订单自动关闭延迟消息");
            sendOrderCloseDelayedMessage(orders.getOrderId().toString());

            log.info("创建预订单成功，订单ID: {}, 总金额: {}", orders.getOrderId(), totalPrice);
            return orders.getOrderId().toString();
        } catch (Exception e) {
            log.error("创建预订单异常，异常信息: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<OrdersVo> getPageList(int offset, int limit, Long customerId) {
        Page<HcecOrders> ordersPage = baseMapper.selectPage(new Page<>(offset, limit), new LambdaQueryWrapper<HcecOrders>()
            .eq(HcecOrders::getCustomerId, customerId)
            .orderByDesc(HcecOrders::getCreateTime)
        );
        ArrayList<OrdersVo> ordersList = new ArrayList<>();
        ordersPage.getRecords().forEach(order -> {
            //订单商品列表
            List<HcecOrdersProduct> ordersProducts = ordersProductMapper.selectList(new LambdaQueryWrapper<HcecOrdersProduct>()
                .eq(order.getOrderId() != null, HcecOrdersProduct::getOrderId, order.getOrderId())
            );
            OrdersVo ordersVo = new OrdersVo();
            ArrayList<OrdersVo.ProductVo> productVos = new ArrayList<>();
            ordersVo.setOrderId(order.getOrderId());
            ordersVo.setProductQuantity(order.getProductQuantity());
            ordersVo.setTotalPrice(order.getTotalPrice());
            ordersVo.setCustomerName(order.getCustomerName());
            ordersVo.setProducts(productVos);
            ordersVo.setOrderStatus(order.getOrderStatus());
            ordersVo.setAddress(order.getAddress());
            ordersVo.setPhone(AppLoginHelper.getUserPhoneNumber());
            ordersProducts.forEach(ordersProduct -> {
                OrdersVo.ProductVo productVo = new OrdersVo.ProductVo();
                productVo.setBuyNum(ordersProduct.getBuyNum());
                productVo.setPrice(ordersProduct.getTotalPrice());
                if (ordersProduct.getImageUrl() != null){
                    productVo.setImageUrl(ordersProduct.getImageUrl());
                } else {
                    productVo.setImageUrl(productImageMapper.selectOne(new LambdaQueryWrapper<HcecProductImage>()
                        .eq(ordersProduct.getProductId() != null, HcecProductImage::getProductId, ordersProduct.getProductId())
                        .eq(HcecProductImage::getIsPrimary, 1)).getUrl());
                }
                productVo.setName(ordersProduct.getProductName());
                productVos.add(productVo);
            });
            ordersList.add(ordersVo);
        });
        return ordersList;
    }

    @Override
    @Transactional
    public boolean deleteById(Long id) {
        baseMapper.deleteById(id);
        ordersProductMapper.delete(new LambdaQueryWrapper<HcecOrdersProduct>()
            .eq(HcecOrdersProduct::getOrderId, id)
        );
        return true;
    }

    /**
     * 获取订单统计信息
     *
     * @return 订单统计信息
     */
    @Override
    public Map<String, Object> orderStatistics() {
        log.info("开始获取订单统计信息");

        try {
            // 获取全部已支付的有效订单
            List<HcecOrders> orders = baseMapper.selectList(new LambdaQueryWrapper<HcecOrders>()
                // 查询已支付的订单
                .eq(HcecOrders::getPaymentStatus, PaymentStatusEnum.PAID.getCode())
                // 查询有效的订单状态
                .in(HcecOrders::getOrderStatus,
                    OrderStatusEnum.PENDING_SHIPMENT.getCode(),
                    OrderStatusEnum.SHIPPED.getCode(),
                    OrderStatusEnum.COMPLETED.getCode())
            );
            log.debug("查询到有效订单数量: {}", orders.size());

            // 计算总销售额
            BigDecimal totalPrice = orders.stream()
                .map(HcecOrders::getTotalPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            log.debug("计算总销售额: {}", totalPrice);

            // 订单数量
            int orderCount = orders.size();

            // 计算order中的全部商品数量
            int productCount = orders.stream().mapToInt(HcecOrders::getProductQuantity).sum();
            log.debug("计算商品总数量: {}", productCount);

            // 计算付款人数
            int payerCount = orders.size();
            log.debug("计算付款人数: {}", payerCount);

            // 计算平均销售额，用上面的总销售额除以订单数量，并保留两位小数
            BigDecimal averagePrice = BigDecimal.ZERO;
            if (orderCount > 0) {
                averagePrice = totalPrice.divide(BigDecimal.valueOf(orderCount), 2, RoundingMode.HALF_UP);
            }
            log.debug("计算平均销售额: {}", averagePrice);

            // 组装返回结果
            Map<String, Object> map = new HashMap<>();
            map.put("totalPrice", totalPrice);
            map.put("orderCount", orderCount);
            map.put("productCount", productCount);
            map.put("payerCount", payerCount);
            map.put("averagePrice", averagePrice);

            log.info("获取订单统计信息成功，订单数量: {}, 总销售额: {}", orderCount, totalPrice);
            return map;
        } catch (Exception e) {
            log.error("获取订单统计信息异常，异常信息: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public List<Map<String, Long>> getOrderCount(String type, String year) {
        return getStatistics(type, year, () -> 0L, (count, order) -> count + 1);
    }

    @Override
    public List<Map<String, BigDecimal>> getTotalPrice(String type, String year) {
        return getStatistics(type, year, () -> BigDecimal.ZERO, (total, order) -> total.add(order.getTotalPrice()));
    }

    @Override
    public List<Map<String, Long>> getPayerCount(String type, String year) {
        return getStatistics(type, year,
            HashSet::new,
            (set, order) -> { set.add(order.getCustomerId()); return set; })
            .stream()
            .map(entry -> {
                String key = entry.keySet().iterator().next();
                long count = entry.values().iterator().next().size();
                return Collections.singletonMap(key, count);
            })
            .collect(Collectors.toList());
    }

    /**
     * 更新订单状态
     *
     * @param orderId 订单ID
     * @return 是否更新成功
     */
    @Override
    public boolean updateOrderStatus(long orderId) {
        log.info("开始更新订单状态，订单ID: {}", orderId);

        try {
            // 查询订单
            HcecOrders order = baseMapper.selectById(orderId);

            // 检查订单是否存在
            if (order == null) {
                log.error("更新订单状态失败，订单不存在，订单ID: {}", orderId);
                throw new IllegalArgumentException("无效的订单ID: " + orderId);
            }

            // 获取当前订单状态和支付状态
            int currentOrderStatus = order.getOrderStatus();
            int currentPaymentStatus = order.getPaymentStatus();
            log.debug("当前订单状态: {}, 支付状态: {}",
                OrderStatusEnum.fromCode(currentOrderStatus).getDescription(),
                PaymentStatusEnum.fromCode(currentPaymentStatus).getDescription());

            // 如果当前订单状态为待付款且支付状态为未支付，则更新为已取消
            if (currentOrderStatus == OrderStatusEnum.PENDING_PAYMENT.getCode()
                && currentPaymentStatus == PaymentStatusEnum.UNPAID.getCode()) {

                log.debug("订单符合取消条件，准备更新为已取消状态");
                // 设置为已取消状态
                order.setOrderStatus(OrderStatusEnum.CANCELLED.getCode());

                // 如果支付方式未设置，设置为微信支付
                if (order.getPay() == null) {
                    order.setPay(PayEnum.WECHAT.getCode());
                    log.debug("设置订单支付方式为微信支付");
                }

                // 更新订单信息到数据库
                int rowsAffected = baseMapper.updateById(order);
                boolean result = rowsAffected > 0;

                if (result) {
                    log.info("订单状态更新成功，订单ID: {}, 新状态: 已取消", orderId);
                } else {
                    log.error("订单状态更新失败，数据库更新返回影响行数为0，订单ID: {}", orderId);
                }

                // 返回更新是否成功
                return result;
            } else {
                // 如果当前状态不是待付款或已经支付，返回true
                log.info("订单当前状态不需要更新为已取消，订单ID: {}", orderId);
                return true;
            }
        } catch (Exception e) {
            log.error("更新订单状态异常，订单ID: {}, 异常信息: {}", orderId, e.getMessage(), e);
            throw e;
        }
    }



    /**
     * 发送订单关闭延迟消息
     *
     * @param orderId 订单ID
     */
    private void sendOrderCloseDelayedMessage(String orderId) {
        // 构建延迟消息，使用LEVEL_16对应30分钟
        MessageDTO<String> messageDTO = MessageDTO.delay(
            RocketMqTopicConstants.ORDER_TOPIC,
            RocketMqTopicConstants.ORDER_CLOSE_TAG,
            orderId,
            RocketMqConstants.DelayLevel.LEVEL_16
        );

        try {
            // 同步发送延迟消息
            RocketMqProducerUtil.syncSend(messageDTO);
            log.info("订单关闭延迟消息发送成功，订单ID: {}, 30分钟后将自动关闭", orderId);
        } catch (Exception e) {
            log.error("订单关闭延迟消息发送失败，订单ID: {}, 异常信息: {}", orderId, e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean successNotifyPay(String outTradeNo, String transactionId) {
        log.info("开始处理支付成功通知，订单号: {}, 交易号: {}", outTradeNo, transactionId);

        // 定义幂等键
        String idempotentKey = "pay_notify:" + outTradeNo + ":" + transactionId;

        return IdempotentUtils.executeWithIdempotent(
            idempotentKey,
            24, TimeUnit.HOURS,
            new IdempotentUtils.IdempotentSupplier<>() {
                @Override
                public Boolean get() {
                    // 定义分布式锁的键
                    String lockKey = "order_pay_lock:" + outTradeNo;
                    // 尝试获取锁
                    boolean locked = false;
                    RedissonClient redissonClient = RedisUtils.getClient();
                    RLock lock = redissonClient.getLock(lockKey);
                    try {
                        // 尝试获取锁，等待5秒，持有锁30秒
                        locked = lock.tryLock(5, 30, TimeUnit.SECONDS);
                        if (!locked) {
                            log.warn("获取订单支付锁失败，可能有其他线程正在处理，订单号: {}", outTradeNo);
                            return false;
                        }

                        log.debug("成功获取订单支付锁，开始处理支付通知，订单号: {}", outTradeNo);

                        // 查询订单
                        HcecOrders order = baseMapper.selectOne(new LambdaQueryWrapper<HcecOrders>()
                            .eq(HcecOrders::getOrderId, outTradeNo)
                        );
                        if (order == null) {
                            log.error("支付通知处理失败：订单不存在，订单号: {}", outTradeNo);
                            return false;
                        }

                        // 如果订单已经是待发货状态且支付状态为已支付，直接返回成功
                        if (order.getOrderStatus() == OrderStatusEnum.PENDING_SHIPMENT.getCode()
                            && order.getPaymentStatus() == PaymentStatusEnum.PAID.getCode()) {
                            log.info("订单已处理过，跳过重复处理，订单号: {}", outTradeNo);
                            return true;
                        }

                        log.debug("更新订单状态为待发货，订单号: {}", outTradeNo);
                        // 更新订单状态为待发货
                        order.setOrderStatus(OrderStatusEnum.PENDING_SHIPMENT.getCode());
                        // 更新支付状态为已支付
                        order.setPaymentStatus(PaymentStatusEnum.PAID.getCode());
                        // 更新支付相关信息
                        order.setTransactionId(transactionId);
                        // 设置支付方式为微信支付
                        order.setPay(PayEnum.WECHAT.getCode());
                        order.setPayDate(new Date());
                        baseMapper.updateById(order);

                        // 查询订单中的商品
                        List<HcecOrdersProduct> ordersProducts = ordersProductMapper.selectList(new LambdaQueryWrapper<HcecOrdersProduct>()
                            .eq(HcecOrdersProduct::getOrderId, outTradeNo)
                        );

                        log.debug("处理订单商品库存，订单号: {}, 商品数量: {}", outTradeNo, ordersProducts.size());

                        if (ordersProducts != null && !ordersProducts.isEmpty()) {
                            for (HcecOrdersProduct ordersProduct : ordersProducts) {
                                // 根据 SKU ID 查询商品 SKU
                                HcecProductSku productSku = productSkuMapper.selectById(ordersProduct.getProductSkuId());
                                if (productSku != null) {
                                    // 计算新的库存锁定量
                                    int newQuantityLock = productSku.getQuantityLock() - ordersProduct.getBuyNum();
                                    if (newQuantityLock < 0) {
                                        // 如果库存锁定量不足，抛出异常以回滚事务
                                        log.error("库存锁定量不足，无法完成订单，订单号: {}, 商品ID: {}, 锁定量: {}, 购买数量: {}",
                                            outTradeNo, ordersProduct.getProductId(), productSku.getQuantityLock(), ordersProduct.getBuyNum());
                                        throw new RuntimeException("库存锁定量不足，无法完成订单，订单号: " + outTradeNo);
                                    }

                                    // 更新库存锁定量
                                    productSku.setQuantityLock(newQuantityLock);
                                    log.debug("更新商品库存锁定量，商品ID: {}, 新锁定量: {}", ordersProduct.getProductId(), newQuantityLock);

                                    productSkuMapper.updateById(productSku);
                                } else {
                                    // 如果 SKU 不存在，抛出异常以回滚事务
                                    log.error("商品SKU不存在，无法完成订单，订单号: {}, SKU ID: {}", outTradeNo, ordersProduct.getProductSkuId());
                                    throw new RuntimeException("商品SKU不存在，无法完成订单，订单号: " + outTradeNo);
                                }
                            }
                        }

                        log.info("订单支付成功处理完成，订单号: {}", outTradeNo);
                        return true;
                    } catch (Exception e) {
                        log.error("订单支付成功处理异常，订单号: {}, 异常信息: {}", outTradeNo, e.getMessage(), e);
                        // 重新抛出异常，确保事务回滚
                        throw new RuntimeException(e);
                    } finally {
                        // 释放锁
                        if (locked && lock.isHeldByCurrentThread()) {
                            try {
                                lock.unlock();
                                log.debug("释放订单支付锁，订单号: {}", outTradeNo);
                            } catch (Exception e) {
                                log.error("释放订单支付锁异常，订单号: {}, 异常信息: {}", outTradeNo, e.getMessage(), e);
                            }
                        }
                    }
                }

                @Override
                public Boolean getOnIdempotent() {
                    // 幂等处理，返回成功
                    log.info("支付通知已处理，忽略重复通知，订单号: {}, 交易号: {}", outTradeNo, transactionId);
                    return true;
                }
            }
        );
    }

    private <T> List<Map<String, T>> getStatistics(String type, String yearStr,
                                                   Supplier<T> initializer,
                                                   BiFunction<T, HcecOrders, T> accumulator) {
        LocalDateTime now = LocalDateTime.now();

        LambdaQueryWrapper<HcecOrders> queryWrapper = new LambdaQueryWrapper<>();
        // 查询已支付的订单
        queryWrapper.eq(HcecOrders::getPaymentStatus, PaymentStatusEnum.PAID.getCode());
        // 查询有效的订单状态
        queryWrapper.in(HcecOrders::getOrderStatus,
            OrderStatusEnum.PENDING_SHIPMENT.getCode(),
            OrderStatusEnum.SHIPPED.getCode(),
            OrderStatusEnum.COMPLETED.getCode());

        Map<Integer,T> resultMap;
        String suffix;

        if ("month".equalsIgnoreCase(type)) {
            LocalDateTime startOfYear;
            LocalDateTime endOfYear;

            // 判断是否提供了年份，如果没有则使用当前年。
            int currentYear;
            if (yearStr != null && !yearStr.isEmpty()) {
                currentYear = Integer.parseInt(yearStr);
                // 一年的最后一天
            } else {
                // 如果没有提供年份，则默认取当前年
                currentYear = LocalDateTime.now().getYear();
                // 一年的最后一天
            }
            startOfYear = LocalDateTime.of(currentYear, 1, 1, 0, 0);
            endOfYear = LocalDateTime.of(currentYear, 12, 31, 23, 59);
            queryWrapper.between(HcecOrders::getCreateTime, startOfYear, endOfYear);

            resultMap = initializeMap(1 ,12 ,initializer);
            suffix = "月";

        } else if ("year".equalsIgnoreCase(type)) {
            // 同样处理年份逻辑
            int currentYear;
            if (yearStr != null && !yearStr.isEmpty()) {
                currentYear = Integer.parseInt(yearStr);
                LocalDateTime startOfSelectedYear = LocalDateTime.of(currentYear , 1 , 1 ,0 ,0);
                LocalDateTime endOfSelectedYear = LocalDateTime.of(currentYear ,12 ,31 ,23 ,59);
                queryWrapper.between(HcecOrders::getCreateTime, startOfSelectedYear, endOfSelectedYear);
            } else{
                currentYear=now.getYear();
                LocalDateTime startOfFiveYearsAgo = LocalDateTime.of(currentYear - 5 ,1 ,1 ,0 ,0);
                LocalDateTime endOfCurrentYear = LocalDateTime.of(currentYear ,12 ,31 ,23 ,59);
                queryWrapper.between(HcecOrders::getCreateTime, startOfFiveYearsAgo, endOfCurrentYear);
            }
            resultMap=initializeMap(currentYear-4 ,5 ,initializer);
            suffix="年";

        }else{
            return Collections.emptyList();
        }

        List<HcecOrders> ordersList = baseMapper.selectList(queryWrapper);

        for (HcecOrders order: ordersList){
            // 将Date转换为LocalDateTime以获取月份和年份
            LocalDateTime dateTime = order.getCreateTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            int key = "month".equalsIgnoreCase(type) ? dateTime.getMonthValue() : dateTime.getYear();
            resultMap.put(key,(accumulator.apply(resultMap.get(key),order)));
        }

        return resultMap.entrySet()
            .stream()
            .map(entry->Collections.singletonMap(entry.getKey()+suffix ,
                entry.getValue()))
            .collect(Collectors.toList());
    }

    private <T> Map<Integer,T> initializeMap(int start,int count,Supplier<T> initializer){
        Map<Integer,T> map=new LinkedHashMap<>();
        for(int i=start;i<start+count;i++){
            map.put(i, initializer.get());
        }
        return map;
    }
}
