package org.dromara.cyly.hcec.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单退款状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum OrderRefundEnum {

    /**
     * 退款审核中
     */
    REFUND_AUDITING(0, "退款审核中"),

    /**
     * 已退款
     */
    REFUNDED(1, "已退款"),

    /**
     * 退款失败
     */
    REFUND_FAILED(2, "退款失败");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态描述
     */
    private final String description;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 枚举
     */
    public static OrderRefundEnum fromCode(Integer code) {
        for (OrderRefundEnum status : OrderRefundEnum.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}
