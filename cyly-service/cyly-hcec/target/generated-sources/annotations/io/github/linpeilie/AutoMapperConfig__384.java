package io.github.linpeilie;

import org.dromara.cyly.hcec.domain.HcecCategoryToHcecCategoryVoMapper;
import org.dromara.cyly.hcec.domain.HcecChatMessageToHcecChatMessageVoMapper;
import org.dromara.cyly.hcec.domain.HcecChatRoomToHcecChatRoomVoMapper;
import org.dromara.cyly.hcec.domain.HcecNewsToHcecNewsVoMapper;
import org.dromara.cyly.hcec.domain.HcecOrderRefundsToHcecOrderRefundsVoMapper;
import org.dromara.cyly.hcec.domain.HcecOrdersProductToHcecOrdersProductVoMapper;
import org.dromara.cyly.hcec.domain.HcecOrdersToHcecOrdersVoMapper;
import org.dromara.cyly.hcec.domain.HcecProductDetailToHcecProductDetailVoMapper;
import org.dromara.cyly.hcec.domain.HcecProductImageToHcecProductImageVoMapper;
import org.dromara.cyly.hcec.domain.HcecProductSkuToHcecProductSkuVoMapper;
import org.dromara.cyly.hcec.domain.HcecProductToHcecProductVoMapper;
import org.dromara.cyly.hcec.domain.HcecUserCartToHcecUserCartVoMapper;
import org.dromara.cyly.hcec.domain.HcecWechatBillToHcecWechatBillVoMapper;
import org.dromara.cyly.hcec.domain.HcecWechatPayConfigToHcecWechatPayConfigVoMapper;
import org.dromara.cyly.hcec.domain.bo.HcecCategoryBoToHcecCategoryMapper;
import org.dromara.cyly.hcec.domain.bo.HcecChatMessageBoToHcecChatMessageMapper;
import org.dromara.cyly.hcec.domain.bo.HcecChatRoomBoToHcecChatRoomMapper;
import org.dromara.cyly.hcec.domain.bo.HcecNewsBoToHcecNewsMapper;
import org.dromara.cyly.hcec.domain.bo.HcecOrderRefundsBoToHcecOrderRefundsMapper;
import org.dromara.cyly.hcec.domain.bo.HcecOrdersBoToHcecOrdersMapper;
import org.dromara.cyly.hcec.domain.bo.HcecOrdersProductBoToHcecOrdersProductMapper;
import org.dromara.cyly.hcec.domain.bo.HcecProductBoToHcecProductMapper;
import org.dromara.cyly.hcec.domain.bo.HcecProductDetailBoToHcecProductDetailMapper;
import org.dromara.cyly.hcec.domain.bo.HcecProductImageBoToHcecProductImageMapper;
import org.dromara.cyly.hcec.domain.bo.HcecProductSkuBoToHcecProductSkuMapper;
import org.dromara.cyly.hcec.domain.bo.HcecUserCartBoToHcecUserCartMapper;
import org.dromara.cyly.hcec.domain.bo.HcecWechatPayConfigBoToHcecWechatPayConfigMapper;
import org.dromara.cyly.hcec.domain.vo.HcecCategoryVoToHcecCategoryMapper;
import org.dromara.cyly.hcec.domain.vo.HcecChatMessageVoToHcecChatMessageMapper;
import org.dromara.cyly.hcec.domain.vo.HcecChatRoomVoToHcecChatRoomMapper;
import org.dromara.cyly.hcec.domain.vo.HcecNewsVoToHcecNewsMapper;
import org.dromara.cyly.hcec.domain.vo.HcecOrderRefundsVoToHcecOrderRefundsMapper;
import org.dromara.cyly.hcec.domain.vo.HcecOrdersProductVoToHcecOrdersProductMapper;
import org.dromara.cyly.hcec.domain.vo.HcecOrdersVoToHcecOrdersMapper;
import org.dromara.cyly.hcec.domain.vo.HcecProductDetailVoToHcecProductDetailMapper;
import org.dromara.cyly.hcec.domain.vo.HcecProductImageVoToHcecProductImageMapper;
import org.dromara.cyly.hcec.domain.vo.HcecProductSkuVoToHcecProductSkuMapper;
import org.dromara.cyly.hcec.domain.vo.HcecProductVoToHcecProductMapper;
import org.dromara.cyly.hcec.domain.vo.HcecUserCartVoToHcecUserCartMapper;
import org.dromara.cyly.hcec.domain.vo.HcecWechatBillVoToHcecWechatBillMapper;
import org.dromara.cyly.hcec.domain.vo.HcecWechatPayConfigVoToHcecWechatPayConfigMapper;
import org.mapstruct.Builder;
import org.mapstruct.MapperConfig;
import org.mapstruct.ReportingPolicy;

@MapperConfig(
    componentModel = "spring-lazy",
    uses = {ConverterMapperAdapter__384.class, HcecProductImageToHcecProductImageVoMapper.class, HcecChatMessageVoToHcecChatMessageMapper.class, HcecWechatBillToHcecWechatBillVoMapper.class, HcecOrdersProductVoToHcecOrdersProductMapper.class, HcecChatRoomBoToHcecChatRoomMapper.class, HcecOrderRefundsToHcecOrderRefundsVoMapper.class, HcecProductBoToHcecProductMapper.class, HcecProductSkuBoToHcecProductSkuMapper.class, HcecChatRoomToHcecChatRoomVoMapper.class, HcecOrdersToHcecOrdersVoMapper.class, HcecProductVoToHcecProductMapper.class, HcecChatMessageToHcecChatMessageVoMapper.class, HcecProductImageBoToHcecProductImageMapper.class, HcecCategoryToHcecCategoryVoMapper.class, HcecOrdersVoToHcecOrdersMapper.class, HcecChatMessageBoToHcecChatMessageMapper.class, HcecOrdersBoToHcecOrdersMapper.class, HcecNewsVoToHcecNewsMapper.class, HcecOrdersProductBoToHcecOrdersProductMapper.class, HcecProductImageVoToHcecProductImageMapper.class, HcecOrderRefundsVoToHcecOrderRefundsMapper.class, HcecProductToHcecProductVoMapper.class, HcecWechatPayConfigToHcecWechatPayConfigVoMapper.class, HcecWechatBillVoToHcecWechatBillMapper.class, HcecWechatPayConfigVoToHcecWechatPayConfigMapper.class, HcecNewsToHcecNewsVoMapper.class, HcecUserCartToHcecUserCartVoMapper.class, HcecCategoryBoToHcecCategoryMapper.class, HcecProductDetailVoToHcecProductDetailMapper.class, HcecOrdersProductToHcecOrdersProductVoMapper.class, HcecProductSkuVoToHcecProductSkuMapper.class, HcecOrderRefundsBoToHcecOrderRefundsMapper.class, HcecUserCartBoToHcecUserCartMapper.class, HcecProductDetailToHcecProductDetailVoMapper.class, HcecNewsBoToHcecNewsMapper.class, HcecProductDetailBoToHcecProductDetailMapper.class, HcecProductSkuToHcecProductSkuVoMapper.class, HcecCategoryVoToHcecCategoryMapper.class, HcecUserCartVoToHcecUserCartMapper.class, HcecChatRoomVoToHcecChatRoomMapper.class, HcecWechatPayConfigBoToHcecWechatPayConfigMapper.class},
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    builder = @Builder(buildMethod = "build", disableBuilder = true)
)
public interface AutoMapperConfig__384 {
}
