package org.dromara.cyly.hcec.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.cyly.hcec.domain.HcecProductDetail;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T09:19:04+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class HcecProductDetailVoToHcecProductDetailMapper__2Impl implements HcecProductDetailVoToHcecProductDetailMapper__2 {

    @Override
    public HcecProductDetail convert(HcecProductDetailVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        HcecProductDetail hcecProductDetail = new HcecProductDetail();

        hcecProductDetail.setContext( arg0.getContext() );
        hcecProductDetail.setId( arg0.getId() );
        hcecProductDetail.setImageUrls( arg0.getImageUrls() );
        hcecProductDetail.setProductId( arg0.getProductId() );

        return hcecProductDetail;
    }

    @Override
    public HcecProductDetail convert(HcecProductDetailVo arg0, HcecProductDetail arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setContext( arg0.getContext() );
        arg1.setId( arg0.getId() );
        arg1.setImageUrls( arg0.getImageUrls() );
        arg1.setProductId( arg0.getProductId() );

        return arg1;
    }
}
