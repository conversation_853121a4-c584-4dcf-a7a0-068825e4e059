package org.dromara.cyly.hcec.domain.bo;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.cyly.hcec.domain.HcecProductSku;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T09:19:04+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class HcecProductSkuBoToHcecProductSkuMapper__2Impl implements HcecProductSkuBoToHcecProductSkuMapper__2 {

    @Override
    public HcecProductSku convert(HcecProductSkuBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        HcecProductSku hcecProductSku = new HcecProductSku();

        hcecProductSku.setCreateBy( arg0.getCreateBy() );
        hcecProductSku.setCreateDept( arg0.getCreateDept() );
        hcecProductSku.setCreateTime( arg0.getCreateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            hcecProductSku.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        hcecProductSku.setSearchValue( arg0.getSearchValue() );
        hcecProductSku.setUpdateBy( arg0.getUpdateBy() );
        hcecProductSku.setUpdateTime( arg0.getUpdateTime() );
        hcecProductSku.setColor( arg0.getColor() );
        hcecProductSku.setColorSpec( arg0.getColorSpec() );
        hcecProductSku.setCreated( arg0.getCreated() );
        hcecProductSku.setId( arg0.getId() );
        hcecProductSku.setModel( arg0.getModel() );
        if ( arg0.getPrice() != null ) {
            hcecProductSku.setPrice( BigDecimal.valueOf( arg0.getPrice() ) );
        }
        hcecProductSku.setProductId( arg0.getProductId() );
        if ( arg0.getQuantityLock() != null ) {
            hcecProductSku.setQuantityLock( arg0.getQuantityLock().intValue() );
        }
        if ( arg0.getStockQuantity() != null ) {
            hcecProductSku.setStockQuantity( arg0.getStockQuantity().intValue() );
        }
        hcecProductSku.setUpdated( arg0.getUpdated() );

        return hcecProductSku;
    }

    @Override
    public HcecProductSku convert(HcecProductSkuBo arg0, HcecProductSku arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateTime( arg0.getCreateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setColor( arg0.getColor() );
        arg1.setColorSpec( arg0.getColorSpec() );
        arg1.setCreated( arg0.getCreated() );
        arg1.setId( arg0.getId() );
        arg1.setModel( arg0.getModel() );
        if ( arg0.getPrice() != null ) {
            arg1.setPrice( BigDecimal.valueOf( arg0.getPrice() ) );
        }
        else {
            arg1.setPrice( null );
        }
        arg1.setProductId( arg0.getProductId() );
        if ( arg0.getQuantityLock() != null ) {
            arg1.setQuantityLock( arg0.getQuantityLock().intValue() );
        }
        else {
            arg1.setQuantityLock( null );
        }
        if ( arg0.getStockQuantity() != null ) {
            arg1.setStockQuantity( arg0.getStockQuantity().intValue() );
        }
        else {
            arg1.setStockQuantity( null );
        }
        arg1.setUpdated( arg0.getUpdated() );

        return arg1;
    }
}
