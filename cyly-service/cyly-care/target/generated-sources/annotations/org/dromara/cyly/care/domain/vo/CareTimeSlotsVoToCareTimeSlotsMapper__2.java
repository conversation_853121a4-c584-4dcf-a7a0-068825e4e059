package org.dromara.cyly.care.domain.vo;

import io.github.linpeilie.AutoMapperConfig__357;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.care.domain.CareTimeSlots;
import org.dromara.cyly.care.domain.CareTimeSlotsToCareTimeSlotsVoMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__357.class,
    uses = {CareTimeSlotsToCareTimeSlotsVoMapper__2.class},
    imports = {}
)
public interface CareTimeSlotsVoToCareTimeSlotsMapper__2 extends BaseMapper<CareTimeSlotsVo, CareTimeSlots> {
}
