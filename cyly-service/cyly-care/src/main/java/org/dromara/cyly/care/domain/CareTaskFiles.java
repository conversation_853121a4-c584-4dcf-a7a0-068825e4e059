package org.dromara.cyly.care.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 护理任务图片对象 care_task_files
 *
 * <AUTHOR>
 * @date 2025-02-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("care_task_files")
public class CareTaskFiles extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 任务id
     */
    private Long taskId;

    /**
     * 1图片2视频3录音
     */
    private Long fileType;

    /**
     * 任务图片
     */
    private String imageUrl;


}
