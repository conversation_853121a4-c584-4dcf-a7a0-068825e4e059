package org.dromara.cyly.care.service.impl;

import org.apache.dubbo.config.annotation.DubboReference;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.resource.api.RemoteFileService;
import org.dromara.resource.api.domain.RemoteFile;
import org.springframework.stereotype.Service;
import org.dromara.cyly.care.domain.bo.CareTaskFilesBo;
import org.dromara.cyly.care.domain.vo.CareTaskFilesVo;
import org.dromara.cyly.care.domain.CareTaskFiles;
import org.dromara.cyly.care.mapper.CareTaskFilesMapper;
import org.dromara.cyly.care.service.ICareTaskFilesService;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Collection;
import java.util.stream.Collectors;

/**
 * 护理任务图片Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-02-11
 */
@RequiredArgsConstructor
@Service
public class CareTaskFilesServiceImpl implements ICareTaskFilesService {

    private final CareTaskFilesMapper baseMapper;

    @DubboReference(mock = "true", timeout = 5000)
    private final RemoteFileService remoteFileService;

    /**
     * 查询护理任务图片
     *
     * @param id 主键
     * @return 护理任务图片
     */
    @Override
    public CareTaskFilesVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询护理任务图片列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 护理任务图片分页列表
     */
    @Override
    public TableDataInfo<CareTaskFilesVo> queryPageList(CareTaskFilesBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<CareTaskFiles> lqw = buildQueryWrapper(bo);
        Page<CareTaskFilesVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的护理任务图片列表
     *
     * @param bo 查询条件
     * @return 护理任务图片列表
     */
    @Override
    public List<CareTaskFilesVo> queryList(CareTaskFilesBo bo) {
        LambdaQueryWrapper<CareTaskFiles> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<CareTaskFiles> buildQueryWrapper(CareTaskFilesBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<CareTaskFiles> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getTaskId() != null, CareTaskFiles::getTaskId, bo.getTaskId());
        lqw.eq(bo.getFileType() != null, CareTaskFiles::getFileType, bo.getFileType());
        lqw.eq(StringUtils.isNotBlank(bo.getImageUrl()), CareTaskFiles::getImageUrl, bo.getImageUrl());
        return lqw;
    }

    /**
     * 新增护理任务图片
     *
     * @param bo 护理任务图片
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(CareTaskFilesBo bo) {
        CareTaskFiles add = MapstructUtils.convert(bo, CareTaskFiles.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改护理任务图片
     *
     * @param bo 护理任务图片
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(CareTaskFilesBo bo) {
        CareTaskFiles update = MapstructUtils.convert(bo, CareTaskFiles.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(CareTaskFiles entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除护理任务图片信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public boolean insert(Long taskId, MultipartFile file, Integer fileType) {
        String originalFilename = file.getOriginalFilename();
        String contentType = file.getContentType();
        byte[] fileBytes = null;
        try {
            fileBytes = file.getBytes();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        RemoteFile remoteFile = remoteFileService.upload(file.getName(), originalFilename, contentType, fileBytes);
        if (remoteFile != null && remoteFile.getUrl() != null) {
            CareTaskFiles careTaskFiles = new CareTaskFiles();
            careTaskFiles.setTaskId(taskId);
            careTaskFiles.setFileType(Long.valueOf(fileType));
            careTaskFiles.setImageUrl(remoteFile.getUrl());
            return baseMapper.insert(careTaskFiles) > 0;
        }if (remoteFile != null) {
            CareTaskFilesBo bo = new CareTaskFilesBo();
            bo.setTaskId(taskId);
            bo.setFileType(Long.valueOf(fileType));
            bo.setImageUrl(remoteFile.getUrl());
            return insertByBo(bo);
        }
        return false;
    }

    @Override
    public String picList(Long taskId) {
        List<CareTaskFilesVo> careTaskFilesVos = baseMapper.selectVoList(new LambdaQueryWrapper<CareTaskFiles>()
            .eq(CareTaskFiles::getTaskId, taskId)
        );
        if (careTaskFilesVos.isEmpty()) {
            return null;
        }
        // 提取所有非空的imageUrl字段并用逗号拼接
        return careTaskFilesVos.stream()
            // fileType为1
            .filter(vo -> vo.getFileType().equals(1L))
            // 获取每个对象的imageUrl
            .map(CareTaskFilesVo::getImageUrl)
            // 过滤空值和空白字符串
            .filter(url -> url != null && !url.isBlank())
            // 用逗号拼接
            .collect(Collectors.joining(","));
    }
}
