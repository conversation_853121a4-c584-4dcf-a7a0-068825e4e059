package org.dromara.cyly.cms.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mail.constant.EmailConstants;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.common.redis.utils.RedisUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.dromara.cyly.cms.domain.bo.CmsEmailSenderBo;
import org.dromara.cyly.cms.domain.vo.CmsEmailSenderVo;
import org.dromara.cyly.cms.domain.CmsEmailSender;
import org.dromara.cyly.cms.mapper.CmsEmailSenderMapper;
import org.dromara.cyly.cms.service.ICmsEmailSenderService;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 邮件发送者Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-08
 */
@RequiredArgsConstructor
@Service
public class CmsEmailSenderServiceImpl implements ICmsEmailSenderService {

    private final CmsEmailSenderMapper baseMapper;

    /**
     * 项目启动时，初始化邮件发送者信息参数到缓存，加载配置类
     */
    @Async("undertowVirtualThreadExecutor") // 1. 指定虚拟线程执行器
    @Override
    public void init(){
        //定义发送者邮箱的集合
        List<CmsEmailSender> qyEmailSenderList = baseMapper.selectList();
        //筛选出账户状态为启用的列表
        List<CmsEmailSender> qyEmailSenders = qyEmailSenderList.stream().filter(i-> i.getStatus().equals(EmailConstants.STATUS_TRUE)).collect(Collectors.toCollection(ArrayList::new));
        //用map进行封装
        Map<String,List<CmsEmailSender>> map = new HashMap<>();
        map.put("sendList",qyEmailSenders);
        //将邮件发送者列表存入缓存
        RedisUtils.setCacheMap(EmailConstants.EMAIL_SENDER,map);
        //RedisUtils.setCacheList(EmailConstants.EMAIL_SENDER,qyEmailSenders);
        //加载邮件初始化配置
        SpringUtils.context().publishEvent(map);
    }

    /**
     * 查询邮件发送者
     *
     * @param senderId 主键
     * @return 邮件发送者
     */
    @Override
    public CmsEmailSenderVo queryById(Long senderId){
        return baseMapper.selectVoById(senderId);
    }

    /**
     * 分页查询邮件发送者列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 邮件发送者分页列表
     */
    @Override
    public TableDataInfo<CmsEmailSenderVo> queryPageList(CmsEmailSenderBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<CmsEmailSender> lqw = buildQueryWrapper(bo);
        Page<CmsEmailSenderVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的邮件发送者列表
     *
     * @param bo 查询条件
     * @return 邮件发送者列表
     */
    @Override
    public List<CmsEmailSenderVo> queryList(CmsEmailSenderBo bo) {
        LambdaQueryWrapper<CmsEmailSender> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<CmsEmailSender> buildQueryWrapper(CmsEmailSenderBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<CmsEmailSender> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getSenderEmailHost()), CmsEmailSender::getSenderEmailHost, bo.getSenderEmailHost());
        lqw.eq(bo.getSenderEmailPost() != null, CmsEmailSender::getSenderEmailPost, bo.getSenderEmailPost());
        lqw.eq(StringUtils.isNotBlank(bo.getSenderEmailFrom()), CmsEmailSender::getSenderEmailFrom, bo.getSenderEmailFrom());
        lqw.eq(StringUtils.isNotBlank(bo.getSenderEmailUser()), CmsEmailSender::getSenderEmailUser, bo.getSenderEmailUser());
        lqw.eq(StringUtils.isNotBlank(bo.getSenderEmailAuth()), CmsEmailSender::getSenderEmailAuth, bo.getSenderEmailAuth());
        lqw.eq(StringUtils.isNotBlank(bo.getSenderEmailPassword()), CmsEmailSender::getSenderEmailPassword, bo.getSenderEmailPassword());
        lqw.eq(StringUtils.isNotBlank(bo.getStarttlsEnable()), CmsEmailSender::getStarttlsEnable, bo.getStarttlsEnable());
        lqw.eq(StringUtils.isNotBlank(bo.getSslEnable()), CmsEmailSender::getSslEnable, bo.getSslEnable());
        lqw.eq(bo.getSenderSort() != null, CmsEmailSender::getSenderSort, bo.getSenderSort());
        lqw.eq(bo.getStatus() != null, CmsEmailSender::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增邮件发送者
     *
     * @param bo 邮件发送者
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(CmsEmailSenderBo bo) {
        CmsEmailSender add = MapstructUtils.convert(bo, CmsEmailSender.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setSenderId(add.getSenderId());
        }
        return flag;
    }

    /**
     * 修改邮件发送者
     *
     * @param bo 邮件发送者
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(CmsEmailSenderBo bo) {
        CmsEmailSender update = MapstructUtils.convert(bo, CmsEmailSender.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(CmsEmailSender entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除邮件发送者信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
