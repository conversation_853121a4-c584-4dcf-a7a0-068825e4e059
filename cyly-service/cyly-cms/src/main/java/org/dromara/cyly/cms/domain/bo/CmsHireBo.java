package org.dromara.cyly.cms.domain.bo;

import org.dromara.cyly.cms.domain.CmsHire;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 招聘信息业务对象 cms_hire
 *
 * <AUTHOR>
 * @date 2025-03-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = CmsHire.class, reverseConvertGenerate = false)
public class CmsHireBo extends BaseEntity {

    /**
     * 招聘id
     */
    @NotNull(message = "招聘id不能为空", groups = { EditGroup.class })
    private Long hireId;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 性别
     */
    @NotBlank(message = "性别不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sex;

    /**
     * 年龄
     */
    @NotNull(message = "年龄不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long age;

    /**
     * 学历
     */
    @NotBlank(message = "学历不能为空", groups = { AddGroup.class, EditGroup.class })
    private String education;

    /**
     * 应聘职位
     */
    @NotBlank(message = "应聘职位不能为空", groups = { AddGroup.class, EditGroup.class })
    private String job;

    /**
     * 行政区域代码
     */
    @NotNull(message = "行政区域代码不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long areaId;

    /**
     * 简历附件id
     */
    @NotNull(message = "简历附件id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long ossId;


}
