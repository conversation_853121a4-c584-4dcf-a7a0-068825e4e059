package org.dromara.cyly.cms.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.cyly.cms.domain.CmsArticle;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import java.util.Date;

/**
 * 文章业务对象 cms_article
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = CmsArticle.class, reverseConvertGenerate = false)
public class CmsArticleBo extends BaseEntity {

    /**
     * 文章id
     */
    @NotNull(message = "文章id不能为空", groups = { EditGroup.class })
    private Long articleId;

    /**
     * 栏目id
     */
    @NotNull(message = "栏目id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long columnId;

    /**
     * 文章标题
     */
   // @NotBlank(message = "文章标题不能为空", groups = { AddGroup.class, EditGroup.class })
    private String articleTitle;

    /**
     * 作者
     */
   // @NotBlank(message = "作者不能为空", groups = { AddGroup.class, EditGroup.class })
    private String articleAuthor;

    /**
     * 轮播图
     */
    private String articleCarouselImage;

    /**
     * 排序
     */
    private String articleOrder;

    /**
     * 文章类型(1文章，2页面)
     */
    //@NotBlank(message = "文章类型(1文章，2页面)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String articleType;

    /**
     * 文章摘要
     */
    //@NotBlank(message = "文章摘要不能为空", groups = { AddGroup.class, EditGroup.class })
        private String articleExcerpt;

    /**
     * 文章内容
     */
    //@NotBlank(message = "文章内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String articleContent;

    /**
     * 文章状态，发布(1)、草稿(0)
     */
    @NotBlank(message = "文章状态，发布(1)、草稿(0)不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    /**
     * 文章被查看的次数统计
     */
    //@NotNull(message = "文章被查看的次数统计不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long viewsCount;

    /**
     * 是否为特色文章标志位，可用于首页推荐(默认0,true为1)
     */
   // @NotNull(message = "是否为特色文章标志位，可用于首页推荐(默认0,true为1)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long isFeatured;

    /**
     * 文章封面
     */
    //@NotBlank(message = "文章封面不能为空", groups = { AddGroup.class, EditGroup.class })
    private String featuredImage;

    /**
     * 文章正式发布日期
     */
    //@NotNull(message = "文章正式发布日期不能为空", groups = { AddGroup.class, EditGroup.class })
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Date publishedTime;
}
