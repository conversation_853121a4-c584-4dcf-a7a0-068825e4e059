package org.dromara.cyly.cms.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.cyly.cms.domain.CmsUserConsultations;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 用户咨询信息视图对象 cms_user_consultations
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = CmsUserConsultations.class)
public class CmsUserConsultationsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名")
    private String name;

    /**
     * 邮箱
     */
    @ExcelProperty(value = "邮箱")
    private String email;

    /**
     * 手机号
     */
    @ExcelProperty(value = "手机号")
    private String phone;

    /**
     * 消息
     */
    @ExcelProperty(value = "消息")
    private String message;

    /**
     * 提交时间
     */
    @ExcelProperty(value = "提交时间")
    private Date createdAt;


}
