package org.dromara.cyly.cms.domain.bo;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import org.dromara.cyly.cms.domain.CmsColumn;

/**
 * 栏目业务对象 cms-column
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = CmsColumn.class, reverseConvertGenerate = false)
public class CmsColumnBo extends BaseEntity {

    /**
     * 栏目id
     */
    @NotNull(message = "栏目id不能为空", groups = { EditGroup.class })
    private Long columnId;

    /**
     * 栏目名称
     */
    @NotBlank(message = "栏目名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String columnName;

    /**
     * 栏目图片
     */
    private String columnImage;

    /**
     * 栏目内容
     */
    private String columnContent;

    /**
     * 父id(顶级栏目则为0，默认0)
     */
    @NotNull(message = "父id(顶级栏目则为0，默认0)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long parentId;

    /**
     * 排序字段
     */
    @NotNull(message = "排序字段不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long sortOrder;

    /**
     * 栏目状态-启用(1)或禁用(0)
     */
    @NotNull(message = "栏目状态-启用(1)或禁用(0)不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long status;

    /**
     * 路由地址
     */
    private String path;

    /**
     * 组件路径
     */
    private String component;

    /**
     * 路由参数
     */
    private String queryParam;

    /**
     * 是否为外链（0是 1否）
     */
    private String isFrame;

    /**
     * 是否缓存（0缓存 1不缓存）
     */
    private String isCache;

    /**
     * 类型（M目录 C菜单 F按钮）
     */
    private String menuType;

    /**
     * 显示状态（0显示 1隐藏）
     */
    private String visible;

    /**
     * 权限字符串
     */
    private String perms;

    /**
     * 菜单图标
     */
    private String icon;
}
