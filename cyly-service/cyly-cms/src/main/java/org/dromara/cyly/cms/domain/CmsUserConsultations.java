package org.dromara.cyly.cms.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 用户咨询信息对象 cms_user_consultations
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cms_user_consultations")
public class CmsUserConsultations extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 姓名
     */
    private String name;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 消息
     */
    private String message;

    /**
     * 提交时间
     */
    private Date createdAt;


}
