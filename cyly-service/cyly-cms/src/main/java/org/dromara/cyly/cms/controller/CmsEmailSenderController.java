package org.dromara.cyly.cms.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.cyly.cms.domain.vo.CmsEmailSenderVo;
import org.dromara.cyly.cms.domain.bo.CmsEmailSenderBo;
import org.dromara.cyly.cms.service.ICmsEmailSenderService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 邮件发送者
 * 前端访问路由地址为:/cms/emailSender
 *
 * <AUTHOR>
 * @date 2025-03-08
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/emailSender")
public class CmsEmailSenderController extends BaseController {

    private final ICmsEmailSenderService cmsEmailSenderService;

    /**
     * 查询邮件发送者列表
     */
    @SaCheckPermission("cmd:emailSender:list")
    @GetMapping("/list")
    public TableDataInfo<CmsEmailSenderVo> list(CmsEmailSenderBo bo, PageQuery pageQuery) {
        return cmsEmailSenderService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出邮件发送者列表
     */
    @SaCheckPermission("cmd:emailSender:export")
    @Log(title = "邮件发送者", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(CmsEmailSenderBo bo, HttpServletResponse response) {
        List<CmsEmailSenderVo> list = cmsEmailSenderService.queryList(bo);
        ExcelUtil.exportExcel(list, "邮件发送者", CmsEmailSenderVo.class, response);
    }

    /**
     * 获取邮件发送者详细信息
     *
     * @param senderId 主键
     */
    @SaCheckPermission("cmd:emailSender:query")
    @GetMapping("/{senderId}")
    public R<CmsEmailSenderVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long senderId) {
        return R.ok(cmsEmailSenderService.queryById(senderId));
    }

    /**
     * 新增邮件发送者
     */
    @SaCheckPermission("cmd:emailSender:add")
    @Log(title = "邮件发送者", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/add")
    public R<Void> add(@Validated(AddGroup.class) @RequestBody CmsEmailSenderBo bo) {
        return toAjax(cmsEmailSenderService.insertByBo(bo));
    }

    /**
     * 修改邮件发送者
     */
    @SaCheckPermission("cmd:emailSender:edit")
    @Log(title = "邮件发送者", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/edit")
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody CmsEmailSenderBo bo) {
        return toAjax(cmsEmailSenderService.updateByBo(bo));
    }

    /**
     * 删除邮件发送者
     *
     * @param senderIds 主键串
     */
    @SaCheckPermission("cmd:emailSender:remove")
    @Log(title = "邮件发送者", businessType = BusinessType.DELETE)
    @PostMapping("/delete/{senderIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] senderIds) {
        return toAjax(cmsEmailSenderService.deleteWithValidByIds(List.of(senderIds), true));
    }
}
