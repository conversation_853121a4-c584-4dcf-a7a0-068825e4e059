package org.dromara.cyly.cms.domain.vo;

import org.dromara.common.translation.annotation.Translation;
import java.util.Date;
import org.dromara.common.translation.constant.TransConstant;
import org.dromara.cyly.cms.domain.CmsArticle;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;



/**
 * 文章视图对象 cms_article
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = CmsArticle.class)
public class CmsArticleVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 文章id
     */
    @ExcelProperty(value = "文章id")
    private Long articleId;

    /**
     * 栏目id
     */
    @ExcelProperty(value = "栏目id")
    private Long columnId;

    /**
     * 文章标题
     */
    @ExcelProperty(value = "文章标题")
    private String articleTitle;

    /**
     * 作者
     */
    @ExcelProperty(value = "作者")
    private String articleAuthor;

    /**
     *
     * 轮播图
     */
    private String articleCarouselImage;

    /**
     * 文章排序值(默认为0)
     */
    private String articleOrder;



    /**
     * 文章类型(1文章，2页面)
     */
    @ExcelProperty(value = "文章类型(1文章，2页面)")
    private String articleType;

    /**
     * 文章摘要
     */
    @ExcelProperty(value = "文章摘要")
    private String articleExcerpt;

    /**
     * 文章内容
     */
    @ExcelProperty(value = "文章内容")
    private String articleContent;



    /**
     * 文章状态，发布(1)、草稿(0)
     */
    @ExcelProperty(value = "文章状态，发布(1)、草稿(0)")
    private String status;

    /**
     * 文章被查看的次数统计
     */
    @ExcelProperty(value = "文章被查看的次数统计")
    private Long viewsCount;

    /**
     * 是否为特色文章标志位，可用于首页推荐(默认0,true为1)
     */
    @ExcelProperty(value = "是否为特色文章标志位，可用于首页推荐(默认0,true为1)")
    private Long isFeatured;

    /**
     * 文章封面
     */
    @ExcelProperty(value = "文章封面")
    @Translation(type = TransConstant.OSS_ID_TO_URL)
    private String featuredImage;

    /**
     * 文章正式发布日期
     */
    @ExcelProperty(value = "文章正式发布日期")
    private Date publishedTime;


}
