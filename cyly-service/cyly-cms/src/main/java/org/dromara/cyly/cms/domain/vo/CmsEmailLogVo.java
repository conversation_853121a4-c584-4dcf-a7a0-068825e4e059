package org.dromara.cyly.cms.domain.vo;

import org.dromara.cyly.cms.domain.CmsEmailLog;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import org.dromara.common.excel.annotation.ExcelDictFormat;
import org.dromara.common.excel.convert.ExcelDictConvert;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 邮件发送日志视图对象 cms_email_log
 *
 * <AUTHOR>
 * @date 2025-03-08
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = CmsEmailLog.class)
public class CmsEmailLogVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 邮件发送日志id
     */
    @ExcelProperty(value = "邮件发送日志id")
    private Long id;

    /**
     * 发送方
     */
    @ExcelProperty(value = "发送方")
    private String sendFrom;

    /**
     * 接收方
     */
    @ExcelProperty(value = "接收方")
    private String sendUser;

    /**
     * 邮件发送对象id
     */
    @ExcelProperty(value = "邮件发送对象id")
    private Long targetId;

    /**
     * 邮件标题
     */
    @ExcelProperty(value = "邮件标题")
    private String emailTitle;

    /**
     * 邮件内容
     */
    @ExcelProperty(value = "邮件内容")
    private String emailContent;

    /**
     * 邮件发送耗时
     */
    @ExcelProperty(value = "邮件发送耗时")
    private String timeConsuming;

    /**
     * 发送状态(0成功 1失败)
     */
    @ExcelProperty(value = "发送状态(0成功 1失败)")
    private String sendStatus;

    /**
     * 返回原因
     */
    @ExcelProperty(value = "返回原因")
    private String backReason;

    /**
     * 创建部门
     */
    @ExcelProperty(value = "创建部门")
    private Long creteDept;


}
