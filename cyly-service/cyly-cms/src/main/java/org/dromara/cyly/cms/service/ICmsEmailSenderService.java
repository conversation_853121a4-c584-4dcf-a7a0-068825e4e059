package org.dromara.cyly.cms.service;

import org.dromara.cyly.cms.domain.CmsEmailSender;
import org.dromara.cyly.cms.domain.vo.CmsEmailSenderVo;
import org.dromara.cyly.cms.domain.bo.CmsEmailSenderBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 邮件发送者Service接口
 *
 * <AUTHOR>
 * @date 2025-03-08
 */
public interface ICmsEmailSenderService {

    void init();

    /**
     * 查询邮件发送者
     *
     * @param senderId 主键
     * @return 邮件发送者
     */
    CmsEmailSenderVo queryById(Long senderId);

    /**
     * 分页查询邮件发送者列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 邮件发送者分页列表
     */
    TableDataInfo<CmsEmailSenderVo> queryPageList(CmsEmailSenderBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的邮件发送者列表
     *
     * @param bo 查询条件
     * @return 邮件发送者列表
     */
    List<CmsEmailSenderVo> queryList(CmsEmailSenderBo bo);

    /**
     * 新增邮件发送者
     *
     * @param bo 邮件发送者
     * @return 是否新增成功
     */
    Boolean insertByBo(CmsEmailSenderBo bo);

    /**
     * 修改邮件发送者
     *
     * @param bo 邮件发送者
     * @return 是否修改成功
     */
    Boolean updateByBo(CmsEmailSenderBo bo);

    /**
     * 校验并批量删除邮件发送者信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
