package org.dromara.cyly.cms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import org.dromara.common.core.constant.UserConstants;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StreamUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.core.utils.TreeBuildUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.cyly.cms.domain.vo.CmsMetaVo;
import org.dromara.cyly.cms.domain.vo.CmsRouterVo;
import org.dromara.cyly.cms.mapper.CmsColumnMapper;
import org.dromara.cyly.cms.service.ICmsColumnService;
import org.springframework.stereotype.Service;
import org.dromara.cyly.cms.domain.bo.CmsColumnBo;
import org.dromara.cyly.cms.domain.vo.CmsColumnVo;
import org.dromara.cyly.cms.domain.CmsColumn;

import java.util.*;

/**
 * 栏目Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
@RequiredArgsConstructor
@Service
public class CmsColumnServiceImpl implements ICmsColumnService {

    private final CmsColumnMapper baseMapper;

    /**
     * 查询栏目
     *
     * @param columnId 主键
     * @return 栏目
     */
    @Override
    public CmsColumnVo queryById(Long columnId) {
        return baseMapper.selectVoById(columnId);
    }

    /**
     * 分页查询栏目列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 栏目分页列表
     */
    @Override
    public TableDataInfo<CmsColumnVo> queryPageList(CmsColumnBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<CmsColumn> lqw = buildQueryWrapper(bo);
        Page<CmsColumnVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询栏目列表
     *
     * @param bo 栏目
     * @return 栏目
     */
    @Override
    public List<CmsColumnVo> selectCmsColumnVoList(CmsColumnBo bo) {
        LambdaQueryWrapper<CmsColumn> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }


    /**
     * 构建前端路由所需要的菜单
     * 路由name命名规则 path首字母转大写 + id
     *
     * @param menus 菜单列表
     * @return 路由列表
     */
    @Override
    public List<CmsRouterVo> buildMenus(List<CmsColumn> menus) {
        List<CmsRouterVo> routers = new LinkedList<>();
        for (CmsColumn menu : menus) {
            // 生成路由名称
            String name = menu.getColumnName() + menu.getColumnId();
            CmsRouterVo router = new CmsRouterVo();
            // 设置路由是否隐藏
            router.setHidden("1".equals(menu.getVisible()));
            router.setName(name);
            router.setPath(menu.getRouterPath());
            router.setComponent(menu.getComponentInfo());
            router.setQuery(menu.getQueryParam());
            // 设置路由的元数据
            router.setMeta(new CmsMetaVo(menu.getColumnName(), menu.getIcon(), StringUtils.equals("1", menu.getIsCache()), menu.getPath()));

            // 处理子菜单
            List<CmsColumn> cMenus = menu.getChildren();
            if (CollUtil.isNotEmpty(cMenus) && UserConstants.TYPE_DIR.equals(menu.getMenuType())) {
                // 如果当前菜单有子菜单且为目录类型，则设置路由为始终显示
                router.setAlwaysShow(true);
                router.setRedirect("noRedirect");
                router.setChildren(buildMenus(cMenus));
            } else if (menu.isMenuFrame()) {
                // 处理菜单为框架类型的情况
                String frameName = StringUtils.capitalize(menu.getPath()) + menu.getColumnId();
                router.setMeta(null);
                List<CmsRouterVo> childrenList = new ArrayList<>();
                CmsRouterVo children = new CmsRouterVo();
                children.setPath(menu.getPath());
                children.setComponent(menu.getComponent());
                children.setName(frameName);
                children.setMeta(new CmsMetaVo(menu.getColumnName(), menu.getIcon(), StringUtils.equals("1", menu.getIsCache()), menu.getPath()));
                children.setQuery(menu.getQueryParam());
                childrenList.add(children);
                router.setChildren(childrenList);
            } else if (menu.getParentId().intValue() == 0 && menu.isInnerLink()) {
                // 处理菜单为内链类型的情况
                router.setMeta(new CmsMetaVo(menu.getColumnName(), menu.getIcon()));
                router.setPath("/");
                List<CmsRouterVo> childrenList = new ArrayList<>();
                CmsRouterVo children = new CmsRouterVo();
                String routerPath = CmsColumn.innerLinkReplaceEach(menu.getPath());
                String innerLinkName = StringUtils.capitalize(routerPath) + menu.getColumnId();
                children.setPath(routerPath);
                children.setComponent(UserConstants.INNER_LINK);
                children.setName(innerLinkName);
                children.setMeta(new CmsMetaVo(menu.getColumnName(), menu.getIcon(), menu.getPath()));
                childrenList.add(children);
                router.setChildren(childrenList);
            }
            routers.add(router);
        }
        return routers;
    }

    /**
     * 将栏目信息转换为树形结构
     * 此方法用于将一组栏目信息（CmsColumnVo对象列表）转换成树形结构的List
     * 主要用于前端展示或数据结构转换等场景
     *
     * @param bo 栏目信息
     * @return 返回一个Tree<Long>类型的List，表示树形结构的栏目信息
     */
    @Override
    public List<Tree<Long>> treeList(CmsColumnBo bo) {
        LambdaQueryWrapper<CmsColumn> lqw = buildQueryWrapper(bo);
        List<CmsColumnVo> cmsColumns = baseMapper.selectVoList(lqw);
        // 检查输入列表是否为空，如果为空则直接返回一个新的空列表
        if (CollUtil.isEmpty(cmsColumns)) {
            return CollUtil.newArrayList();
        }
        // 使用TreeBuildUtils工具类构建树形结构，将栏目信息转换为树节点
        // 通过Lambda表达式设置树节点的属性，包括ID、父ID、名称和权重
        return TreeBuildUtils.build(cmsColumns, (cmsColumn, tree) -> tree.setId(cmsColumn.getColumnId()).setParentId(cmsColumn.getParentId()).setName(cmsColumn.getColumnName()).setWeight(cmsColumn.getSortOrder()));
    }

    /**
     * 查询栏目列表
     *
     * @return 栏目列表
     */
    @Override
    public List<CmsColumn> queryList() {
        LambdaQueryWrapper<CmsColumn> lqw = new LambdaQueryWrapper<>();
        lqw.in(CmsColumn::getMenuType, UserConstants.TYPE_DIR, UserConstants.TYPE_MENU);
        lqw.eq(CmsColumn::getStatus, UserConstants.MENU_NORMAL);
        lqw.orderByAsc(CmsColumn::getParentId);
        lqw.orderByAsc(CmsColumn::getSortOrder);
        List<CmsColumn> cmsColumns = baseMapper.selectList(lqw);
        return getChildPerms(cmsColumns, 0);
    }

    /**
     * 根据父节点的ID获取所有子节点
     *
     * @param list     分类表
     * @param parentId 传入的父节点ID
     * @return String
     */
    private List<CmsColumn> getChildPerms(List<CmsColumn> list, int parentId) {
        List<CmsColumn> returnList = new ArrayList<>();
        for (CmsColumn t : list) {
            // 一、根据传入的某个父节点ID,遍历该父节点的所有子节点
            if (t.getParentId() == parentId) {
                recursionFn(list, t);
                returnList.add(t);
            }
        }
        return returnList;
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<CmsColumn> list, CmsColumn t) {
        // 得到子节点列表
        List<CmsColumn> childList = StreamUtils.filter(list, n -> n.getParentId().equals(t.getColumnId()));
        t.setChildren(childList);
        for (CmsColumn tChild : childList) {
            // 判断是否有子节点
            if (list.stream().anyMatch(n -> n.getParentId().equals(tChild.getColumnId()))) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 查询符合条件的栏目列表
     *
     * @param bo 查询条件
     * @return 栏目列表
     */
    @Override
    public List<CmsColumnVo> queryList(CmsColumnBo bo) {
        LambdaQueryWrapper<CmsColumn> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<CmsColumn> buildQueryWrapper(CmsColumnBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<CmsColumn> lqw = Wrappers.lambdaQuery();
        // 栏目名称
        lqw.like(StringUtils.isNotBlank(bo.getColumnName()), CmsColumn::getColumnName, bo.getColumnName());
        // 栏目图片
        lqw.like(StringUtils.isNotBlank(bo.getColumnImage()), CmsColumn::getColumnImage, bo.getColumnImage());
        // 栏目内容
        lqw.like(StringUtils.isNotBlank(bo.getColumnContent()), CmsColumn::getColumnContent, bo.getColumnContent());
        //父id
        lqw.eq(bo.getParentId() != null, CmsColumn::getParentId, bo.getParentId());
        // 排序字段
        lqw.eq(bo.getSortOrder() != null, CmsColumn::getSortOrder, bo.getSortOrder());
        // 栏目状态
        lqw.eq(bo.getStatus() != null, CmsColumn::getStatus, bo.getStatus());
        // 路由地址
        lqw.like(StringUtils.isNotBlank(bo.getPath()), CmsColumn::getPath, bo.getPath());
        // 组件路径
        lqw.like(StringUtils.isNotBlank(bo.getComponent()), CmsColumn::getComponent, bo.getComponent());
        // 路由参数
        lqw.like(StringUtils.isNotBlank(bo.getQueryParam()), CmsColumn::getQueryParam, bo.getQueryParam());
        // 是否为外链
        lqw.eq(StringUtils.isNotBlank(bo.getIsFrame()), CmsColumn::getIsFrame, bo.getIsFrame());
        // 是否缓存
        lqw.eq(StringUtils.isNotBlank(bo.getIsCache()), CmsColumn::getIsCache, bo.getIsCache());
        // 类型
        lqw.eq(StringUtils.isNotBlank(bo.getMenuType()), CmsColumn::getMenuType, bo.getMenuType());
        // 显示状态
        lqw.eq(StringUtils.isNotBlank(bo.getVisible()), CmsColumn::getVisible, bo.getVisible());
        // 权限字符串
        lqw.like(StringUtils.isNotBlank(bo.getPerms()), CmsColumn::getPerms, bo.getPerms());
        // 菜单图标
        lqw.like(StringUtils.isNotBlank(bo.getIcon()), CmsColumn::getIcon, bo.getIcon());
        return lqw;
    }

    /**
     * 新增栏目
     *
     * @param bo 栏目
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(CmsColumnBo bo) {
        CmsColumn add = MapstructUtils.convert(bo, CmsColumn.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setColumnId(add.getColumnId());
        }
        return flag;
    }

    /**
     * 修改栏目
     *
     * @param bo 栏目
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(CmsColumnBo bo) {
        CmsColumn update = MapstructUtils.convert(bo, CmsColumn.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(CmsColumn entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除栏目信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
