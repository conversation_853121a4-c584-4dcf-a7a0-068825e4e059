package org.dromara.cyly.cms.domain.bo;

import org.dromara.cyly.cms.domain.CmsEmailRecipient;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 邮件接收方业务对象 cms_email_recipient
 *
 * <AUTHOR>
 * @date 2025-03-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = CmsEmailRecipient.class, reverseConvertGenerate = false)
public class CmsEmailRecipientBo extends BaseEntity {

    /**
     * 接收方id
     */
    @NotNull(message = "接收方id不能为空", groups = { EditGroup.class })
    private Long recipientId;

    /**
     * 接收方邮箱
     */
    @NotBlank(message = "接收方邮箱不能为空", groups = { AddGroup.class, EditGroup.class })
    private String recipientEmail;

    /**
     * 账户状态（0：启用，1：禁用）
     */
    @NotNull(message = "账户状态（0：启用，1：禁用）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long status;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;


}
