package org.dromara.cyly.cms.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 轮播图对象 cms_carousel_image
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cms_carousel_image")
public class CmsCarouselImage extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "image_id")
    private Long imageId;

    /**
     * 栏目id
     */
            private Long columnId;

    /**
     * 图片地址
     */
    private String imageUrl;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;
    /**
     * 图片名称
     */
    private String imageName;

}
