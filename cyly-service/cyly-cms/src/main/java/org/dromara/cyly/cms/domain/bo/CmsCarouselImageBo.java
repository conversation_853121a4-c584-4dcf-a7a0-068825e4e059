package org.dromara.cyly.cms.domain.bo;


import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import org.dromara.cyly.cms.domain.CmsCarouselImage;

/**
 * 轮播图业务对象 cms_carousel_image
 *
 * <AUTHOR>
 * @date 2025-01-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = CmsCarouselImage.class, reverseConvertGenerate = false)
public class CmsCarouselImageBo extends BaseEntity {

    /**
     *
     */
    @NotNull(message = "不能为空", groups = { EditGroup.class })
    private Long imageId;

    /**
     * 栏目id
     */

    private Long columnId;

    /**
     * 图片地址
     */

    private String imageUrl;
    /**
     * 图片名称
     */
    private String imageName;

}
