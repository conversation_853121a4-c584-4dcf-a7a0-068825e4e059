package org.dromara.cyly.cms.domain.bo;

import org.dromara.cyly.cms.domain.CmsEmailSender;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

/**
 * 邮件发送者业务对象 cms_email_sender
 *
 * <AUTHOR>
 * @date 2025-03-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = CmsEmailSender.class, reverseConvertGenerate = false)
public class CmsEmailSenderBo extends BaseEntity {

    /**
     * 邮件发送方id
     */
    @NotNull(message = "邮件发送方id不能为空", groups = { EditGroup.class })
    private Long senderId;

    /**
     * 邮件服务地址
     */
    @NotBlank(message = "邮件服务地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String senderEmailHost;

    /**
     * 邮箱加密端口号
     */
    @NotNull(message = "邮箱加密端口号不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long senderEmailPost;

    /**
     * 发送方
     */
    @NotBlank(message = "发送方不能为空", groups = { AddGroup.class, EditGroup.class })
    private String senderEmailFrom;

    /**
     * 发送方用户名
     */
    @NotBlank(message = "发送方用户名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String senderEmailUser;

    /**
     * 是否需要用户名密码验证
     */
    @NotBlank(message = "是否需要用户名密码验证不能为空", groups = { AddGroup.class, EditGroup.class })
    private String senderEmailAuth;

    /**
     * 授权码
     */
    @NotBlank(message = "授权码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String senderEmailPassword;

    /**
     * 使用 STARTTLS安全连接，STARTTLS是对纯文本通信协议的扩展
     */
    @NotBlank(message = "使用 STARTTLS安全连接，STARTTLS是对纯文本通信协议的扩展不能为空", groups = { AddGroup.class, EditGroup.class })
    private String starttlsEnable;

    /**
     * 是否使用SSL安全连接
     */
    @NotBlank(message = "是否使用SSL安全连接不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sslEnable;

    /**
     * 排序值
     */
    @NotNull(message = "排序值不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long senderSort;

    /**
     * 账户状态（0：启用，1：禁用）
     */
    @NotNull(message = "账户状态（0：启用，1：禁用）不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long status;

    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;


}
