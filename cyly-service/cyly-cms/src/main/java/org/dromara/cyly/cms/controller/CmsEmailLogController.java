package org.dromara.cyly.cms.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.cyly.cms.domain.vo.CmsEmailLogVo;
import org.dromara.cyly.cms.domain.bo.CmsEmailLogBo;
import org.dromara.cyly.cms.service.ICmsEmailLogService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 邮件发送日志
 * 前端访问路由地址为:/cms/emailLog
 *
 * <AUTHOR>
 * @date 2025-03-08
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/emailLog")
public class CmsEmailLogController extends BaseController {

    private final ICmsEmailLogService cmsEmailLogService;

    /**
     * 查询邮件发送日志列表
     */
    @SaCheckPermission("cms:emailLog:list")
    @GetMapping("/list")
    public TableDataInfo<CmsEmailLogVo> list(CmsEmailLogBo bo, PageQuery pageQuery) {
        return cmsEmailLogService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出邮件发送日志列表
     */
    @SaCheckPermission("cms:emailLog:export")
    @Log(title = "邮件发送日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(CmsEmailLogBo bo, HttpServletResponse response) {
        List<CmsEmailLogVo> list = cmsEmailLogService.queryList(bo);
        ExcelUtil.exportExcel(list, "邮件发送日志", CmsEmailLogVo.class, response);
    }

    /**
     * 获取邮件发送日志详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("cms:emailLog:query")
    @GetMapping("/{id}")
    public R<CmsEmailLogVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(cmsEmailLogService.queryById(id));
    }

    /**
     * 新增邮件发送日志
     */
    @SaCheckPermission("cms:emailLog:add")
    @Log(title = "邮件发送日志", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/add")
    public R<Void> add(@Validated(AddGroup.class) @RequestBody CmsEmailLogBo bo) {
        return toAjax(cmsEmailLogService.insertByBo(bo));
    }

    /**
     * 修改邮件发送日志
     */
    @SaCheckPermission("cms:emailLog:edit")
    @Log(title = "邮件发送日志", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/edit")
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody CmsEmailLogBo bo) {
        return toAjax(cmsEmailLogService.updateByBo(bo));
    }

    /**
     * 删除邮件发送日志
     *
     * @param ids 主键串
     */
    @SaCheckPermission("cms:emailLog:remove")
    @Log(title = "邮件发送日志", businessType = BusinessType.DELETE)
    @PostMapping("/delete/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(cmsEmailLogService.deleteWithValidByIds(List.of(ids), true));
    }
}
