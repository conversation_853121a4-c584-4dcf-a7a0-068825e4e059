package org.dromara.cyly.cms.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 邮件接收方对象 cms_email_recipient
 *
 * <AUTHOR>
 * @date 2025-03-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cms_email_recipient")
public class CmsEmailRecipient extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 接收方id
     */
    @TableId(value = "recipient_id")
    private Long recipientId;

    /**
     * 接收方邮箱
     */
    private String recipientEmail;

    /**
     * 账户状态（0：启用，1：禁用）
     */
    private Long status;

    /**
     * 备注
     */
    private String remark;


}
