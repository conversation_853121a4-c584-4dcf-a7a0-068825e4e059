package org.dromara.cyly.cms;

import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.scheduling.annotation.EnableAsync;

@EnableDubbo
@EnableAsync
@SpringBootApplication
public class CylyCmsApplication {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(CylyCmsApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  长养乐园养老服务官网模块启动成功   ლ(´ڡ`ლ)ﾞ  ");
    }
}
