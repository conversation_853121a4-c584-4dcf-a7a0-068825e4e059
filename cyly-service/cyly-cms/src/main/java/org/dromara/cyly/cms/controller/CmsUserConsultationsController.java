package org.dromara.cyly.cms.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.cyly.cms.domain.vo.CmsUserConsultationsVo;
import org.dromara.cyly.cms.domain.bo.CmsUserConsultationsBo;
import org.dromara.cyly.cms.service.ICmsUserConsultationsService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 用户咨询信息
 * 前端访问路由地址为:/cms/userConsultations
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/userConsultations")
public class CmsUserConsultationsController extends BaseController {

    private final ICmsUserConsultationsService cmsUserConsultationsService;

    /**
     * 查询用户咨询信息列表
     */
    @GetMapping("/list")
    public TableDataInfo<CmsUserConsultationsVo> list(CmsUserConsultationsBo bo, PageQuery pageQuery) {
        return cmsUserConsultationsService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出用户咨询信息列表
     */
    @Log(title = "用户咨询信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(CmsUserConsultationsBo bo, HttpServletResponse response) {
        List<CmsUserConsultationsVo> list = cmsUserConsultationsService.queryList(bo);
        ExcelUtil.exportExcel(list, "用户咨询信息", CmsUserConsultationsVo.class, response);
    }

    /**
     * 获取用户咨询信息详细信息
     *
     * @param id 主键
     */
    @GetMapping("/{id}")
    public R<CmsUserConsultationsVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(cmsUserConsultationsService.queryById(id));
    }

    /**
     * 新增用户咨询信息
     */
    @Log(title = "用户咨询信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody CmsUserConsultationsBo bo) {
        return toAjax(cmsUserConsultationsService.insertByBo(bo));
    }

    /**
     * 修改用户咨询信息
     */
    @Log(title = "用户咨询信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody CmsUserConsultationsBo bo) {
        return toAjax(cmsUserConsultationsService.updateByBo(bo));
    }

    /**
     * 删除用户咨询信息
     *
     * @param ids 主键串
     */
    @Log(title = "用户咨询信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(cmsUserConsultationsService.deleteWithValidByIds(List.of(ids), true));
    }
}
