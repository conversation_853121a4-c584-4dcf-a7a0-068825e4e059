package org.dromara.cyly.cms.controller;

import java.util.List;

import cn.hutool.core.lang.tree.Tree;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.web.core.BaseController;
import org.dromara.cyly.cms.domain.CmsColumn;
import org.dromara.cyly.cms.domain.vo.CmsRouterVo;
import org.dromara.cyly.cms.service.ICmsColumnService;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.cyly.cms.domain.vo.CmsColumnVo;
import org.dromara.cyly.cms.domain.bo.CmsColumnBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 栏目
 * 前端访问路由地址为:/cms/CmsColumn
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/CmsColumn")
public class CmsColumnController extends BaseController {

    private final ICmsColumnService cmsColumnService;

    /**
     * 查询栏目列表
     */
    @SaCheckPermission("cms:CmsColumn:list")
    @GetMapping("/list")
    public R<List<CmsColumnVo>> list(CmsColumnBo bo) {
        return R.ok(cmsColumnService.selectCmsColumnVoList(bo));
    }

    /**
     * 构建栏目树列表
     */
    @GetMapping("/treeList")
    public R<List<Tree<Long>>> treeList(CmsColumnBo bo) {
        return R.ok(cmsColumnService.treeList(bo));
    }

    /**
     * 获取cms路由信息
     *
     * @return cms路由信息
     */
    @GetMapping("/getCmsRouters")
    public R<List<CmsRouterVo>> getCmsRouters() {
        List<CmsColumn> menus = cmsColumnService.queryList();
        return R.ok(cmsColumnService.buildMenus(menus));
    }

    /**
     * 导出栏目列表
     */
    @SaCheckPermission("cms:CmsColumn:export")
    @Log(title = "栏目", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(CmsColumnBo bo, HttpServletResponse response) {
        List<CmsColumnVo> list = cmsColumnService.queryList(bo);
        ExcelUtil.exportExcel(list, "栏目", CmsColumnVo.class, response);
    }

    /**
     * 获取栏目详细信息
     *
     * @param columnId 主键
     */
    @SaCheckPermission("cms:CmsColumn:query")
    @GetMapping("/{columnId}")
    public R<CmsColumnVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long columnId) {
        return R.ok(cmsColumnService.queryById(columnId));
    }

    /**
     * 新增栏目
     */
    @SaCheckPermission("cms:CmsColumn:add")
    @Log(title = "栏目", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/add")
    public R<Void> add(@Validated(AddGroup.class) @RequestBody CmsColumnBo bo) {
        return toAjax(cmsColumnService.insertByBo(bo));
    }

    /**
     * 修改栏目
     */
    @SaCheckPermission("cms:CmsColumn:edit")
    @Log(title = "栏目", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/edit")
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody CmsColumnBo bo) {
        return toAjax(cmsColumnService.updateByBo(bo));
    }

    /**
     * 删除栏目
     *
     * @param columnIds 主键串
     */
    @SaCheckPermission("cms:CmsColumn:remove")
    @Log(title = "栏目", businessType = BusinessType.DELETE)
    @PostMapping("/delete/{columnIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] columnIds) {
        return toAjax(cmsColumnService.deleteWithValidByIds(List.of(columnIds), true));
    }
}
