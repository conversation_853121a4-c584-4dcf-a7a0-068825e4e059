package org.dromara.cyly.cms.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.cyly.cms.domain.bo.CmsEmailLogBo;
import org.dromara.cyly.cms.domain.vo.CmsEmailLogVo;
import org.dromara.cyly.cms.domain.CmsEmailLog;
import org.dromara.cyly.cms.mapper.CmsEmailLogMapper;
import org.dromara.cyly.cms.service.ICmsEmailLogService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 邮件发送日志Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-08
 */
@RequiredArgsConstructor
@Service
public class CmsEmailLogServiceImpl implements ICmsEmailLogService {

    private final CmsEmailLogMapper baseMapper;

    /**
     * 查询邮件发送日志
     *
     * @param id 主键
     * @return 邮件发送日志
     */
    @Override
    public CmsEmailLogVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询邮件发送日志列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 邮件发送日志分页列表
     */
    @Override
    public TableDataInfo<CmsEmailLogVo> queryPageList(CmsEmailLogBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<CmsEmailLog> lqw = buildQueryWrapper(bo);
        Page<CmsEmailLogVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的邮件发送日志列表
     *
     * @param bo 查询条件
     * @return 邮件发送日志列表
     */
    @Override
    public List<CmsEmailLogVo> queryList(CmsEmailLogBo bo) {
        LambdaQueryWrapper<CmsEmailLog> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<CmsEmailLog> buildQueryWrapper(CmsEmailLogBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<CmsEmailLog> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getSendFrom()), CmsEmailLog::getSendFrom, bo.getSendFrom());
        lqw.eq(StringUtils.isNotBlank(bo.getSendUser()), CmsEmailLog::getSendUser, bo.getSendUser());
        lqw.eq(bo.getTargetId() != null, CmsEmailLog::getTargetId, bo.getTargetId());
        lqw.eq(StringUtils.isNotBlank(bo.getEmailTitle()), CmsEmailLog::getEmailTitle, bo.getEmailTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getEmailContent()), CmsEmailLog::getEmailContent, bo.getEmailContent());
        lqw.eq(StringUtils.isNotBlank(bo.getTimeConsuming()), CmsEmailLog::getTimeConsuming, bo.getTimeConsuming());
        lqw.eq(StringUtils.isNotBlank(bo.getSendStatus()), CmsEmailLog::getSendStatus, bo.getSendStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getBackReason()), CmsEmailLog::getBackReason, bo.getBackReason());
        lqw.eq(bo.getCreteDept() != null, CmsEmailLog::getCreteDept, bo.getCreteDept());
        return lqw;
    }

    /**
     * 新增邮件发送日志
     *
     * @param bo 邮件发送日志
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(CmsEmailLogBo bo) {
        CmsEmailLog add = MapstructUtils.convert(bo, CmsEmailLog.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改邮件发送日志
     *
     * @param bo 邮件发送日志
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(CmsEmailLogBo bo) {
        CmsEmailLog update = MapstructUtils.convert(bo, CmsEmailLog.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(CmsEmailLog entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除邮件发送日志信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
