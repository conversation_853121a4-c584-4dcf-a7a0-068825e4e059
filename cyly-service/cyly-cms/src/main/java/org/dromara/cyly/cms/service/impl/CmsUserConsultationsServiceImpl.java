package org.dromara.cyly.cms.service.impl;

import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.cyly.cms.domain.bo.CmsUserConsultationsBo;
import org.dromara.cyly.cms.domain.vo.CmsUserConsultationsVo;
import org.dromara.cyly.cms.domain.CmsUserConsultations;
import org.dromara.cyly.cms.mapper.CmsUserConsultationsMapper;
import org.dromara.cyly.cms.service.ICmsUserConsultationsService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 用户咨询信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-22
 */
@RequiredArgsConstructor
@Service
public class CmsUserConsultationsServiceImpl implements ICmsUserConsultationsService {

    private final CmsUserConsultationsMapper baseMapper;

    /**
     * 查询用户咨询信息
     *
     * @param id 主键
     * @return 用户咨询信息
     */
    @Override
    public CmsUserConsultationsVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询用户咨询信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 用户咨询信息分页列表
     */
    @Override
    public TableDataInfo<CmsUserConsultationsVo> queryPageList(CmsUserConsultationsBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<CmsUserConsultations> lqw = buildQueryWrapper(bo);
        Page<CmsUserConsultationsVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的用户咨询信息列表
     *
     * @param bo 查询条件
     * @return 用户咨询信息列表
     */
    @Override
    public List<CmsUserConsultationsVo> queryList(CmsUserConsultationsBo bo) {
        LambdaQueryWrapper<CmsUserConsultations> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<CmsUserConsultations> buildQueryWrapper(CmsUserConsultationsBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<CmsUserConsultations> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()), CmsUserConsultations::getName, bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getEmail()), CmsUserConsultations::getEmail, bo.getEmail());
        lqw.eq(StringUtils.isNotBlank(bo.getPhone()), CmsUserConsultations::getPhone, bo.getPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getMessage()), CmsUserConsultations::getMessage, bo.getMessage());
        return lqw;
    }

    /**
     * 新增用户咨询信息
     *
     * @param bo 用户咨询信息
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(CmsUserConsultationsBo bo) {
        CmsUserConsultations add = MapstructUtils.convert(bo, CmsUserConsultations.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改用户咨询信息
     *
     * @param bo 用户咨询信息
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(CmsUserConsultationsBo bo) {
        CmsUserConsultations update = MapstructUtils.convert(bo, CmsUserConsultations.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(CmsUserConsultations entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除用户咨询信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
