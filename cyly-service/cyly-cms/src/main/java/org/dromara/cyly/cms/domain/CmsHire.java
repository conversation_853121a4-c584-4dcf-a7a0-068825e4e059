package org.dromara.cyly.cms.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 招聘信息对象 cms_hire
 *
 * <AUTHOR>
 * @date 2025-03-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cms_hire")
public class CmsHire extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 招聘id
     */
    @TableId(value = "hire_id")
    private Long hireId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 性别
     */
    private String sex;

    /**
     * 年龄
     */
    private Long age;

    /**
     * 学历
     */
    private String education;

    /**
     * 应聘职位
     */
    private String job;

    /**
     * 行政区域代码
     */
    private Long areaId;

    /**
     * 简历附件id
     */
    private Long ossId;


}
