package org.dromara.cyly.cms.runner;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.cyly.cms.service.ICmsEmailRecipientService;
import org.dromara.cyly.cms.service.ICmsEmailSenderService;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * @version 1.0
 * @author: 肖剑峰
 * @date: 2025年3月9日 14:59:16
 * 描述：初始化官网招聘信息对应业务数据
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class complainApplicationRunner implements ApplicationRunner {
    private final ICmsEmailRecipientService cmsEmailRecipientService;
    private final ICmsEmailSenderService cmsEmailSenderService;

    @Override
    public void run(ApplicationArguments args) {
        try {
            cmsEmailSenderService.init();
            log.info("初始化邮件发送者配置成功");
        } catch (Exception e) {
            log.error("初始化邮件发送者配置失败", e);
        }

        try {
            cmsEmailRecipientService.init();
            log.info("初始化邮件接受者配置成功");
        } catch (Exception e) {
            log.error("初始化邮件接受者配置失败", e);
        }
    }
}
