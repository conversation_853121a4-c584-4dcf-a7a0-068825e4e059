package org.dromara.cyly.cms.service;

import org.dromara.cyly.cms.domain.CmsEmailRecipient;
import org.dromara.cyly.cms.domain.vo.CmsEmailRecipientVo;
import org.dromara.cyly.cms.domain.bo.CmsEmailRecipientBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 邮件接收方Service接口
 *
 * <AUTHOR>
 * @date 2025-03-08
 */
public interface ICmsEmailRecipientService {

    void init();

    /**
     * 查询邮件接收方
     *
     * @param recipientId 主键
     * @return 邮件接收方
     */
    CmsEmailRecipientVo queryById(Long recipientId);

    /**
     * 分页查询邮件接收方列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 邮件接收方分页列表
     */
    TableDataInfo<CmsEmailRecipientVo> queryPageList(CmsEmailRecipientBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的邮件接收方列表
     *
     * @param bo 查询条件
     * @return 邮件接收方列表
     */
    List<CmsEmailRecipientVo> queryList(CmsEmailRecipientBo bo);

    /**
     * 新增邮件接收方
     *
     * @param bo 邮件接收方
     * @return 是否新增成功
     */
    Boolean insertByBo(CmsEmailRecipientBo bo);

    /**
     * 修改邮件接收方
     *
     * @param bo 邮件接收方
     * @return 是否修改成功
     */
    Boolean updateByBo(CmsEmailRecipientBo bo);

    /**
     * 校验并批量删除邮件接收方信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
