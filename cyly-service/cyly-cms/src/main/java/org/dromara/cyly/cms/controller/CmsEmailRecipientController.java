package org.dromara.cyly.cms.controller;

import java.util.List;

import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.web.core.BaseController;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.cyly.cms.domain.vo.CmsEmailRecipientVo;
import org.dromara.cyly.cms.domain.bo.CmsEmailRecipientBo;
import org.dromara.cyly.cms.service.ICmsEmailRecipientService;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * 邮件接收方
 * 前端访问路由地址为:/cms/emailRecipient
 *
 * <AUTHOR>
 * @date 2025-03-08
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/emailRecipient")
public class CmsEmailRecipientController extends BaseController {

    private final ICmsEmailRecipientService cmsEmailRecipientService;

    /**
     * 查询邮件接收方列表
     */
    @SaCheckPermission("cms:emailRecipient:list")
    @GetMapping("/list")
    public TableDataInfo<CmsEmailRecipientVo> list(CmsEmailRecipientBo bo, PageQuery pageQuery) {
        return cmsEmailRecipientService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出邮件接收方列表
     */
    @SaCheckPermission("cms:emailRecipient:export")
    @Log(title = "邮件接收方", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(CmsEmailRecipientBo bo, HttpServletResponse response) {
        List<CmsEmailRecipientVo> list = cmsEmailRecipientService.queryList(bo);
        ExcelUtil.exportExcel(list, "邮件接收方", CmsEmailRecipientVo.class, response);
    }

    /**
     * 获取邮件接收方详细信息
     *
     * @param recipientId 主键
     */
    @SaCheckPermission("cms:emailRecipient:query")
    @GetMapping("/{recipientId}")
    public R<CmsEmailRecipientVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long recipientId) {
        return R.ok(cmsEmailRecipientService.queryById(recipientId));
    }

    /**
     * 新增邮件接收方
     */
    @SaCheckPermission("cms:emailRecipient:add")
    @Log(title = "邮件接收方", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/add")
    public R<Void> add(@Validated(AddGroup.class) @RequestBody CmsEmailRecipientBo bo) {
        return toAjax(cmsEmailRecipientService.insertByBo(bo));
    }

    /**
     * 修改邮件接收方
     */
    @SaCheckPermission("cms:emailRecipient:edit")
    @Log(title = "邮件接收方", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/edit")
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody CmsEmailRecipientBo bo) {
        return toAjax(cmsEmailRecipientService.updateByBo(bo));
    }

    /**
     * 删除邮件接收方
     *
     * @param recipientIds 主键串
     */
    @SaCheckPermission("cms:emailRecipient:remove")
    @Log(title = "邮件接收方", businessType = BusinessType.DELETE)
    @PostMapping("/delete/{recipientIds}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] recipientIds) {
        return toAjax(cmsEmailRecipientService.deleteWithValidByIds(List.of(recipientIds), true));
    }
}
