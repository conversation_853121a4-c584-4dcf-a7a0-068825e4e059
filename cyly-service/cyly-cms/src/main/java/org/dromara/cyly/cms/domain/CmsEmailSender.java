package org.dromara.cyly.cms.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 邮件发送者对象 cms_email_sender
 *
 * <AUTHOR>
 * @date 2025-03-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cms_email_sender")
public class CmsEmailSender extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 邮件发送方id
     */
    @TableId(value = "sender_id")
    private Long senderId;

    /**
     * 邮件服务地址
     */
    private String senderEmailHost;

    /**
     * 邮箱加密端口号
     */
    private Long senderEmailPost;

    /**
     * 发送方
     */
    private String senderEmailFrom;

    /**
     * 发送方用户名
     */
    private String senderEmailUser;

    /**
     * 是否需要用户名密码验证
     */
    private String senderEmailAuth;

    /**
     * 授权码
     */
    private String senderEmailPassword;

    /**
     * 使用 STARTTLS安全连接，STARTTLS是对纯文本通信协议的扩展
     */
    private String starttlsEnable;

    /**
     * 是否使用SSL安全连接
     */
    private String sslEnable;

    /**
     * 排序值
     */
    private Long senderSort;

    /**
     * 账户状态（0：启用，1：禁用）
     */
    private Long status;

    /**
     * 备注
     */
    private String remark;


}
