package org.dromara.cyly.cms.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

import java.io.Serial;

/**
 * 文章对象 cms_article
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cms_article")
public class CmsArticle extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 文章id
     */
    @TableId(value = "article_id")
    private Long articleId;

    /**
     * 栏目id
     */
    private Long columnId;

    /**
     * 文章标题
     */
    private String articleTitle;

    /**
     * 作者
     */
    private String articleAuthor;

    /**
     *
     * 轮播图
     */
    public String articleCarouselImage;

    /**
     * 文章排序值(默认为0)
     */
    private String articleOrder;

    /**
     * 文章类型(1文章，2页面)
     */
    private String articleType;

    /**
     * 文章摘要
     */
    private String articleExcerpt;

    /**
     * 文章内容
     */
    private String articleContent;

    /**
     * 文章状态，发布(1)、草稿(0)
     */
    private String status;

    /**
     * 文章被查看的次数统计
     */
    private Long viewsCount;

    /**
     * 是否为特色文章标志位，可用于首页推荐(默认0,true为1)
     */
    private Long isFeatured;

    /**
     * 文章封面
     */
    private String featuredImage;

    /**
     * 文章正式发布日期
     */
    private Date publishedTime;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
