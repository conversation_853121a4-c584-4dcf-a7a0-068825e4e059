package org.dromara.cyly.cms.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.extra.mail.MailAccount;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.mail.config.properties.MailProperties;
import org.dromara.common.mail.constant.EmailConstants;
import org.dromara.common.mail.utils.MailUtils;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.cyly.cms.domain.CmsEmailRecipient;
import org.dromara.cyly.cms.domain.CmsEmailSender;
import org.dromara.cyly.cms.domain.CmsHire;
import org.dromara.cyly.cms.domain.bo.CmsEmailLogBo;
import org.dromara.cyly.cms.service.ICmsEmailLogService;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;

/**
 * @version 1.0
 * @author: 枫玥筱筱
 * @date: 2024/5/23 8:51
 * 描述：异步发送邮件工具类
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class MailAsyncService {
    private final MailProperties mailProperties;
    private final ICmsEmailLogService cmsEmailLogService;
    //邮件发送状态
    private final String sendStatusSuccess = "0";
    private final String sendStatusFail = "1";

    @Async("undertowVirtualThreadExecutor")
    @EventListener
    public Future<String> sendMail(@NotNull CmsHire bo) {
        //测试邮箱发送
        //MailUtils.sendText("<EMAIL>", bo.getTitle(), bo.getContent(), null);
        TimeInterval timeInterval = DateUtil.timer();
        //邮件账户对象
        MailAccount mailAccount = new MailAccount();
        //查询发件人列表
        //List<QyEmailSender> qyEmailSenderList = RedisUtils.getCacheList(EmailConstants.EMAIL_SENDER);
        Map<String, List<CmsEmailSender>> senderMap = RedisUtils.getCacheMap(EmailConstants.EMAIL_SENDER);
        List<CmsEmailSender> qyEmailSenderList = senderMap.get("sendList");
        //查询收件人列表
        Map<String, List<CmsEmailRecipient>> recipientMap = RedisUtils.getCacheMap(EmailConstants.EMAIL_RECIPIENT);
        List<CmsEmailRecipient> qyEmailRecipients = recipientMap.get("recipientList");
        //List<QyEmailRecipient> qyEmailRecipients = RedisUtils.getCacheList(EmailConstants.EMAIL_RECIPIENT);
        List<String> recipientList = new ArrayList<>();
        qyEmailRecipients.forEach(i -> recipientList.add(i.getRecipientEmail()));
        //定义耗时
        String timeConsuming;
        //定义求职标题信息
        String title = bo.getName()+"的求职信息";
        //定义求职信息内容
        String content = "姓名："+bo.getName()+"，性别："+bo.getSex()+"，年龄："+bo.getAge()+"，学历："+bo.getEducation()+"，应聘职位："+bo.getJob();
        try {
            if (mailProperties.getEnabled() && !qyEmailSenderList.isEmpty() && !qyEmailRecipients.isEmpty()) {
                //邮件发送机制：主邮箱发送邮件失败，采用备用邮箱进行发送
                mailAccount = initMailAccount(qyEmailSenderList, EmailConstants.MAIN_EMAIL);
                mailAccount.setConnectionTimeout(10000);
                mailAccount.setTimeout(10000);
                //发送邮件给多人
                MailUtils.send(mailAccount, true, recipientList, null, null, title, content, null, false, null);
                timeConsuming = "发送耗时：" + timeInterval.intervalSecond() + "秒";
                insertEmailLog(bo, mailAccount.getFrom(), recipientList, timeConsuming, sendStatusSuccess, "发送成功！");
            }
        } catch (Exception e) {
            log.error("邮件发送失败:{}", e.getMessage());
            if (!qyEmailSenderList.isEmpty() && !qyEmailRecipients.isEmpty()) {
                //采用备用邮箱发送
                mailAccount = initMailAccount(qyEmailSenderList, EmailConstants.BACKUP_EMAIL);
                MailUtils.send(mailAccount, true, recipientList, null, null, title, content, null, false, null);
            }
            timeConsuming = "发送耗时：" + timeInterval.intervalSecond() + "秒";
            insertEmailLog(bo, mailAccount.getFrom(), recipientList, timeConsuming, sendStatusFail, e.getMessage());
            return new AsyncResult<String>("邮件发送失败！");
        }
        log.info("邮件发送成功");
        return new AsyncResult<String>("邮件发送成功！");
    }

    public MailAccount initMailAccount(List<CmsEmailSender> qyEmailSenderList, Long status) {
        //邮件账户对象
        MailAccount mailAccount = new MailAccount();
        //使用数据表中发送发信息覆盖MailAccount发送方信息
        qyEmailSenderList.forEach(i -> {
            if (i.getStatus().equals(status)) {
                //使用主邮箱信息
                mailAccount.setHost(i.getSenderEmailHost());
                mailAccount.setPort(i.getSenderEmailPost().intValue());
                mailAccount.setFrom(i.getSenderEmailFrom());
                mailAccount.setUser(i.getSenderEmailUser());
                mailAccount.setAuth(Boolean.parseBoolean(i.getSenderEmailAuth()));
                mailAccount.setPass(i.getSenderEmailPassword());
                mailAccount.setStarttlsEnable(Boolean.parseBoolean(i.getStarttlsEnable()));
                mailAccount.setSslEnable(Boolean.valueOf(i.getSslEnable()));
            }
        });
        return mailAccount;
    }

    @Async
    public void insertEmailLog(@NotNull CmsHire bo, String emailSender,List<String> recipientList,String timeConsuming, String sendStatus, String backReason) {
        CmsEmailLogBo cmsEmailLog = new CmsEmailLogBo();
        cmsEmailLog.setSendFrom(emailSender);
        cmsEmailLog.setSendUser(recipientList.toString());
        cmsEmailLog.setTargetId(bo.getHireId());
        cmsEmailLog.setEmailTitle(bo.getName()+"的求职信息");
        cmsEmailLog.setEmailContent("姓名："+bo.getName()+"，性别："+bo.getSex()+"，年龄："+bo.getAge()+"，学历："+bo.getEducation()+"，应聘职位："+bo.getJob());
        cmsEmailLog.setTimeConsuming(timeConsuming);
        cmsEmailLog.setSendStatus(sendStatus);
        cmsEmailLog.setBackReason(backReason);
        Boolean flag = cmsEmailLogService.insertByBo(cmsEmailLog);
        log.info("插入邮件日志成功");
        //发布事件
        SpringUtils.context().publishEvent(flag);
    }
}
