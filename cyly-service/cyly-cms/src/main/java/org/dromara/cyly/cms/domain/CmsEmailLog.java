package org.dromara.cyly.cms.domain;

import org.dromara.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;

/**
 * 邮件发送日志对象 cms_email_log
 *
 * <AUTHOR>
 * @date 2025-03-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cms_email_log")
public class CmsEmailLog extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 邮件发送日志id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 发送方
     */
    private String sendFrom;

    /**
     * 接收方
     */
    private String sendUser;

    /**
     * 邮件发送对象id
     */
    private Long targetId;

    /**
     * 邮件标题
     */
    private String emailTitle;

    /**
     * 邮件内容
     */
    private String emailContent;

    /**
     * 邮件发送耗时
     */
    private String timeConsuming;

    /**
     * 发送状态(0成功 1失败)
     */
    private String sendStatus;

    /**
     * 返回原因
     */
    private String backReason;

    /**
     * 创建部门
     */
    private Long creteDept;


}
