package org.dromara.cyly.cms.domain.vo;


import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.cyly.cms.domain.CmsColumn;
import java.io.Serial;
import java.io.Serializable;


/**
 * 栏目视图对象 cms-column
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = CmsColumn.class)
public class CmsColumnVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 栏目id
     */
    @ExcelProperty(value = "栏目id")
    private Long columnId;

    /**
     * 栏目名称
     */
    @ExcelProperty(value = "栏目名称")
    private String columnName;

    /**
     * 栏目图片
     */
    @ExcelProperty(value = "栏目图片")
    private String columnImage;

    /**
     * 栏目内容
     */
    @ExcelProperty(value = "栏目内容")
    private String columnContent;

    /**
     * 父id(顶级栏目则为0，默认0)
     */
    @ExcelProperty(value = "父id(顶级栏目则为0，默认0)")
    private Long parentId;

    /**
     * 排序字段
     */
    @ExcelProperty(value = "排序字段")
    private Long sortOrder;

    /**
     * 栏目状态-启用(1)或禁用(0)
     */
    @ExcelProperty(value = "栏目状态-启用(1)或禁用(0)")
    private Long status;

    /**
     * 路由地址
     */
    private String path;

    /**
     * 组件路径
     */
    private String component;

    /**
     * 路由参数
     */
    private String queryParam;

    /**
     * 是否为外链（0是 1否）
     */
    private String isFrame;

    /**
     * 是否缓存（0缓存 1不缓存）
     */
    private String isCache;

    /**
     * 类型（M目录 C菜单 F按钮）
     */
    private String menuType;

    /**
     * 显示状态（0显示 1隐藏）
     */
    private String visible;

    /**
     * 权限字符串
     */
    private String perms;

    /**
     * 菜单图标
     */
    private String icon;

}
