package org.dromara.cyly.cms.service.impl;

import org.apache.commons.codec.digest.Md5Crypt;
import org.apache.dubbo.common.utils.MD5Utils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.encrypt.utils.EncryptUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.dromara.cyly.cms.domain.bo.CmsArticleBo;
import org.dromara.cyly.cms.domain.vo.CmsArticleVo;
import org.dromara.cyly.cms.domain.CmsArticle;
import org.dromara.cyly.cms.mapper.CmsArticleMapper;
import org.dromara.cyly.cms.service.ICmsArticleService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 文章Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-19
 */
@RequiredArgsConstructor
@Service
public class CmsArticleServiceImpl implements ICmsArticleService {

    private final CmsArticleMapper baseMapper;

    /**
     * 查询文章
     *
     * @param articleId 主键
     * @return 文章
     */
    @Override
    public CmsArticleVo queryById(Long articleId){
        return baseMapper.selectVoById(articleId);
    }

    /**
     * 分页查询文章列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 文章分页列表
     */
    @Override
    public TableDataInfo<CmsArticleVo> queryPageList(CmsArticleBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<CmsArticle> lqw = buildQueryWrapper(bo);
        Page<CmsArticleVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的文章列表
     *
     * @param bo 查询条件
     * @return 文章列表
     */
    @Override
    public List<CmsArticleVo> queryList(CmsArticleBo bo) {
        LambdaQueryWrapper<CmsArticle> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<CmsArticle> buildQueryWrapper(CmsArticleBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<CmsArticle> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getColumnId() != null, CmsArticle::getColumnId, bo.getColumnId());
        lqw.like(StringUtils.isNotBlank(bo.getArticleTitle()), CmsArticle::getArticleTitle, bo.getArticleTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getArticleAuthor()), CmsArticle::getArticleAuthor, bo.getArticleAuthor());
        lqw.eq(StringUtils.isNotBlank(bo.getArticleCarouselImage()), CmsArticle::getArticleCarouselImage, bo.getArticleCarouselImage());
        lqw.eq(StringUtils.isNotBlank(bo.getArticleOrder()), CmsArticle::getArticleOrder, bo.getArticleOrder());
        lqw.eq(StringUtils.isNotBlank(bo.getArticleType()), CmsArticle::getArticleType, bo.getArticleType());
        lqw.eq(StringUtils.isNotBlank(bo.getArticleExcerpt()), CmsArticle::getArticleExcerpt, bo.getArticleExcerpt());
        lqw.eq(StringUtils.isNotBlank(bo.getArticleContent()), CmsArticle::getArticleContent, bo.getArticleContent());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), CmsArticle::getStatus, bo.getStatus());
        lqw.eq(bo.getViewsCount() != null, CmsArticle::getViewsCount, bo.getViewsCount());
        lqw.eq(bo.getIsFeatured() != null, CmsArticle::getIsFeatured, bo.getIsFeatured());
        lqw.eq(StringUtils.isNotBlank(bo.getFeaturedImage()), CmsArticle::getFeaturedImage, bo.getFeaturedImage());
        lqw.eq(bo.getPublishedTime() != null, CmsArticle::getPublishedTime, bo.getPublishedTime());
        lqw.orderByDesc(CmsArticle::getCreateTime);
        return lqw;
    }

    /**
     * 新增文章
     *
     * @param bo 文章
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(CmsArticleBo bo) {
        CmsArticle add = MapstructUtils.convert(bo, CmsArticle.class);
        String articleContent = EncryptUtils.decryptByBase64(add.getArticleContent());
        add.setArticleContent(articleContent);
        //实现md5解密
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setArticleId(add.getArticleId());
        }
        return flag;
    }

    /**
     * 修改文章
     *
     * @param bo 文章
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(CmsArticleBo bo) {
        CmsArticle update = MapstructUtils.convert(bo, CmsArticle.class);
        System.out.println(update.getArticleContent());
        String articleContent = EncryptUtils.decryptByBase64(update.getArticleContent());
        System.out.println(articleContent);
        update.setArticleContent(articleContent);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(CmsArticle entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除文章信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
