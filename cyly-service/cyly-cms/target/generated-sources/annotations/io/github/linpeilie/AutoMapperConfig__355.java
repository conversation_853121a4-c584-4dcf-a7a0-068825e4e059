package io.github.linpeilie;

import org.dromara.cyly.cms.domain.CmsArticleToCmsArticleVoMapper__3;
import org.dromara.cyly.cms.domain.CmsCarouselImageToCmsCarouselImageVoMapper__3;
import org.dromara.cyly.cms.domain.CmsColumnToCmsColumnVoMapper__3;
import org.dromara.cyly.cms.domain.CmsEmailLogToCmsEmailLogVoMapper__3;
import org.dromara.cyly.cms.domain.CmsEmailRecipientToCmsEmailRecipientVoMapper__3;
import org.dromara.cyly.cms.domain.CmsEmailSenderToCmsEmailSenderVoMapper__3;
import org.dromara.cyly.cms.domain.CmsHireToCmsHireVoMapper__3;
import org.dromara.cyly.cms.domain.CmsUserConsultationsToCmsUserConsultationsVoMapper__3;
import org.dromara.cyly.cms.domain.bo.CmsArticleBoToCmsArticleMapper__3;
import org.dromara.cyly.cms.domain.bo.CmsCarouselImageBoToCmsCarouselImageMapper__3;
import org.dromara.cyly.cms.domain.bo.CmsColumnBoToCmsColumnMapper__3;
import org.dromara.cyly.cms.domain.bo.CmsEmailLogBoToCmsEmailLogMapper__3;
import org.dromara.cyly.cms.domain.bo.CmsEmailRecipientBoToCmsEmailRecipientMapper__3;
import org.dromara.cyly.cms.domain.bo.CmsEmailSenderBoToCmsEmailSenderMapper__3;
import org.dromara.cyly.cms.domain.bo.CmsHireBoToCmsHireMapper__3;
import org.dromara.cyly.cms.domain.bo.CmsUserConsultationsBoToCmsUserConsultationsMapper__3;
import org.dromara.cyly.cms.domain.vo.CmsArticleVoToCmsArticleMapper__3;
import org.dromara.cyly.cms.domain.vo.CmsCarouselImageVoToCmsCarouselImageMapper__3;
import org.dromara.cyly.cms.domain.vo.CmsColumnVoToCmsColumnMapper__3;
import org.dromara.cyly.cms.domain.vo.CmsEmailLogVoToCmsEmailLogMapper__3;
import org.dromara.cyly.cms.domain.vo.CmsEmailRecipientVoToCmsEmailRecipientMapper__3;
import org.dromara.cyly.cms.domain.vo.CmsEmailSenderVoToCmsEmailSenderMapper__3;
import org.dromara.cyly.cms.domain.vo.CmsHireVoToCmsHireMapper__3;
import org.dromara.cyly.cms.domain.vo.CmsUserConsultationsVoToCmsUserConsultationsMapper__3;
import org.mapstruct.Builder;
import org.mapstruct.MapperConfig;
import org.mapstruct.ReportingPolicy;

@MapperConfig(
    componentModel = "spring-lazy",
    uses = {ConverterMapperAdapter__355.class, CmsColumnToCmsColumnVoMapper__3.class, CmsEmailLogBoToCmsEmailLogMapper__3.class, CmsCarouselImageToCmsCarouselImageVoMapper__3.class, CmsEmailLogToCmsEmailLogVoMapper__3.class, CmsUserConsultationsVoToCmsUserConsultationsMapper__3.class, CmsEmailRecipientVoToCmsEmailRecipientMapper__3.class, CmsEmailRecipientToCmsEmailRecipientVoMapper__3.class, CmsEmailLogVoToCmsEmailLogMapper__3.class, CmsUserConsultationsToCmsUserConsultationsVoMapper__3.class, CmsArticleBoToCmsArticleMapper__3.class, CmsEmailSenderBoToCmsEmailSenderMapper__3.class, CmsCarouselImageVoToCmsCarouselImageMapper__3.class, CmsCarouselImageBoToCmsCarouselImageMapper__3.class, CmsEmailSenderToCmsEmailSenderVoMapper__3.class, CmsHireBoToCmsHireMapper__3.class, CmsArticleVoToCmsArticleMapper__3.class, CmsColumnVoToCmsColumnMapper__3.class, CmsArticleToCmsArticleVoMapper__3.class, CmsEmailSenderVoToCmsEmailSenderMapper__3.class, CmsEmailRecipientBoToCmsEmailRecipientMapper__3.class, CmsUserConsultationsBoToCmsUserConsultationsMapper__3.class, CmsHireToCmsHireVoMapper__3.class, CmsColumnBoToCmsColumnMapper__3.class, CmsHireVoToCmsHireMapper__3.class},
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    builder = @Builder(buildMethod = "build", disableBuilder = true)
)
public interface AutoMapperConfig__355 {
}
