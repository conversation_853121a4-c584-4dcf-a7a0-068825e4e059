package org.dromara.cyly.cms.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.cyly.cms.domain.CmsHire;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T09:42:58+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class CmsHireVoToCmsHireMapperImpl implements CmsHireVoToCmsHireMapper {

    @Override
    public CmsHire convert(CmsHireVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        CmsHire cmsHire = new CmsHire();

        cmsHire.setAge( arg0.getAge() );
        cmsHire.setAreaId( arg0.getAreaId() );
        cmsHire.setEducation( arg0.getEducation() );
        cmsHire.setHireId( arg0.getHireId() );
        cmsHire.setJob( arg0.getJob() );
        cmsHire.setName( arg0.getName() );
        cmsHire.setOssId( arg0.getOssId() );
        cmsHire.setSex( arg0.getSex() );

        return cmsHire;
    }

    @Override
    public CmsHire convert(CmsHireVo arg0, CmsHire arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setAge( arg0.getAge() );
        arg1.setAreaId( arg0.getAreaId() );
        arg1.setEducation( arg0.getEducation() );
        arg1.setHireId( arg0.getHireId() );
        arg1.setJob( arg0.getJob() );
        arg1.setName( arg0.getName() );
        arg1.setOssId( arg0.getOssId() );
        arg1.setSex( arg0.getSex() );

        return arg1;
    }
}
