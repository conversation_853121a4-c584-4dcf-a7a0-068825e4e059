package org.dromara.cyly.cms.domain.vo;

import io.github.linpeilie.AutoMapperConfig__390;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.cms.domain.CmsColumn;
import org.dromara.cyly.cms.domain.CmsColumnToCmsColumnVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__390.class,
    uses = {CmsColumnToCmsColumnVoMapper.class},
    imports = {}
)
public interface CmsColumnVoToCmsColumnMapper extends BaseMapper<CmsColumnVo, CmsColumn> {
}
