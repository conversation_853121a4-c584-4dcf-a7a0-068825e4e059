package org.dromara.cyly.cms.domain.vo;

import io.github.linpeilie.AutoMapperConfig__390;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.cms.domain.CmsEmailLog;
import org.dromara.cyly.cms.domain.CmsEmailLogToCmsEmailLogVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__390.class,
    uses = {CmsEmailLogToCmsEmailLogVoMapper.class},
    imports = {}
)
public interface CmsEmailLogVoToCmsEmailLogMapper extends BaseMapper<CmsEmailLogVo, CmsEmailLog> {
}
