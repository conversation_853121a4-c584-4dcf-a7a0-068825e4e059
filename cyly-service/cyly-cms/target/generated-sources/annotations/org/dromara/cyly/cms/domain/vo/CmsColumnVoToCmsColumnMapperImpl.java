package org.dromara.cyly.cms.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.cyly.cms.domain.CmsColumn;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T09:42:58+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class CmsColumnVoToCmsColumnMapperImpl implements CmsColumnVoToCmsColumnMapper {

    @Override
    public CmsColumn convert(CmsColumnVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        CmsColumn cmsColumn = new CmsColumn();

        cmsColumn.setColumnContent( arg0.getColumnContent() );
        cmsColumn.setColumnId( arg0.getColumnId() );
        cmsColumn.setColumnImage( arg0.getColumnImage() );
        cmsColumn.setColumnName( arg0.getColumnName() );
        cmsColumn.setComponent( arg0.getComponent() );
        cmsColumn.setIcon( arg0.getIcon() );
        cmsColumn.setIsCache( arg0.getIsCache() );
        cmsColumn.setIsFrame( arg0.getIsFrame() );
        cmsColumn.setMenuType( arg0.getMenuType() );
        cmsColumn.setParentId( arg0.getParentId() );
        cmsColumn.setPath( arg0.getPath() );
        cmsColumn.setPerms( arg0.getPerms() );
        cmsColumn.setQueryParam( arg0.getQueryParam() );
        cmsColumn.setSortOrder( arg0.getSortOrder() );
        cmsColumn.setStatus( arg0.getStatus() );
        cmsColumn.setVisible( arg0.getVisible() );

        return cmsColumn;
    }

    @Override
    public CmsColumn convert(CmsColumnVo arg0, CmsColumn arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setColumnContent( arg0.getColumnContent() );
        arg1.setColumnId( arg0.getColumnId() );
        arg1.setColumnImage( arg0.getColumnImage() );
        arg1.setColumnName( arg0.getColumnName() );
        arg1.setComponent( arg0.getComponent() );
        arg1.setIcon( arg0.getIcon() );
        arg1.setIsCache( arg0.getIsCache() );
        arg1.setIsFrame( arg0.getIsFrame() );
        arg1.setMenuType( arg0.getMenuType() );
        arg1.setParentId( arg0.getParentId() );
        arg1.setPath( arg0.getPath() );
        arg1.setPerms( arg0.getPerms() );
        arg1.setQueryParam( arg0.getQueryParam() );
        arg1.setSortOrder( arg0.getSortOrder() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setVisible( arg0.getVisible() );

        return arg1;
    }
}
