package org.dromara.cyly.cms.domain.vo;

import io.github.linpeilie.AutoMapperConfig__355;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.cms.domain.CmsEmailSender;
import org.dromara.cyly.cms.domain.CmsEmailSenderToCmsEmailSenderVoMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__355.class,
    uses = {CmsEmailSenderToCmsEmailSenderVoMapper__2.class},
    imports = {}
)
public interface CmsEmailSenderVoToCmsEmailSenderMapper__2 extends BaseMapper<CmsEmailSenderVo, CmsEmailSender> {
}
