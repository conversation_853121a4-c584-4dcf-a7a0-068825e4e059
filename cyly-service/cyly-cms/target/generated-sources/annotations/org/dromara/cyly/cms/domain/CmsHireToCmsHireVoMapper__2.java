package org.dromara.cyly.cms.domain;

import io.github.linpeilie.AutoMapperConfig__355;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.cms.domain.bo.CmsHireBoToCmsHireMapper__2;
import org.dromara.cyly.cms.domain.vo.CmsHireVo;
import org.dromara.cyly.cms.domain.vo.CmsHireVoToCmsHireMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__355.class,
    uses = {CmsHireVoToCmsHireMapper__2.class,CmsHireBoToCmsHireMapper__2.class},
    imports = {}
)
public interface CmsHireToCmsHireVoMapper__2 extends BaseMapper<CmsHire, CmsHireVo> {
}
