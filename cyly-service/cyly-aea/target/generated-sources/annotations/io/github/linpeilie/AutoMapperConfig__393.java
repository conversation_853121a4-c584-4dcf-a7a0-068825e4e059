package io.github.linpeilie;

import org.dromara.cyly.aea.domain.AeaAdolescentHealthToAdolescentHealthVoMapper__1;
import org.dromara.cyly.aea.domain.AeaCaregiverInfoToAeaCaregiverInfoVoMapper__1;
import org.dromara.cyly.aea.domain.AeaClinicalAssessmentToClinicalAssessmentVoMapper__1;
import org.dromara.cyly.aea.domain.AeaElderFamilyToAeaElderFamilyVoMapper__1;
import org.dromara.cyly.aea.domain.AeaElderImportLogToAeaElderImportLogVoMapper__1;
import org.dromara.cyly.aea.domain.AeaElderInfoToAeaElderInfoVoMapper__1;
import org.dromara.cyly.aea.domain.AeaElderToAeaElderVoMapper__1;
import org.dromara.cyly.aea.domain.AeaFormFieldConfigToAeaFormFieldConfigVoMapper__1;
import org.dromara.cyly.aea.domain.AeaGbEvaluateBaseInfoToAeaGbEvaluateBaseInfoVoMapper__1;
import org.dromara.cyly.aea.domain.AeaGbEvaluateBaseToAeaGbEvaluateBaseVoMapper__1;
import org.dromara.cyly.aea.domain.AeaGbEvaluateScaleInfoToAeaGbEvaluateScaleInfoVoMapper__1;
import org.dromara.cyly.aea.domain.AeaGbMedicineToAeaGbMedicineVoMapper__1;
import org.dromara.cyly.aea.domain.AeaHomeCareToHomeCareVoMapper__1;
import org.dromara.cyly.aea.domain.AeaInterRaiToAeaInterRaiVoMapper__1;
import org.dromara.cyly.aea.domain.AeaLongTermCareToLongTermCareVoMapper__1;
import org.dromara.cyly.aea.domain.AeaTaskAbilityLevelToAeaTaskAbilityLevelVoMapper__1;
import org.dromara.cyly.aea.domain.AeaTaskBaseToAeaTaskBaseVoMapper__1;
import org.dromara.cyly.aea.domain.AeaTaskMedicineToAeaTaskMedicineVoMapper__1;
import org.dromara.cyly.aea.domain.AeaTaskScaleToAeaTaskScaleVoMapper__1;
import org.dromara.cyly.aea.domain.AeaTaskTimeToAeaTaskTimeVoMapper__1;
import org.dromara.cyly.aea.domain.AeaTaskTitleStatusToAeaTaskTitleStatusVoMapper__1;
import org.dromara.cyly.aea.domain.AeaTaskTotalToAeaTaskTotalVoMapper__1;
import org.dromara.cyly.aea.domain.bo.AdolescentHealthBoToAeaAdolescentHealthMapper__1;
import org.dromara.cyly.aea.domain.bo.AeaCaregiverInfoBoToAeaCaregiverInfoMapper__1;
import org.dromara.cyly.aea.domain.bo.AeaElderBoToAeaElderMapper__1;
import org.dromara.cyly.aea.domain.bo.AeaElderFamilyBoToAeaElderFamilyMapper__1;
import org.dromara.cyly.aea.domain.bo.AeaElderImportLogBoToAeaElderImportLogMapper__1;
import org.dromara.cyly.aea.domain.bo.AeaElderInfoBoToAeaElderInfoMapper__1;
import org.dromara.cyly.aea.domain.bo.AeaFormFieldConfigBoToAeaFormFieldConfigMapper__1;
import org.dromara.cyly.aea.domain.bo.AeaGbEvaluateBaseBoToAeaGbEvaluateBaseMapper__1;
import org.dromara.cyly.aea.domain.bo.AeaGbEvaluateBaseInfoBoToAeaGbEvaluateBaseInfoMapper__1;
import org.dromara.cyly.aea.domain.bo.AeaGbEvaluateScaleInfoBoToAeaGbEvaluateScaleInfoMapper__1;
import org.dromara.cyly.aea.domain.bo.AeaGbMedicineBoToAeaGbMedicineMapper__1;
import org.dromara.cyly.aea.domain.bo.AeaInterRaiBoToAeaInterRaiMapper__1;
import org.dromara.cyly.aea.domain.bo.AeaTaskAbilityLevelBoToAeaTaskAbilityLevelMapper__1;
import org.dromara.cyly.aea.domain.bo.AeaTaskBaseBoToAeaTaskBaseMapper__1;
import org.dromara.cyly.aea.domain.bo.AeaTaskMedicineBoToAeaTaskMedicineMapper__1;
import org.dromara.cyly.aea.domain.bo.AeaTaskScaleBoToAeaTaskScaleMapper__1;
import org.dromara.cyly.aea.domain.bo.AeaTaskTimeBoToAeaTaskTimeMapper__1;
import org.dromara.cyly.aea.domain.bo.AeaTaskTitleStatusBoToAeaTaskTitleStatusMapper__1;
import org.dromara.cyly.aea.domain.bo.AeaTaskTotalBoToAeaTaskTotalMapper__1;
import org.dromara.cyly.aea.domain.bo.ClinicalAssessmentBoToAeaClinicalAssessmentMapper__1;
import org.dromara.cyly.aea.domain.bo.HomeCareBoToAeaHomeCareMapper__1;
import org.dromara.cyly.aea.domain.bo.LongTermCareBoToAeaLongTermCareMapper__1;
import org.dromara.cyly.aea.domain.convert.AeaElderVoConvert;
import org.dromara.cyly.aea.domain.convert.AeaTaskScaleVoConvert;
import org.dromara.cyly.aea.domain.convert.AeaTaskTotalVoConvert;
import org.dromara.cyly.aea.domain.convert.ElderFamilyVoConvert;
import org.dromara.cyly.aea.domain.convert.ElderVoConvert;
import org.dromara.cyly.aea.domain.vo.AdolescentHealthVoToAeaAdolescentHealthMapper__1;
import org.dromara.cyly.aea.domain.vo.AeaCaregiverInfoVoToAeaCaregiverInfoMapper__1;
import org.dromara.cyly.aea.domain.vo.AeaElderFamilyVoToAeaElderFamilyMapper__1;
import org.dromara.cyly.aea.domain.vo.AeaElderImportLogVoToAeaElderImportLogMapper__1;
import org.dromara.cyly.aea.domain.vo.AeaElderInfoVoToAeaElderInfoMapper__1;
import org.dromara.cyly.aea.domain.vo.AeaElderVoToAeaElderMapper__1;
import org.dromara.cyly.aea.domain.vo.AeaFormFieldConfigVoToAeaFormFieldConfigMapper__1;
import org.dromara.cyly.aea.domain.vo.AeaGbEvaluateBaseInfoVoToAeaGbEvaluateBaseInfoMapper__1;
import org.dromara.cyly.aea.domain.vo.AeaGbEvaluateBaseVoToAeaGbEvaluateBaseMapper__1;
import org.dromara.cyly.aea.domain.vo.AeaGbEvaluateScaleInfoVoToAeaGbEvaluateScaleInfoMapper__1;
import org.dromara.cyly.aea.domain.vo.AeaGbMedicineVoToAeaGbMedicineMapper__1;
import org.dromara.cyly.aea.domain.vo.AeaInterRaiVoToAeaInterRaiMapper__1;
import org.dromara.cyly.aea.domain.vo.AeaTaskAbilityLevelVoToAeaTaskAbilityLevelMapper__1;
import org.dromara.cyly.aea.domain.vo.AeaTaskBaseVoToAeaTaskBaseMapper__1;
import org.dromara.cyly.aea.domain.vo.AeaTaskMedicineVoToAeaTaskMedicineMapper__1;
import org.dromara.cyly.aea.domain.vo.AeaTaskScaleVoToAeaTaskScaleMapper__1;
import org.dromara.cyly.aea.domain.vo.AeaTaskTimeVoToAeaTaskTimeMapper__1;
import org.dromara.cyly.aea.domain.vo.AeaTaskTitleStatusVoToAeaTaskTitleStatusMapper__1;
import org.dromara.cyly.aea.domain.vo.AeaTaskTotalVoToAeaTaskTotalMapper__1;
import org.dromara.cyly.aea.domain.vo.ClinicalAssessmentVoToAeaClinicalAssessmentMapper__1;
import org.dromara.cyly.aea.domain.vo.HomeCareVoToAeaHomeCareMapper__1;
import org.dromara.cyly.aea.domain.vo.LongTermCareVoToAeaLongTermCareMapper__1;
import org.mapstruct.Builder;
import org.mapstruct.MapperConfig;
import org.mapstruct.ReportingPolicy;

@MapperConfig(
    componentModel = "spring-lazy",
    uses = {ConverterMapperAdapter__393.class, AeaElderToAeaElderVoMapper__1.class, HomeCareBoToAeaHomeCareMapper__1.class, LongTermCareVoToAeaLongTermCareMapper__1.class, AeaTaskBaseBoToAeaTaskBaseMapper__1.class, AeaTaskScaleVoConvert.class, AeaGbEvaluateScaleInfoToAeaGbEvaluateScaleInfoVoMapper__1.class, AeaTaskTotalToAeaTaskTotalVoMapper__1.class, AeaGbEvaluateScaleInfoBoToAeaGbEvaluateScaleInfoMapper__1.class, AeaInterRaiBoToAeaInterRaiMapper__1.class, AeaClinicalAssessmentToClinicalAssessmentVoMapper__1.class, AeaFormFieldConfigVoToAeaFormFieldConfigMapper__1.class, AeaGbMedicineToAeaGbMedicineVoMapper__1.class, AeaTaskScaleBoToAeaTaskScaleMapper__1.class, AeaGbEvaluateBaseToAeaGbEvaluateBaseVoMapper__1.class, ElderFamilyVoConvert.class, AeaTaskTitleStatusVoToAeaTaskTitleStatusMapper__1.class, AeaElderImportLogToAeaElderImportLogVoMapper__1.class, AeaTaskTimeToAeaTaskTimeVoMapper__1.class, AeaGbEvaluateBaseInfoVoToAeaGbEvaluateBaseInfoMapper__1.class, AdolescentHealthVoToAeaAdolescentHealthMapper__1.class, AeaElderVoToAeaElderMapper__1.class, ClinicalAssessmentBoToAeaClinicalAssessmentMapper__1.class, AeaTaskAbilityLevelBoToAeaTaskAbilityLevelMapper__1.class, AeaElderInfoBoToAeaElderInfoMapper__1.class, AeaTaskTotalVoConvert.class, AeaElderInfoToAeaElderInfoVoMapper__1.class, AeaGbMedicineVoToAeaGbMedicineMapper__1.class, LongTermCareBoToAeaLongTermCareMapper__1.class, AeaTaskTotalBoToAeaTaskTotalMapper__1.class, AeaElderFamilyBoToAeaElderFamilyMapper__1.class, AeaTaskTitleStatusBoToAeaTaskTitleStatusMapper__1.class, AeaLongTermCareToLongTermCareVoMapper__1.class, AeaAdolescentHealthToAdolescentHealthVoMapper__1.class, AeaElderFamilyVoToAeaElderFamilyMapper__1.class, AeaElderVoConvert.class, AeaElderInfoVoToAeaElderInfoMapper__1.class, AeaTaskTimeVoToAeaTaskTimeMapper__1.class, AdolescentHealthBoToAeaAdolescentHealthMapper__1.class, ElderVoConvert.class, AeaTaskTitleStatusToAeaTaskTitleStatusVoMapper__1.class, AeaTaskTotalVoToAeaTaskTotalMapper__1.class, ClinicalAssessmentVoToAeaClinicalAssessmentMapper__1.class, AeaCaregiverInfoBoToAeaCaregiverInfoMapper__1.class, AeaElderImportLogVoToAeaElderImportLogMapper__1.class, AeaGbEvaluateScaleInfoVoToAeaGbEvaluateScaleInfoMapper__1.class, AeaInterRaiVoToAeaInterRaiMapper__1.class, AeaGbEvaluateBaseInfoBoToAeaGbEvaluateBaseInfoMapper__1.class, AeaTaskAbilityLevelToAeaTaskAbilityLevelVoMapper__1.class, AeaGbEvaluateBaseVoToAeaGbEvaluateBaseMapper__1.class, AeaTaskMedicineToAeaTaskMedicineVoMapper__1.class, AeaHomeCareToHomeCareVoMapper__1.class, AeaTaskAbilityLevelVoToAeaTaskAbilityLevelMapper__1.class, AeaTaskBaseVoToAeaTaskBaseMapper__1.class, AeaFormFieldConfigToAeaFormFieldConfigVoMapper__1.class, AeaElderFamilyToAeaElderFamilyVoMapper__1.class, AeaTaskScaleVoToAeaTaskScaleMapper__1.class, AeaTaskScaleToAeaTaskScaleVoMapper__1.class, AeaCaregiverInfoToAeaCaregiverInfoVoMapper__1.class, AeaCaregiverInfoVoToAeaCaregiverInfoMapper__1.class, AeaFormFieldConfigBoToAeaFormFieldConfigMapper__1.class, AeaGbEvaluateBaseBoToAeaGbEvaluateBaseMapper__1.class, HomeCareVoToAeaHomeCareMapper__1.class, AeaTaskBaseToAeaTaskBaseVoMapper__1.class, AeaInterRaiToAeaInterRaiVoMapper__1.class, AeaGbEvaluateBaseInfoToAeaGbEvaluateBaseInfoVoMapper__1.class, AeaTaskMedicineBoToAeaTaskMedicineMapper__1.class, AeaElderBoToAeaElderMapper__1.class, AeaTaskTimeBoToAeaTaskTimeMapper__1.class, AeaTaskMedicineVoToAeaTaskMedicineMapper__1.class, AeaGbMedicineBoToAeaGbMedicineMapper__1.class, AeaElderImportLogBoToAeaElderImportLogMapper__1.class},
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    builder = @Builder(buildMethod = "build", disableBuilder = true)
)
public interface AutoMapperConfig__393 {
}
