package org.dromara.cyly.aea.domain;

import javax.annotation.processing.Generated;
import org.dromara.cyly.aea.domain.vo.AeaTaskBaseVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T09:45:01+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AeaTaskBaseToAeaTaskBaseVoMapper__4Impl implements AeaTaskBaseToAeaTaskBaseVoMapper__4 {

    @Override
    public AeaTaskBaseVo convert(AeaTaskBase arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AeaTaskBaseVo aeaTaskBaseVo = new AeaTaskBaseVo();

        aeaTaskBaseVo.setBaseId( arg0.getBaseId() );
        aeaTaskBaseVo.setContent( arg0.getContent() );
        aeaTaskBaseVo.setId( arg0.getId() );
        aeaTaskBaseVo.setOptionId( arg0.getOptionId() );
        aeaTaskBaseVo.setTaskId( arg0.getTaskId() );
        aeaTaskBaseVo.setTitleId( arg0.getTitleId() );

        return aeaTaskBaseVo;
    }

    @Override
    public AeaTaskBaseVo convert(AeaTaskBase arg0, AeaTaskBaseVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setBaseId( arg0.getBaseId() );
        arg1.setContent( arg0.getContent() );
        arg1.setId( arg0.getId() );
        arg1.setOptionId( arg0.getOptionId() );
        arg1.setTaskId( arg0.getTaskId() );
        arg1.setTitleId( arg0.getTitleId() );

        return arg1;
    }
}
