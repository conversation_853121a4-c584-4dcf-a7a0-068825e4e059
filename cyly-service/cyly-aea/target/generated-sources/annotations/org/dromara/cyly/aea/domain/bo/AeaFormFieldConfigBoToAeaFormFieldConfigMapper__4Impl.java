package org.dromara.cyly.aea.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.cyly.aea.domain.AeaFormFieldConfig;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T09:45:01+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AeaFormFieldConfigBoToAeaFormFieldConfigMapper__4Impl implements AeaFormFieldConfigBoToAeaFormFieldConfigMapper__4 {

    @Override
    public AeaFormFieldConfig convert(AeaFormFieldConfigBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AeaFormFieldConfig aeaFormFieldConfig = new AeaFormFieldConfig();

        aeaFormFieldConfig.setCreateBy( arg0.getCreateBy() );
        aeaFormFieldConfig.setCreateTime( arg0.getCreateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            aeaFormFieldConfig.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        aeaFormFieldConfig.setSearchValue( arg0.getSearchValue() );
        aeaFormFieldConfig.setUpdateBy( arg0.getUpdateBy() );
        aeaFormFieldConfig.setUpdateTime( arg0.getUpdateTime() );
        aeaFormFieldConfig.setCreateDept( arg0.getCreateDept() );
        aeaFormFieldConfig.setId( arg0.getId() );
        aeaFormFieldConfig.setMappingKey( arg0.getMappingKey() );
        aeaFormFieldConfig.setPid( arg0.getPid() );
        aeaFormFieldConfig.setRequired( arg0.getRequired() );
        aeaFormFieldConfig.setTitle( arg0.getTitle() );
        aeaFormFieldConfig.setType( arg0.getType() );
        aeaFormFieldConfig.setWeight( arg0.getWeight() );

        return aeaFormFieldConfig;
    }

    @Override
    public AeaFormFieldConfig convert(AeaFormFieldConfigBo arg0, AeaFormFieldConfig arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setId( arg0.getId() );
        arg1.setMappingKey( arg0.getMappingKey() );
        arg1.setPid( arg0.getPid() );
        arg1.setRequired( arg0.getRequired() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setType( arg0.getType() );
        arg1.setWeight( arg0.getWeight() );

        return arg1;
    }
}
