package org.dromara.cyly.aea.domain;

import io.github.linpeilie.AutoMapperConfig__358;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.domain.bo.AeaElderInfoBoToAeaElderInfoMapper__4;
import org.dromara.cyly.aea.domain.vo.AeaElderInfoVo;
import org.dromara.cyly.aea.domain.vo.AeaElderInfoVoToAeaElderInfoMapper__4;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__358.class,
    uses = {AeaElderInfoBoToAeaElderInfoMapper__4.class,AeaElderInfoVoToAeaElderInfoMapper__4.class},
    imports = {}
)
public interface AeaElderInfoToAeaElderInfoVoMapper__4 extends BaseMapper<AeaElderInfo, AeaElderInfoVo> {
}
