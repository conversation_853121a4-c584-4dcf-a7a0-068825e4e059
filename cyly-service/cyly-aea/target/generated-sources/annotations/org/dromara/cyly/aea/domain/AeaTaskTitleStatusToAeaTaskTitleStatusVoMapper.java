package org.dromara.cyly.aea.domain;

import io.github.linpeilie.AutoMapperConfig__393;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.domain.bo.AeaTaskTitleStatusBoToAeaTaskTitleStatusMapper;
import org.dromara.cyly.aea.domain.vo.AeaTaskTitleStatusVo;
import org.dromara.cyly.aea.domain.vo.AeaTaskTitleStatusVoToAeaTaskTitleStatusMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__393.class,
    uses = {AeaTaskTitleStatusVoToAeaTaskTitleStatusMapper.class,AeaTaskTitleStatusBoToAeaTaskTitleStatusMapper.class},
    imports = {}
)
public interface AeaTaskTitleStatusToAeaTaskTitleStatusVoMapper extends BaseMapper<AeaTaskTitleStatus, AeaTaskTitleStatusVo> {
}
