package org.dromara.cyly.aea.controller;

import lombok.RequiredArgsConstructor;
import org.dromara.common.core.constant.HttpStatus;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.BasicGroup;
import org.dromara.common.core.validate.HealthGroup;
import org.dromara.common.log.annotation.AppLog;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.satoken.annotation.SystemType;
import org.dromara.common.web.core.BaseController;
import org.dromara.cyly.aea.domain.bo.AeaElderInfoBo;
import org.dromara.cyly.aea.domain.vo.AeaElderInfoVo;
import org.dromara.cyly.aea.service.IAeaElderInfoService;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

/**
 * 老年人信息 控制器类
 *
 * <AUTHOR>
 * @date 2025-04-22
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/elderInfo")
public class AeaElderInfoController extends BaseController {

    private final IAeaElderInfoService aeaElderInfoService;

    /**
     * 检查老人信息是否完整
     *
     * @param taskId 老人ID
     * @return 检查结果信息
     */
    @SystemType("aea")
    @GetMapping("/usrapi/checkComplete/{taskId}")
    public R<Boolean> checkElderInfoComplete(@PathVariable("taskId") String taskId) {
        return aeaElderInfoService.checkElderInfoComplete(taskId);
    }

    /**
     * 提交老人基本信息
     *
     * @param bo 老人基本信息业务对象
     * @param bindingResult 绑定结果
     * @return 是否提交成功
     */
    @SystemType("aea")
//    @Log(title = "老人基本信息", businessType = BusinessType.INSERT)
    @PostMapping("/usrapi/submitBasicInfo")
    public R<Boolean> submitBasicInfo(@Validated(BasicGroup.class) @RequestBody AeaElderInfoBo bo, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            // 创建一个可修改的列表副本
            List<FieldError> fieldErrors = new ArrayList<>(bindingResult.getFieldErrors());

            // 获取实体类字段顺序
            Field[] fields = AeaElderInfoBo.class.getDeclaredFields();
            List<String> fieldOrder = Arrays.stream(fields)
                .map(Field::getName)
                .toList();

            // 对错误进行排序
            fieldErrors.sort(Comparator.comparingInt(error -> fieldOrder.indexOf(error.getField())));

            // 获取第一个错误信息
            String errorMessage = fieldErrors.getFirst().getDefaultMessage();

            // 返回单个错误信息
            return R.fail(errorMessage);
        }

        return aeaElderInfoService.saveBasicInfo(bo);
    }

    /**
     * 获取老人信息详情
     *
     * @param taskId 老人ID
     * @return 老人信息详情
     */
    @SystemType("aea")
    @PostMapping("/usrapi/getDetail/{taskId}")
    public R<AeaElderInfoVo> getElderInfoDetail(@PathVariable("taskId") String taskId) {
        return R.ok(aeaElderInfoService.getElderInfoDetail(taskId));
    }

    /**
     * 提交老人健康信息
     *
     * @param bo 老人健康信息业务对象
     * @param bindingResult 绑定结果
     * @return 是否提交成功
     */
    @SystemType("aea")
    @AppLog(title = "老人健康信息", businessType = BusinessType.INSERT)
    @PostMapping("/usrapi/submitHealthInfo")
    public R<Boolean> submitHealthInfo(@Validated(HealthGroup.class) @RequestBody AeaElderInfoBo bo, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            // 创建一个可修改的列表副本
            List<FieldError> fieldErrors = new ArrayList<>(bindingResult.getFieldErrors());

            // 获取实体类字段顺序
            Field[] fields = AeaElderInfoBo.class.getDeclaredFields();
            List<String> fieldOrder = Arrays.stream(fields)
                .map(Field::getName)
                .toList();

            // 对错误进行排序
            fieldErrors.sort(Comparator.comparingInt(error -> fieldOrder.indexOf(error.getField())));

            // 获取第一个错误信息
            String errorMessage = fieldErrors.getFirst().getDefaultMessage();

            // 返回单个错误信息
            return R.fail(HttpStatus.BAD_REQUEST, errorMessage);
        }

        return aeaElderInfoService.saveHealthInfo(bo);
    }

    /**
     * 同步老人信息到基础表
     *
     * @param elderId 老人ID
     * @return 同步结果信息
     */
    @SystemType("aea")
    @Log(title = "同步老人信息", businessType = BusinessType.OTHER)
    @GetMapping("/usrapi/syncToElderBase/{elderId}")
    public R<Boolean> syncToElderBase(@PathVariable("elderId") Long elderId) {
        return aeaElderInfoService.syncToElderBase(elderId);
    }
}
