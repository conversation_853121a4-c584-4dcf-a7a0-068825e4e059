package org.dromara.cyly.aea.enums;

import java.util.Arrays;
import java.util.Objects;

public interface ConvertibleEnum<T extends Enum<T>> {

    Integer getType();

    String getValue();

    static <E extends Enum<E> & ConvertibleEnum<E>> E convertByType(Class<E> enumClass, Integer type) {
        return Arrays.stream(enumClass.getEnumConstants())
                .filter(e -> Objects.equals(e.getType(), type))
                .findFirst()
                .orElseGet(() -> enumClass.getEnumConstants()[0]); // 或者抛出异常，取决于需求
    }

    static <E extends Enum<E> & ConvertibleEnum<E>> E convertByValue(Class<E> enumClass, String value) {
        return Arrays.stream(enumClass.getEnumConstants())
                .filter(e -> e.getValue().equals(value))
                .findFirst()
                .orElseGet(() -> enumClass.getEnumConstants()[0]); // 或者抛出异常，取决于需求
    }
}
