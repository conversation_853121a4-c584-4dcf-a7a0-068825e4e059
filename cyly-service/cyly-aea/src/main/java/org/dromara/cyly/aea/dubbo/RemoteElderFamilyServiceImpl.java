package org.dromara.cyly.aea.dubbo;

import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.dromara.aea.api.RemoteElderFamilyService;
import org.dromara.aea.api.domain.vo.RemoteElderFamilyVo;
import org.dromara.aea.api.domain.vo.RemoteElderVo;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.cyly.aea.domain.vo.AeaElderFamilyVo;
import org.dromara.cyly.aea.service.IAeaElderFamilyService;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteElderFamilyServiceImpl implements RemoteElderFamilyService {


    private final IAeaElderFamilyService familyService;

    @Override
    public RemoteElderFamilyVo getElderFamilyByNameAndIdCard(String name, String idCard) {
        AeaElderFamilyVo elderFamilyByNameAndIdCard = familyService.getElderFamilyByNameAndIdCard(name, idCard);
        return MapstructUtils.convert(elderFamilyByNameAndIdCard, RemoteElderFamilyVo.class);
    }
}
