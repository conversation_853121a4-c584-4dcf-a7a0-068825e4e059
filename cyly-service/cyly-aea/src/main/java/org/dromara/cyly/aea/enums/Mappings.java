package org.dromara.cyly.aea.enums;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

public class Mappings {
    private static final Map<String, String> scaleMap = new HashMap<>();

    static {
        scaleMap.put("6", "B.1.1");
        scaleMap.put("7", "B.1.2");
        scaleMap.put("8", "B.1.3");
        scaleMap.put("9", "B.1.4");
        scaleMap.put("10", "B.1.5");
        scaleMap.put("11", "B.1.6");
        scaleMap.put("12", "B.1.7");
        scaleMap.put("13", "B.1.8");
        scaleMap.put("14", "B.2.1");
        scaleMap.put("15", "B.2.2");
        scaleMap.put("16", "B.2.3");
        scaleMap.put("17", "B.2.4");
        scaleMap.put("18", "B.3.1");
        scaleMap.put("19", "B.3.2");
        scaleMap.put("20", "B.3.3");
        scaleMap.put("21", "B.3.4");
        scaleMap.put("22", "B.3.5");
        scaleMap.put("23", "B.3.6");
        scaleMap.put("24", "B.3.7");
        scaleMap.put("25", "B.3.8");
        scaleMap.put("26", "B.3.9");
        scaleMap.put("27", "B.4.1");
        scaleMap.put("28", "B.4.2");
        scaleMap.put("29", "B.4.3");
        scaleMap.put("30", "B.4.4");
        scaleMap.put("31", "B.4.5");
    }

    //表A.1
    private static final Map<String, String> evaInfomationMap = new HashMap<>();

    static {
        evaInfomationMap.put("1", "evaCode");
        evaInfomationMap.put("2", "evaBaseTime");
        evaInfomationMap.put("3", "reasonCode");
    }

    //表A.2
    private static final Map<String, String> evaObjectMap = new HashMap<>();

    static {
        evaObjectMap.put("9", "name");
        evaObjectMap.put("10", "gender");
        evaObjectMap.put("13", "birthday");
        evaObjectMap.put("14", "height");
        evaObjectMap.put("15", "weight");
        evaObjectMap.put("16", "ethnic");
        evaObjectMap.put("19", "religion");
        evaObjectMap.put("22", "idnumber");
        evaObjectMap.put("23", "education");
        evaObjectMap.put("30", "residence");
        evaObjectMap.put("39", "marriage");
        evaObjectMap.put("45", "medical");
        evaObjectMap.put("54", "income");
        evaObjectMap.put("62", "fall");
        evaObjectMap.put("67", "lost");
        evaObjectMap.put("72", "choking");
        evaObjectMap.put("77", "suicide");
        evaObjectMap.put("82", "other");
    }

    //表A.3
    private static final Map<String, String> relationMap = new HashMap<>();

    static {
        relationMap.put("87", "provideName");
        relationMap.put("88", "provideRelation");
        relationMap.put("96", "relationName");
        relationMap.put("97", "relationMobile");

    }

    //表A.4
    private static final Map<String, String> illnessrMap = new HashMap<>();

    static {
        illnessrMap.put("98", "illness");
    }

    //表A.5
    private static final Map<String, String> healthMap = new HashMap<>();

    static {
        healthMap.put("125", "pressure");
        healthMap.put("132", "joint");
        healthMap.put("136", "wound");
        healthMap.put("144", "specialCare");
        healthMap.put("153", "pain");
        healthMap.put("159", "tooth");
        healthMap.put("170", "falseTooth");
        healthMap.put("175", "swan");
        healthMap.put("182", "BMI");
        healthMap.put("185", "clean");
        healthMap.put("188", "disease");
        healthMap.put("191", "situation");
    }


    public static Map<String, String> getScaleMap() {
        return Collections.unmodifiableMap(scaleMap);
    }

    public static Map<String, String> getEvaInfomationMap() {
        return Collections.unmodifiableMap(evaInfomationMap);
    }

    public static Map<String, String> getEvaObjectMap() {
        return Collections.unmodifiableMap(evaObjectMap);
    }

    public static Map<String, String> getRelationMap() {
        return Collections.unmodifiableMap(relationMap);
    }

    public static Map<String, String> getIllnessrMap() {
        return Collections.unmodifiableMap(illnessrMap);
    }

    public static Map<String, String> getHealthMap() {
        return Collections.unmodifiableMap(healthMap);
    }
}
