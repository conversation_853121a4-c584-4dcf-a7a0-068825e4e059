package org.dromara.cyly.aea.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.cyly.aea.domain.bo.ClinicalAssessmentBo;
import org.dromara.cyly.aea.domain.vo.ClinicalAssessmentVo;

import java.util.Collection;

/**
 * <p>
 * 《InterRAI临床评估方案》 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
public interface IAeaClinicalAssessmentService {

    /**
     * 新增《InterRAI临床评估方案》
     *
     * @param bo 《InterRAI临床评估方案》
     * @return 是否新增成功
     */
    Boolean insertByBo(ClinicalAssessmentBo bo);

    /**
     * 修改《InterRAI临床评估方案》
     *
     * @param bo 《InterRAI临床评估方案》
     * @return 是否修改成功
     */
    Boolean updateByBo(ClinicalAssessmentBo bo);

    /**
     * 分页查询《InterRAI临床评估方案》列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 《InterRAI临床评估方案》分页列表
     */
    TableDataInfo<ClinicalAssessmentVo> queryPageList(ClinicalAssessmentBo bo, PageQuery pageQuery);

    /**
     * 详细查询《InterRAI临床评估方案》
     *
     * @param id 主键
     * @return 详细《InterRAI临床评估方案》
     */
    ClinicalAssessmentVo queryById(Long id);

    /**
     * 校验并批量删除《InterRAI临床评估方案》信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
