package org.dromara.cyly.aea.domain.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import org.dromara.common.core.validate.QueryGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import org.dromara.cyly.aea.domain.AeaTaskBase;

import java.util.List;

/**
 * 评估任务基础记录业务对象 aea_task_base
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AeaTaskBase.class, reverseConvertGenerate = false)
public class AeaTaskBaseBo extends BaseEntity {

    private Long id;
    /**
     * 任务id UUID
     */
    @NotNull(groups = {QueryGroup.class})
    private String taskId;

    /**
     * 目录id
     */
    private Long baseId;

    /**
     * 题目id
     */
    @NotNull(groups = {QueryGroup.class})
    private Long titleId;

    /**
     * 选项id
     */
    private Long optionId;

    /**
     * 填空内容
     */
    private String content;


    /**
     * 用药情况(目前长期服药情况)
     */
    @TableField(exist = false)
    private List<AeaTaskMedicineBo> taskMedicineList;

    /**
     * 长者id
     */
    @TableField(exist = false)
    private Long elderId;
    /**
     * 证件照片
     */
    private String idPhotos;
    /**
     * 近期免冠照
     */
    private String recentPhoto;
    /**
     * 服务照片列表
     */
    private String servePhotos;
}
