package org.dromara.cyly.aea.dubbo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.cyly.aea.domain.AeaElder;
import org.dromara.cyly.aea.domain.bo.AeaElderBo;
import org.dromara.cyly.aea.domain.vo.AeaElderVo;
import org.dromara.cyly.aea.service.IAeaElderService;
import org.dromara.aea.api.RemoteElderService;
import org.dromara.aea.api.domain.bo.RemoteElderBo;
import org.dromara.aea.api.domain.vo.RemoteElderVo;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 用户服务
 *
 * <AUTHOR> Li
 */
@RequiredArgsConstructor
@Service
@DubboService
public class RemoteElderServiceImpl implements RemoteElderService {

    private final IAeaElderService elderService;

    @Override
    public RemoteElderVo getElder(String id) {
        AeaElderVo aeaElderVo = elderService.queryById(Long.valueOf(id));
        return MapstructUtils.convert(aeaElderVo, RemoteElderVo.class);
    }

    @Override
    public List<RemoteElderVo> getElderList(RemoteElderBo bo) {
        AeaElderBo elderBo = MapstructUtils.convert(bo, AeaElderBo.class);
        List<AeaElderVo> list = elderService.queryList(elderBo);
        return MapstructUtils.convert(list, RemoteElderVo.class);
    }

    @Override
    public RemoteElderVo getElderByNameAndIdCard(String name, String idCard) {
        AeaElderVo aeaElderVo = elderService.getElderByNameAndIdCard(name,idCard);
        return MapstructUtils.convert(aeaElderVo, RemoteElderVo.class);
    }

    @Override
    public Map<String, Object> getElderTaskInfoById(String elderId) {
        return elderService.getElderTaskInfoById(elderId);
    }

    @Override
    public List<RemoteElderVo> selectList(Long id, String name) {
        List<AeaElderVo> aeaElders = elderService.selectList(new LambdaQueryWrapper<AeaElder>()
            .eq(id != null, AeaElder::getId, id)
            .eq(name != null, AeaElder::getName, name)
        );
        return MapstructUtils.convert(aeaElders, RemoteElderVo.class);
    }

    @Override
    public List<RemoteElderVo> selectListByIds(List<String> ids) {
        List<AeaElderVo> aeaElders = elderService.selectList(new LambdaQueryWrapper<AeaElder>()
            .in(AeaElder::getId, ids)
        );
        return MapstructUtils.convert(aeaElders, RemoteElderVo.class);
    }

    @Override
    public String selectElderNameById(Long id) {
        return elderService.queryById(id).getName();
    }
}
