package org.dromara.cyly.aea.dubbo;

import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.context.annotation.Lazy;
import org.dromara.aea.api.RemoteTaskTotalService;
import org.dromara.aea.api.domain.vo.RemoteTaskTotalVo;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.cyly.aea.domain.vo.AeaTaskTotalVo;
import org.dromara.cyly.aea.service.IAeaTaskTotalService;
import org.springframework.stereotype.Service;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;

@RequiredArgsConstructor
@Service
@DubboService
public class RemoteTaskTotalServiceImpl implements RemoteTaskTotalService {

    @Lazy
    private final IAeaTaskTotalService taskTotalService;

    @Override
    public CompletableFuture<RemoteTaskTotalVo> getTaskTotalAsync(String id) {
        return CompletableFuture.supplyAsync(() -> {
            AeaTaskTotalVo aeaTaskTotalVo = taskTotalService.queryById(id);
            return MapstructUtils.convert(aeaTaskTotalVo, RemoteTaskTotalVo.class);
        }, Executors.newVirtualThreadPerTaskExecutor());
    }

//    @Override
//    public RemoteTaskTotalVo getTaskTotal(String id) {
//        AeaTaskTotalVo aeaTaskTotalVo = taskTotalService.queryById(id);
//        return MapstructUtils.convert(aeaTaskTotalVo, RemoteTaskTotalVo.class);
//    }
}
