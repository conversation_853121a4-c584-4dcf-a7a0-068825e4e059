package org.dromara.cyly.aea.enums;

/**
 *
 * 评估类型
 */
public enum AssessmentTypeEnum implements ConvertibleEnum<AssessmentTypeEnum>{
    INITIAL_ASSESSMENT(1, "首次评估"),
    REGULAR_ASSESSMENT(2, "常规评估"),
    IMMEDIATE_ASSESSMENT(3, "即时评估"),
    REVIEW_ASSESSMENT(4, "因对评估结果有疑问进行复评"),
    EXIT_SERVICE_ASSESSMENT(5, "退出服务评估-包括服务最后三天"),
    FOLLOW_UP_ASSESSMENT(6, "退出服务跟进评估"),
    OTHER_ASSESSMENT(7, "其他-例如上诉、研究");

    private final Integer code;
    private final String description;

    AssessmentTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public String toString() {
        return getValue();
    }

    @Override
    public Integer getType() {
        return code;
    }

    @Override
    public String getValue() {
        return description;
    }
}
