package org.dromara.cyly.aea.dubbo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.dromara.cyly.aea.domain.AeaElder;
import org.dromara.cyly.aea.domain.AeaTaskAbilityLevel;
import org.dromara.cyly.aea.domain.AeaTaskTotal;
import org.dromara.cyly.aea.domain.vo.AeaElderVo;
import org.dromara.cyly.aea.domain.vo.AeaTaskAbilityLevelVo;
import org.dromara.cyly.aea.mapper.AeaTaskAbilityLevelMapper;
import org.dromara.cyly.aea.mapper.AeaTaskTotalMapper;
import org.dromara.cyly.aea.service.IAeaElderService;
import org.dromara.cyly.aea.service.IAeaTaskAbilityLevelService;
import org.dromara.cyly.aea.service.IAeaTaskTotalService;
import org.dromara.aea.api.RemoteEstimateStatisticsService;
import org.dromara.aea.api.domain.bo.LineBo;
import org.dromara.aea.api.domain.bo.ScoreTrendBo;
import org.dromara.system.api.RemoteAppUserService;
import org.dromara.system.api.domain.vo.RemoteAppUserVo;
import org.dromara.system.api.model.AppLoginUser;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@DubboService
@Slf4j
public class RemoteEstimateStatisticsServiceImpl implements RemoteEstimateStatisticsService {

    private final IAeaTaskTotalService taskTotalService;

    private final IAeaTaskAbilityLevelService taskAbilityLevelService;

    private final IAeaElderService elderService;

    private final AeaTaskTotalMapper taskTotalMapper;
    private final AeaTaskAbilityLevelMapper taskAbilityLevelMapper;
    @DubboReference(timeout = 600000)
    RemoteAppUserService remoteAppUserService;

    /**
     * 统计每日新增报告数量
     * @return 新增报告数量
     */
    @Override
    public long countDailyNewReports() {
        // 获取今天的时间
        LocalDate today = LocalDate.now();
        String startTime = today + " 00:00:00";
        String endTime = today + " 23:59:59";

        // 构建查询条件
        LambdaQueryWrapper<AeaTaskTotal> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNotNull(AeaTaskTotal::getReportUrl)
            .between(AeaTaskTotal::getCreateTime, startTime, endTime);

        // 返回符合条件的记录数
        return taskTotalMapper.selectCount(queryWrapper);
    }

    @Override
    public long countYearNewReports() {
        // 获取今年的起始时间和结束时间
        LocalDate now = LocalDate.now();
        int year = now.getYear();
        LocalDate startDate = LocalDate.of(year, 1, 1);
        LocalDate endDate = LocalDate.of(year, 12, 31);

        String startTime = startDate + " 00:00:00";
        String endTime = endDate + " 23:59:59";

        // 构建查询条件
        LambdaQueryWrapper<AeaTaskTotal> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNotNull(AeaTaskTotal::getReportUrl)
            .between(AeaTaskTotal::getCreateTime, startTime, endTime);

        // 返回符合条件的记录数
        return taskTotalMapper.selectCount(queryWrapper);
    }

    @Override
    public List<Map<String, Object>> getEvaluationCountStatistics(String elderOrgId) {
        if (StringUtils.isBlank(elderOrgId)) {
            return Collections.emptyList();
        }

        try {
            List<AppLoginUser> assessors = remoteAppUserService.getAppUserInfoByOrgIdAndSystemType(
                Long.valueOf(elderOrgId),
                "aea"
            );

            if (assessors == null || assessors.isEmpty()) {
                return Collections.emptyList();
            }

            List<Long> assessorIds = assessors.stream()
                .filter(Objects::nonNull)
                .map(AppLoginUser::getUserId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

            if (assessorIds.isEmpty()) {
                return Collections.emptyList();
            }

            // 修改查询逻辑，使用正确的统计方式
            List<Map<String, Object>> countResults = taskTotalMapper.selectMaps(
                Wrappers.<AeaTaskTotal>query()
                    .select("assessor_id, COUNT(1) as count")
                    .in("assessor_id", assessorIds)
                    .isNotNull("report_url")
                    .groupBy("assessor_id")
            );

            // 构建评估员ID到评估数量的映射
            Map<Long, Integer> assessorCountMap = new HashMap<>();
            for (Map<String, Object> result : countResults) {
                if (result != null && result.get("assessor_id") != null) {
                    Long assessorId = (Long) result.get("assessor_id");
                    Integer count = ((Number) result.get("count")).intValue();
                    assessorCountMap.put(assessorId, count);
                }
            }

            // 构建最终结果，按照原有格式返回
            return assessors.stream()
                .filter(Objects::nonNull)
                .map(assessor -> {
                    Map<String, Object> result = new LinkedHashMap<>();
                    String name = assessor.getNickname() != null ? assessor.getNickname() : "未知";
                    Integer count = assessorCountMap.getOrDefault(assessor.getUserId(), 0);
                    result.put("name", name);
                    result.put("count", count);
                    return result;
                })
                .collect(Collectors.toList());

        } catch (NumberFormatException e) {
            log.error("机构ID转换异常: {}", elderOrgId, e);
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("获取评估统计数据异常: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }


    @Override
    public Map<String, Object> getLevelStatistics() {
        List<AeaTaskAbilityLevelVo> taskAbilityLevels = taskAbilityLevelService.queryList();
        // 初始化Map并设置默认值为0
        Map<Integer, Integer> levelCountMap = new HashMap<>();
        for (int i = 1; i <= 5; i++) {
            levelCountMap.put(i, 0);
        }
        for (AeaTaskAbilityLevelVo taskAbilityLevel : taskAbilityLevels) {
            if (taskAbilityLevel.getFinalLevel() != null) {
                int finalLevel = Math.toIntExact(taskAbilityLevel.getFinalLevel());
                if (finalLevel >= 1 && finalLevel <= 5) {
                    levelCountMap.merge(Math.toIntExact(finalLevel), 1, Integer::sum);
                }
            }
        }
        // 计算总人数
        int totalCount = levelCountMap.values().stream().mapToInt(Integer::intValue).sum();
        int total = taskAbilityLevels.size();
        // 计算每个等级的占比
        Map<Integer, Object> levelPercentageMap = new HashMap<>();
        DecimalFormat df = new DecimalFormat("#.00");
        for (Map.Entry<Integer, Integer> entry : levelCountMap.entrySet()) {
            int level = entry.getKey();
            int count = entry.getValue();
            double percentage = totalCount > 0 ? (count / (double) totalCount) * 100 : 0.0;
            String formattedPercentage = df.format(percentage) + "%";
            levelPercentageMap.put(level, formattedPercentage);
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("levelCount", levelCountMap);
        resultMap.put("levelPercentage", levelPercentageMap);
        resultMap.put("total", total);
        return resultMap;
    }

    @Override
    public Map<String, Map<String, Object>> getTheOldStatistics() {
        Map<String,Map<String,Object>> map = new HashMap<>();
        DecimalFormat df = new DecimalFormat("0.00");
        // 按经济来源分
        List<Map<String, Object>> incomeByList = elderService.incoms();
        if (incomeByList != null && !incomeByList.isEmpty()) {
            Map<String, Object> incomeMap = incomeByList.get(0);
            BigDecimal totalIncome = BigDecimal.ZERO;
            for (Object value : incomeMap.values()) {
                if (value instanceof BigDecimal) {
                    totalIncome = totalIncome.add((BigDecimal) value);
                }
            }
            // 计算经济来源百分比
            Map<String, Object> IncomeByPercentages = new HashMap<>();
            for (Map.Entry<String, Object> entry : incomeMap.entrySet()) {
                String key = entry.getKey();
                BigDecimal value = (BigDecimal) entry.getValue();
                BigDecimal percentage = (totalIncome.compareTo(BigDecimal.ZERO) == 0) ? BigDecimal.ZERO
                    : value.multiply(BigDecimal.valueOf(100)).divide(totalIncome, 2, BigDecimal.ROUND_HALF_UP);
                IncomeByPercentages.put(key, percentage.toString() + "%");
            }
            map.put("incomeBy", incomeMap);
            map.put("incomeByPercentage", IncomeByPercentages);
        } else {
            map.put("incomeBy", null);
            map.put("incomeByPercentage", null);
        }
        //按老人特殊分类 1.特困 2.低保 3.低保边缘
        List<Map<String,Object>> specialType = elderService.allSpecialType();
        if (specialType != null){
            Map<String, Object> specialTypeMap = specialType.get(0);
            BigDecimal totalSpecialType = BigDecimal.ZERO;
            for (Object value : specialTypeMap.values()) {
                if (value instanceof BigDecimal) {
                    totalSpecialType = totalSpecialType.add((BigDecimal) value);
                }
            }
            Map<String, Object> specialTypePercentages = new HashMap<>();
            for (Map.Entry<String, Object> entry : specialTypeMap.entrySet()) {
                String key = entry.getKey();
                BigDecimal value = (BigDecimal) entry.getValue();
                BigDecimal percentage = (totalSpecialType.compareTo(BigDecimal.ZERO) == 0) ? BigDecimal.ZERO
                    : value.multiply(BigDecimal.valueOf(100)).divide(totalSpecialType, 2, BigDecimal.ROUND_HALF_UP);
                specialTypePercentages.put(key, percentage.toString() + "%");
            }
            map.put("specialType",specialTypeMap);
            map.put("specialTypePercentage",specialTypePercentages);
        }else {
            map.put("specialType",null);
            map.put("specialTypePercentage", null);
        }
        //按照老年人年龄5年一个刻度划分
        List<Map<String,Object>> age = elderService.allAge();
        if (age != null){
            Map<String, Object> ageMap = age.get(0);
            Long totalAge = 0L;
            for (Object value : ageMap.values()) {
                if (value instanceof Long) {
                    totalAge += (Long) value;
                }
            }
            Map<String, Object> agePercentages = new LinkedHashMap<>();
            // 保留两位小数
            for (Map.Entry<String, Object> entry : ageMap.entrySet()) {
                String key = entry.getKey();
                Long value = (Long) entry.getValue();
                double percentage = (totalAge == 0L) ? 0.0
                    : ((double) value * 100 / totalAge);
                String formattedPercentage = df.format(percentage) + "%";
                agePercentages.put(key, formattedPercentage);
            }
            String[] ageRanges = {"1", "2", "3", "4", "5", "6", "7", "8", "9"};
            Map<String, Object> genderPercentage = new LinkedHashMap<>();
            for (String prefix : ageRanges) {
                long maleCount = (long) ageMap.getOrDefault(prefix + "0", 0L);
                long femaleCount = (long) ageMap.getOrDefault(prefix + "1", 0L);
                long total = maleCount + femaleCount;

                if (total > 0) {
                    double malePercentage = (maleCount * 100.0) / total;
                    double femalePercentage = (femaleCount * 100.0) / total;
                    genderPercentage.put(prefix + "0", df.format(malePercentage) + "%");
                    genderPercentage.put(prefix + "1", df.format(femalePercentage) + "%");
                }
            }
            map.put("age",ageMap);
            map.put("agePercentage",agePercentages);
            map.put("genderPercentage",genderPercentage);
        }else {
            map.put("age",null);
            map.put("agePercentage",null);
            map.put("genderPercentage",null);
        }
        //按照老年人划分
        List<Map<String,Object>> liveWithWhoCode = elderService.liveWithWhoCode();
        if (liveWithWhoCode != null){
            Map<String, Object> liveWithWhoCodeMap = liveWithWhoCode.getFirst();

            long totalLiveWithWhoCode = 0L;
            for (Object value : liveWithWhoCodeMap.values()) {
                if (value instanceof Long) {
                    totalLiveWithWhoCode += (Long) value;
                }
            }
            Map<String, Object> LiveWithWhoCodePercentages = new LinkedHashMap<>();
            // 保留两位小数
            for (Map.Entry<String, Object> entry : liveWithWhoCodeMap.entrySet()) {
                String key = entry.getKey();
                Long value = (Long) entry.getValue();
                double percentage = (totalLiveWithWhoCode == 0L) ? 0.0
                    : ((double) value * 100 / totalLiveWithWhoCode);
                String formattedPercentage = df.format(percentage) + "%";
                LiveWithWhoCodePercentages.put(key, formattedPercentage);
            }
            map.put("liveWithWhoCode",liveWithWhoCodeMap);
            map.put("liveWithWhoCodePercentage",LiveWithWhoCodePercentages);
        }else {
            map.put("liveWithWhoCode",null);
            map.put("liveWithWhoCodePercentage", null);
        }
        return map;
    }

    @Override
    public List<Map<String, Object>> getRegionStatistics() {
        List<AeaElderVo> elders = elderService.selectList(new LambdaQueryWrapper<AeaElder>()
            .select(AeaElder::getId, AeaElder::getResidenceStreet));
        // 计算总老人数量
        int totalElders = elders.size();
        return elders.stream()
            .filter(elder -> elder.getResidenceStreet() != null && !elder.getResidenceStreet().isEmpty())
            .collect(Collectors.groupingBy(AeaElderVo::getResidenceStreet, Collectors.counting()))
            .entrySet().stream()
            .map(entry -> {
                Map<String, Object> map = new HashMap<>();
                map.put("name", entry.getKey());
                map.put("number", entry.getValue());
                double percentage = ((double) entry.getValue() / totalElders) * 100;
                map.put("percentage", String.format("%.2f%%", percentage));
                return map;
            }).collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> getRegionScoreTrend(ScoreTrendBo scoreTrendVo) {
        Map<String, Object> scoreTrend = new HashMap<>();
        List<String> residenceStreets = elderService.selectDistinctMarket();
        scoreTrend.put("residenceStreet", residenceStreets);
        String year = Optional.ofNullable(scoreTrendVo.getYear())
            .filter(StringUtils::isNotBlank)
            .orElse(String.valueOf(LocalDate.now().getYear()));
        String residenceStreet = scoreTrendVo.getResidenceStreet();
        Map<String, String> averageScoresByMonth = IntStream.rangeClosed(1, 12)
            .mapToObj(month -> {
                String monthString = String.format("%02d", month);
                String startDate = year + "-" + monthString + "-01";
                String endDate = year + "-" + monthString + "-31";
                List<AeaTaskTotal> taskTotals = taskTotalService.selectList(new LambdaQueryWrapper<AeaTaskTotal>()
                    .isNotNull(AeaTaskTotal::getReportUrl)
                    .isNotNull(AeaTaskTotal::getDate)
                    .ge(AeaTaskTotal::getDate, startDate)
                    .le(AeaTaskTotal::getDate, endDate)
                );
                Set<Long> elderIds = taskTotals.stream()
                    .map(AeaTaskTotal::getElderId)
                    .collect(Collectors.toSet());
                Set<String> taskTotalIds = taskTotals.stream()
                    .map(AeaTaskTotal::getId)
                    .collect(Collectors.toSet());
                Map<Long, AeaElder> elderMap = elderIds.isEmpty() ? Collections.emptyMap() :
                    elderService.selectBatchIds(elderIds).stream()
                        .filter(e -> residenceStreet.equals(e.getResidenceStreet()))
                        .collect(Collectors.toMap(AeaElder::getId, Function.identity()));
                Map<String, AeaTaskAbilityLevel> taskAbilityLevelMap = taskTotalIds.isEmpty() ? Collections.emptyMap() :
                    taskAbilityLevelMapper.selectList(new LambdaQueryWrapper<AeaTaskAbilityLevel>()
                            .in(AeaTaskAbilityLevel::getTaskId, taskTotalIds)
                        )
                        .stream()
                        .collect(Collectors.toMap(AeaTaskAbilityLevel::getTaskId, Function.identity()));
                long totalScore = taskTotals.stream()
                    .map(taskTotal -> Optional.ofNullable(elderMap.get(taskTotal.getElderId()))
                        .flatMap(elder -> Optional.ofNullable(taskAbilityLevelMap.get(taskTotal.getId())))
                        .map(AeaTaskAbilityLevel::getTotalScore)
                        .orElse(0L))  // 使用 0L 作为默认值
                    .reduce(0L, Long::sum);
                long count = taskTotals.stream()
                    .filter(taskTotal -> elderMap.containsKey(taskTotal.getElderId()))
                    .filter(taskTotal -> taskAbilityLevelMap.containsKey(taskTotal.getId()))
                    .count();
                String averageStr = count == 0 ? "0.00" : new DecimalFormat("#.00").format(totalScore / count);
                return new AbstractMap.SimpleEntry<>(monthString, averageStr);
            })
            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        scoreTrend.put("month", averageScoresByMonth);
        return scoreTrend;
    }

    @Override
    public List<Map<String, Object>> gethomepageStatistics() {
        // 获取当前年份的起始时间和结束时间
        java.util.Calendar calendar = java.util.Calendar.getInstance();
        int currentYear = calendar.get(java.util.Calendar.YEAR);

        calendar.set(currentYear, java.util.Calendar.APRIL, 1, 0, 0, 0);
        java.util.Date startDate = calendar.getTime();

        calendar.set(currentYear, java.util.Calendar.DECEMBER, 31, 23, 59, 59);
        java.util.Date endDate = calendar.getTime();

        // 1. 查询当年 TaskTotal 表中 elder_id 不为空且 report_url 不为空的 elderId 列表
        List<Long> elderIds = taskTotalService.selectList(new LambdaQueryWrapper<AeaTaskTotal>()
            .isNotNull(AeaTaskTotal::getElderId)
            .isNotNull(AeaTaskTotal::getReportUrl)
            .between(AeaTaskTotal::getCreateTime, startDate, endDate)
            .groupBy(AeaTaskTotal::getElderId)
            .select(AeaTaskTotal::getElderId))
            .stream()
            .map(AeaTaskTotal::getElderId)
            .distinct()
            .collect(Collectors.toList());

        // 如果 elderIds 为空，提前返回空列表
        if (elderIds.isEmpty()) {
            return Collections.emptyList();
        }

        // 2. 查询海南省当年所有老人的数量
        int totalCount = elderService.selectList(new LambdaQueryWrapper<AeaElder>()
            .like(AeaElder::getIdCardProvince, "海南省")
            .between(AeaElder::getCreateTime, startDate, endDate)
        ).size();

        // 3. 查询当年 elder 表中的数据
        List<AeaElderVo> elderList = elderService.selectList(new LambdaQueryWrapper<AeaElder>()
            .select(AeaElder::getIdCardDistrict, AeaElder::getIdCardCity)
            .in(AeaElder::getId, elderIds)
            .eq(AeaElder::getIdCardProvince, "海南省")
            .between(AeaElder::getCreateTime, startDate, endDate)
        );
        // 4. 统计 id_card_district 并存入 Map 集合
        Map<String, Long> districtCountMap = elderList.stream()
            .collect(Collectors.groupingBy(elder ->
                    elder.getIdCardDistrict().contains("区") ?
                        elder.getIdCardCity() :
                        elder.getIdCardDistrict(),
                Collectors.counting()));
        // 将 Map 转换为所需的 JSON 结构
        return districtCountMap.entrySet().stream()
            .map(entry -> {
                Map<String, Object> map = new HashMap<>();
                map.put("name", entry.getKey());
                map.put("number", entry.getValue());
                double proportion = (double) entry.getValue() / totalCount;
                map.put("proportion", String.format("%.2f%%", proportion * 100));
                return map;
            })
            .collect(Collectors.toList());
    }

    @Override
    public List<Map<String, Object>> getevaluatingInformation() {
        // 获取当前年份的起始时间和结束时间
        java.util.Calendar calendar = java.util.Calendar.getInstance();
        int currentYear = calendar.get(java.util.Calendar.YEAR);

        calendar.set(currentYear, java.util.Calendar.APRIL, 1, 0, 0, 0);
        java.util.Date startDate = calendar.getTime();

        calendar.set(currentYear, java.util.Calendar.DECEMBER, 31, 23, 59, 59);
        java.util.Date endDate = calendar.getTime();

        // 1. 获取当前年份的任务记录（包含所有字段）
        List<AeaTaskTotal> taskTotals = taskTotalMapper.getTaskTotalsByElderIdAndReportUrlAndYear(startDate, endDate);

        // 2. 提取 elder_id 和 assessor_id
        Set<Long> elderIds = taskTotals.stream()
            .map(AeaTaskTotal::getElderId)
            .collect(Collectors.toSet());
        List<Long> assessorIds = taskTotals.stream()
            .map(AeaTaskTotal::getAssessorId)
            .collect(Collectors.toList());

        // 3. 批量查询 elder 名称
        Map<Long, String> elderNameMap = elderService.selectList(new LambdaQueryWrapper<AeaElder>()
                .in(AeaElder::getId, elderIds))
            .stream()
            .collect(Collectors.toMap(AeaElderVo::getId, AeaElderVo::getName));


        // 4. 批量查询评估员名称
        Map<Long, String> assessorNameMap = remoteAppUserService.selectListByIds(assessorIds)
            .stream()
            .collect(Collectors.toMap(RemoteAppUserVo::getUserId, RemoteAppUserVo::getNickName));
        // 5. 组装结果
        return taskTotals.stream()
            .map(task -> {
                Map<String, Object> map = new HashMap<>();
                map.put("elderName", elderNameMap.get(task.getElderId()));
                map.put("assessorName", assessorNameMap.get(task.getAssessorId()));
                map.put("location", task.getLocation());
                map.put("reportUrl", task.getReportUrl());
                return map;
            })
            .collect(Collectors.toList());
    }

    @Override
    public List<Map<String, Object>> getEvaluationConutStatistics() {
        // 获取当前年份的起始时间和结束时间
        java.util.Calendar calendar = java.util.Calendar.getInstance();
        int currentYear = calendar.get(java.util.Calendar.YEAR);

        calendar.set(currentYear, Calendar.APRIL, 1, 0, 0, 0);
        java.util.Date startDate = calendar.getTime();

        calendar.set(currentYear, java.util.Calendar.DECEMBER, 31, 23, 59, 59);
        java.util.Date endDate = calendar.getTime();

        // 查询评估人数（不需要应用时间过滤）
        Integer assessorNum = remoteAppUserService.getAppUserInfoByOrgIdAndSystemType(null, "aea").size();

        // 查询当年已经完成评估数量（应用时间过滤）
        Integer totalAssessment = taskTotalService.selectList(new LambdaQueryWrapper<AeaTaskTotal>()
            .isNotNull(AeaTaskTotal::getReportUrl)
            .between(AeaTaskTotal::getCreateTime, startDate, endDate)
        ).size();

        // 查询当年正在进行中的评估数量（应用时间过滤）
        Integer totalAssessmentNum = taskTotalService.selectList(new LambdaQueryWrapper<AeaTaskTotal>()
            .eq(AeaTaskTotal::getEvalStatus, 1)
            .between(AeaTaskTotal::getCreateTime, startDate, endDate)
        ).size();

        // 查询老人数量（不需要应用时间过滤）
        Integer oldManNumber = elderService.selectList(new LambdaQueryWrapper<AeaElder>()
            .between(AeaElder::getCreateTime, startDate, endDate)
        ).size();

        // 查询当年所有评估任务数量（应用时间过滤）
        Integer allAssessNum = taskTotalService.selectList(new LambdaQueryWrapper<AeaTaskTotal>()
            .between(AeaTaskTotal::getCreateTime, startDate, endDate)
        ).size();

        // 待完成评估数量
        Integer completedAssessorNum = allAssessNum - totalAssessment;

        List<Map<String, Object>> resultList = new ArrayList<>();
        Map<String, Object> map = new LinkedHashMap<>();
        map.put("evaluated", totalAssessmentNum);
        map.put("totalAssessment", totalAssessment);
        map.put("oldManNumber", oldManNumber);
        map.put("completedAssessorNum", completedAssessorNum);
        map.put("assessorNum", assessorNum);
        resultList.add(map);
        return resultList;
    }

    @Override
    public List<Map<String, Object>> getTodayRealTimeEvaluationList() {
        // 获取今天的起始时间和结束时间
        java.util.Calendar calendar = java.util.Calendar.getInstance();
        int year = calendar.get(java.util.Calendar.YEAR);
        int month = calendar.get(java.util.Calendar.MONTH);
        int day = calendar.get(java.util.Calendar.DAY_OF_MONTH);

        calendar.set(year, month, day, 0, 0, 0);
        java.util.Date startDate = calendar.getTime();

        calendar.set(year, month, day, 23, 59, 59);
        java.util.Date endDate = calendar.getTime();

        // 1. 获取今天的任务记录（包含所有字段），按创建时间倒序排序
        List<AeaTaskTotal> taskTotals = taskTotalService.selectList(new LambdaQueryWrapper<AeaTaskTotal>()
                .isNotNull(AeaTaskTotal::getElderId)
            .isNotNull(AeaTaskTotal::getAssessorId)
            .isNotNull(AeaTaskTotal::getCreateTime)
            .isNotNull(AeaTaskTotal::getReportUrl)
            .between(AeaTaskTotal::getCreateTime, startDate, endDate)
            .orderByDesc(AeaTaskTotal::getCreateTime));

        // 如果没有数据，返回空列表
        if (taskTotals == null || taskTotals.isEmpty()) {
            return Collections.emptyList();
        }

        // 2. 提取 elder_id 和 assessor_id
        Set<Long> elderIds = taskTotals.stream()
            .map(AeaTaskTotal::getElderId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

        List<Long> assessorIds = taskTotals.stream()
            .map(AeaTaskTotal::getAssessorId)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        // 如果没有有效的老人ID或评估员ID，返回空列表
        if (elderIds.isEmpty() || assessorIds.isEmpty()) {
            return Collections.emptyList();
        }

        // 3. 批量查询 elder 名称
        Map<Long, String> elderNameMap = elderService.selectList(new LambdaQueryWrapper<AeaElder>()
                .in(AeaElder::getId, elderIds))
            .stream()
            .collect(Collectors.toMap(AeaElderVo::getId, AeaElderVo::getName, (k1, k2) -> k1));

        // 4. 批量查询评估员名称
        Map<Long, String> assessorNameMap = remoteAppUserService.selectListByIds(assessorIds)
            .stream()
            .collect(Collectors.toMap(RemoteAppUserVo::getUserId, RemoteAppUserVo::getNickName, (k1, k2) -> k1));

        // 5. 组装结果
        return taskTotals.stream()
            // 确保老人ID不为空且存在对应的名称
            .filter(task -> task.getElderId() != null && elderNameMap.containsKey(task.getElderId()))
            .map(task -> {
                Map<String, Object> map = new HashMap<>();
                map.put("elderName", elderNameMap.get(task.getElderId()));
                map.put("assessorName", task.getAssessorId() != null ? assessorNameMap.getOrDefault(task.getAssessorId(), "") : "");
                map.put("location", task.getLocation());
                map.put("reportUrl", task.getReportUrl());
                map.put("date", task.getDate());
                map.put("beginTime", task.getBeginTime());
                map.put("endTime", task.getEndTime());
                return map;
            })
            .collect(Collectors.toList());
    }

    @Override
    public List<Map<String, Object>> getYearRealTimeEvaluationList() {
        // 获取当前年份的起始时间和结束时间
        java.util.Calendar calendar = java.util.Calendar.getInstance();
        int currentYear = calendar.get(java.util.Calendar.YEAR);

        calendar.set(currentYear, Calendar.APRIL, 15, 0, 0, 0);
        java.util.Date startDate = calendar.getTime();

        calendar.set(currentYear, java.util.Calendar.DECEMBER, 31, 23, 59, 59);
        java.util.Date endDate = calendar.getTime();

        // 1. 获取当年的任务记录（包含所有字段），按创建时间倒序排序
        List<AeaTaskTotal> taskTotals = taskTotalService.selectList(new LambdaQueryWrapper<AeaTaskTotal>()
            .isNotNull(AeaTaskTotal::getElderId)
            .isNotNull(AeaTaskTotal::getAssessorId)
            .isNotNull(AeaTaskTotal::getCreateTime)
            .isNotNull(AeaTaskTotal::getReportUrl)
            .between(AeaTaskTotal::getCreateTime, startDate, endDate)
            .orderByDesc(AeaTaskTotal::getCreateTime));

        // 如果没有数据，返回空列表
        if (taskTotals == null || taskTotals.isEmpty()) {
            return Collections.emptyList();
        }

        // 2. 提取 elder_id 和 assessor_id
        Set<Long> elderIds = taskTotals.stream()
            .map(AeaTaskTotal::getElderId)
            .filter(Objects::nonNull)  // 过滤掉 null 值
            .collect(Collectors.toSet());

        List<Long> assessorIds = taskTotals.stream()
            .map(AeaTaskTotal::getAssessorId)
            .filter(Objects::nonNull)  // 过滤掉 null 值
            .collect(Collectors.toList());

        // 如果没有有效的老人ID或评估员ID，返回空列表
        if (elderIds.isEmpty() || assessorIds.isEmpty()) {
            return Collections.emptyList();
        }

        // 3. 批量查询 elder 名称
        Map<Long, String> elderNameMap = elderService.selectList(new LambdaQueryWrapper<AeaElder>()
                .in(AeaElder::getId, elderIds))
            .stream()
            .collect(Collectors.toMap(AeaElderVo::getId, AeaElderVo::getName, (k1, k2) -> k1));

        // 4. 批量查询评估员名称
        Map<Long, String> assessorNameMap = remoteAppUserService.selectListByIds(assessorIds)
            .stream()
            .collect(Collectors.toMap(RemoteAppUserVo::getUserId, RemoteAppUserVo::getNickName, (k1, k2) -> k1));

        // 5. 组装结果
        return taskTotals.stream()
            .filter(task -> task.getElderId() != null && elderNameMap.containsKey(task.getElderId()))  // 确保老人ID不为空且存在对应的名称
            .map(task -> {
                Map<String, Object> map = new HashMap<>();
                map.put("elderName", elderNameMap.get(task.getElderId()));
                map.put("assessorName", task.getAssessorId() != null ? assessorNameMap.getOrDefault(task.getAssessorId(), "") : "");
                map.put("location", task.getLocation());
                map.put("reportUrl", task.getReportUrl());
                map.put("date", task.getDate());
                map.put("beginTime", task.getBeginTime());
                map.put("endTime", task.getEndTime());
                return map;
            })
            .collect(Collectors.toList());
    }

    @Override
    public List<LineBo> getLineChartData() {
        // 获取最近10条任务总计数据，按日期降序排列
        List<AeaTaskTotal> taskTotals = taskTotalService.selectList(
            new LambdaQueryWrapper<AeaTaskTotal>()
                .isNotNull(AeaTaskTotal::getEndTime)
                .orderByDesc(AeaTaskTotal::getDate)
                .last("LIMIT 10")
        );

        List<LineBo> objList = new ArrayList<>();
        SimpleDateFormat inputFormat = new SimpleDateFormat("HH:mm:ss");

        for (AeaTaskTotal userInfo : taskTotals) {
            Long diffInMinutes = calculateTimeDifference(inputFormat, userInfo.getBeginTime(), userInfo.getEndTime());
            if (diffInMinutes == null) {
                // 如果计算时间差失败，跳过当前记录
                continue;
            }

            Long id = userInfo.getElderId() != null ? userInfo.getElderId() : null;
            AeaElderVo aeaElderVo = elderService.queryById(id);

            if (aeaElderVo == null || StringUtils.isBlank(aeaElderVo.getName())) {
                // 如果老人信息为空或名字为空，跳过当前记录
                continue;
            }

            LineBo lineBo = new LineBo();
            lineBo.setName(aeaElderVo.getName());
            lineBo.setDiffInMinutes(diffInMinutes);
            objList.add(lineBo);
        }

        return objList;
    }

    private Long calculateTimeDifference(SimpleDateFormat inputFormat, String begin, String end) {
        try {
            Date begindate = inputFormat.parse(begin);
            Date enddate = inputFormat.parse(end);
            long diffInMillis = enddate.getTime() - begindate.getTime();
            return diffInMillis / (1000 * 60); // 转换为分钟
        } catch (ParseException e) {
            return null;
        }
    }
}
