package org.dromara.cyly.aea.dubbo;

import lombok.RequiredArgsConstructor;
import org.apache.dubbo.config.annotation.DubboService;
import org.dromara.aea.api.RemoteTaskScaleService;
import org.dromara.aea.api.domain.vo.RemoteTaskScaleVo;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.cyly.aea.domain.vo.AeaTaskScaleVo;
import org.dromara.cyly.aea.service.IAeaTaskScaleService;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@DubboService
public class RemoteTaskScaleServiceImpl implements RemoteTaskScaleService {

    private final IAeaTaskScaleService taskScaleService;

    @Override
    public List<RemoteTaskScaleVo> selectList(String taskId, List<Long> dicGbNursingIds) {
        List<AeaTaskScaleVo> aeaTaskScaleVos = taskScaleService.selectListByScaleInfoId(taskId, dicGbNursingIds);
        return MapstructUtils.convert(aeaTaskScaleVos, RemoteTaskScaleVo.class);
    }
}
