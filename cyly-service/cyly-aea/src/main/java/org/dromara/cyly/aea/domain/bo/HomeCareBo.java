package org.dromara.cyly.aea.domain.bo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.cyly.aea.domain.AeaHomeCare;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
@AutoMapper(target = AeaHomeCare.class,reverseConvertGenerate = false)
public class HomeCareBo extends BaseEntity {
    /**
     * 家居照护评估id
     */
    @TableId(value = "id")
    @ExcelIgnore
    private Long id;

    /**
     * 所属书籍
     */
    @ExcelIgnore
    private Integer book;

    /**
     * 父id
     */
    @ExcelIgnore
    private Long parentId;

    //节号
    /**
     * 节号
     */
    @ExcelProperty("节号")
    @ColumnWidth(20)
    private String sectionNumber;

    /**
     * 题目
     */
    @ExcelProperty("题目")
    @ColumnWidth(20)
    private String topic;

    /**
     * 类型
     */
    @ExcelProperty("类型")
    @ColumnWidth(20)
    private String type;

    /**
     * 答案
     */
    @ExcelProperty("答案")
    @ColumnWidth(20)
    private String answer;

    /**
     * 目的
     */
    @ExcelProperty("目的")
    @ColumnWidth(20)
    private String goal;

    /**
     * 定义
     */
    @ExcelProperty("定义")
    @ColumnWidth(20)
    private String definition;

    /**
     * 程序
     */
    @ExcelProperty("程序")
    @ColumnWidth(20)
    private String procedureShow;

    /**
     * 编码
     */
    @ExcelProperty("编码")
    @ColumnWidth(20)
    private String coding;

    /**
     * 案例
     */
    @ExcelProperty("案例")
    @ColumnWidth(20)
    private String caseShow;

    /**
     * 封面地址
     */
    private String coverUrl;

    /**
     * 子项集合
     */
    @ExcelIgnore
    @TableField(exist = false)
    private List<AeaHomeCare> children;

    //删除标志（0默认存在 2删除）
    @TableLogic(value = "0",delval = "2")
    @ExcelIgnore
    private String delFlag;
}
