package org.dromara.cyly.aea.service.impl;

import cn.hutool.core.util.IdcardUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.dromara.common.core.constant.HttpStatus;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.exception.base.BaseException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.cyly.aea.domain.vo.*;
import org.springframework.context.annotation.Lazy;
import org.dromara.cyly.aea.domain.*;
import org.dromara.cyly.aea.domain.bo.AeaTaskBaseBo;
import org.dromara.cyly.aea.mapper.*;
import org.dromara.cyly.aea.service.CommonService;
import org.dromara.cyly.aea.service.IAeaTaskBaseService;
import org.dromara.system.api.RemoteAppUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.util.*;

/**
 * 评估任务基础记录Service业务层处理
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class AeaTaskBaseServiceImpl implements IAeaTaskBaseService {

    private final AeaTaskBaseMapper baseMapper;
    private final AeaGbEvaluateBaseInfoMapper gbEvaluateBaseInfoMapper;
    private final AeaTaskMedicineMapper taskMedicineMapper;
    private final AeaTaskTotalMapper taskTotalMapper;
    private final AeaTaskBaseMapper taskBaseMapper;
    private final AeaTaskTitleStatusMapper taskTitleStatusMapper;
    private final AeaElderMapper elderMapper;
    private final AeaElderFamilyMapper elderFamilyMapper;
    @Lazy
    private final CommonService commonService;
    @DubboReference
    RemoteAppUserService remoteAppUserService;

    @Override
    public List<AeaGbEvaluateBaseInfoVo> getBaseInfo(AeaTaskBaseBo bo) {
        log.info("开始执行getBaseInfo，任务ID: {}, 标题ID: {}", bo.getTaskId(), bo.getTitleId());
        if (bo.getTaskId() == null) {
            log.error("任务ID为空，参数校验失败");
            throw new RuntimeException("任务id不能为空");
        }

        AeaTaskTotal aeaTaskTotal = taskTotalMapper.selectById(bo.getTaskId());
        if (aeaTaskTotal == null) {
            log.warn("未找到taskId={}对应的任务总记录", bo.getTaskId());
        } else {
            if (aeaTaskTotal.getEvalStatus() == 0) {
                taskTotalMapper.update(
                    new LambdaUpdateWrapper<AeaTaskTotal>()
                        .eq(AeaTaskTotal::getId, bo.getTaskId())
                        .set(AeaTaskTotal::getEvalStatus, 1)
                );
            }
        }

        List<AeaGbEvaluateBaseInfoVo> gbEvaluateBaseInfoList = new ArrayList<>();
        // 获取基础信息列表
        List<AeaGbEvaluateBaseInfoVo> baseInfos = gbEvaluateBaseInfoMapper.selectVoList(
            new LambdaQueryWrapper<AeaGbEvaluateBaseInfo>()
                .eq(AeaGbEvaluateBaseInfo::getBaseId, bo.getTitleId())
        );
        log.debug("查询到基础信息记录数: {}", baseInfos.size());
        // 获取任务基础列表
        List<AeaTaskBase> taskBases = baseMapper.selectList(new LambdaQueryWrapper<AeaTaskBase>()
            .eq(AeaTaskBase::getId, bo.getTaskId())
        );
        if (taskBases.isEmpty()) {
            log.warn("未找到任务ID {} 对应的任务基础数据", bo.getTaskId());
        }
        // 创建一个Map用于存储顶层节点
        Map<Long, AeaGbEvaluateBaseInfoVo> baseInfoMap = new HashMap<>();
        // 处理基础信息
        baseInfos.forEach(baseInfo -> {
            if (aeaTaskTotal != null && aeaTaskTotal.getServePhotos() != null) {
                baseInfo.setServerPhotos(aeaTaskTotal.getServePhotos());
            }
            //1.从所有数据中找出第一层级 firstDept   我们需要返回的数据
            if (baseInfo.getPid() == null || baseInfo.getPid() == 0) {
                //数据回显
                if (!taskBases.isEmpty()) {
                    for (AeaTaskBase taskBase : taskBases) {
                        //子项id对应
                        if (taskBase.getTitleId().equals(baseInfo.getId())) {
                            //统一返回参数赋值
                            if (StringUtils.isBlank(taskBase.getContent()) && taskBase.getOptionId() != null) {
                                baseInfo.setContent(taskBase.getOptionId().toString());
                            } else {
                                baseInfo.setContent(taskBase.getContent());
                            }
                        }
                        baseInfoMap.put(baseInfo.getId(), baseInfo);
                    }
                } else {
                    baseInfoMap.put(baseInfo.getId(), baseInfo);
                }
                gbEvaluateBaseInfoList.add(baseInfo);//最顶层的数据
            } else {
                // 第二\三\四。。。。\N 层
                //2.只要不是第一层级的，找自己的上级，并将自己放入上级的children中
                AeaGbEvaluateBaseInfoVo parentDept = baseInfoMap.get(baseInfo.getPid());
                if (parentDept != null) {
                    parentDept.getChildren().add(baseInfo);
                }
            }
        });
        // 如果需要添加药品信息
        if (bo.getTitleId() == 4) {
            List<AeaTaskMedicineVo> taskMedicines = taskMedicineMapper.selectVoList(new LambdaQueryWrapper<AeaTaskMedicine>()
                .eq(AeaTaskMedicine::getId, bo.getId())
            );
            log.debug("加载到药品记录数: {}", taskMedicines.size());
            if (!taskMedicines.isEmpty()) {
                AeaGbEvaluateBaseInfoVo medicineInfo = new AeaGbEvaluateBaseInfoVo();
                medicineInfo.setMedicineList(taskMedicines);
                baseInfos.add(medicineInfo);
            }
        }
        return gbEvaluateBaseInfoList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> commit(List<AeaTaskBaseBo> taskBaseList) {
        log.info("任务提交列表：{}", taskBaseList);
        if (taskBaseList.isEmpty()) {
            return R.fail("任务列表不能为空");
        }
        // 用于检测任务ID为空的逻辑
        if (taskBaseList.stream().anyMatch(taskBase -> taskBase.getTaskId() == null)) {
            return R.fail("任务id不能为空");
        }
        if (taskTotalMapper.selectById(taskBaseList.getFirst().getTaskId()) == null) {
            return R.fail("该任务不存在");
        }
        AeaTaskTotal aeaTaskTotal = taskTotalMapper.selectById(taskBaseList.getFirst().getTaskId());
        for (AeaTaskBaseBo taskBase : taskBaseList) {
            //评估基准日期不可修改
            if (taskBase.getTitleId() != null && taskBase.getBaseId() != null
                && taskBase.getBaseId().equals(1L) && taskBase.getTitleId().equals(2L)) {
                continue;
            }

            if (taskBase.getServePhotos() != null) {
                aeaTaskTotal.setServePhotos(taskBase.getServePhotos());
                taskTotalMapper.updateById(aeaTaskTotal);
            }

            //药品列表不写入 基础目录表 taskBase
            if (taskBase.getTaskMedicineList() != null && !taskBase.getTaskMedicineList().isEmpty()) {
                continue;
            }

            try {
                List<AeaTaskBase> aeaTaskBases = taskBaseMapper.selectList(new LambdaQueryWrapper<AeaTaskBase>()
                    .eq(AeaTaskBase::getTaskId, taskBase.getTaskId())
                    .eq(AeaTaskBase::getBaseId, taskBase.getBaseId())
                    .eq(AeaTaskBase::getTitleId, taskBase.getTitleId())
                );
                taskBase.setTitleId(taskBase.getTitleId());

                if (!aeaTaskBases.isEmpty()) {
                    // 更新
                    LambdaQueryWrapper<AeaTaskBase> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(AeaTaskBase::getTitleId, taskBase.getTitleId())
                        .eq(AeaTaskBase::getBaseId, taskBase.getBaseId())
                        .eq(AeaTaskBase::getTaskId, taskBase.getTaskId());
                    taskBaseMapper.update(MapstructUtils.convert(taskBase, AeaTaskBase.class), queryWrapper);
                } else {
                    AeaTaskBase convert = MapstructUtils.convert(taskBase, AeaTaskBase.class);
                    taskBaseMapper.insert(convert);
                }
            } catch (Exception e) {
                log.error("数据库操作失败 {}", e.getMessage());
                throw new ServiceException("数据库操作失败");
            }
        }
        AeaTaskBaseBo taskBase = taskBaseList.get(1);
        if (taskBase.getTaskMedicineList() != null) {
            taskMedicineMapper.delete(new LambdaQueryWrapper<AeaTaskMedicine>()
                .eq(AeaTaskMedicine::getTaskId, taskBase.getTaskId())
            );
            taskBase.getTaskMedicineList().forEach(taskMedicine -> {
                AeaTaskMedicine taskMedicine1 = new AeaTaskMedicine();
                taskMedicine1.setTaskId(taskBase.getTaskId());
                taskMedicine1.setMedicineName(taskMedicine.getMedicineName());
                taskMedicine1.setMedicineMethod(taskMedicine.getMedicineMethod());
                taskMedicine1.setMedicineDosage(taskMedicine.getMedicineDosage());
                taskMedicine1.setMedicineFrequency(taskMedicine.getMedicineFrequency());
                try {
                    taskMedicineMapper.insert(taskMedicine1);
                } catch (Exception e) {
                    log.error("基础目录信息 插入taskMedicineMapper 提交失败 ∑(°△°‖){}", e.getMessage());
                    throw new ServiceException("基础目录信息 插入taskMedicineMapper 提交失败", HttpStatus.BAD_REQUEST);
                }
            });
        }
        // 更新提交状态表
        AeaTaskTitleStatus aeaTaskTitleStatus = taskTitleStatusMapper.selectOne(new LambdaQueryWrapper<AeaTaskTitleStatus>()
            .eq(AeaTaskTitleStatus::getTitleId, taskBaseList.get(0).getBaseId())
            .eq(AeaTaskTitleStatus::getTaskId, taskBaseList.get(0).getTaskId()));
        if (aeaTaskTitleStatus == null) {
            aeaTaskTitleStatus = new AeaTaskTitleStatus();
            aeaTaskTitleStatus.setTaskId(taskBaseList.getFirst().getTaskId());
            aeaTaskTitleStatus.setTitleId(taskBaseList.getFirst().getBaseId());
            // 1 已提交
            aeaTaskTitleStatus.setStatus("1");
            try {
                taskTitleStatusMapper.insert(aeaTaskTitleStatus);
            } catch (Exception e) {
                log.error("题目状态 插入taskTitleStatus 提交失败 ∑(°△°‖){}", e.getMessage());
                throw new ServiceException("题目状态 插入taskTitleStatus 提交失败 ∑(°△°‖)", HttpStatus.BAD_REQUEST);
            }
        }
        //elder表维护
        //约定列表第一项传老人Id
        AeaTaskBaseBo taskBase1 = taskBaseList.getFirst();
        if (taskBase1.getBaseId() != null && taskBase1.getBaseId() == 2) {
            log.info("长者表维护 提交开始");
            try {
                saveElder(taskBase1);
            } catch (Exception e) {
                log.error("长者表维护 提交失败 ∑(°△°‖){}", e.getMessage());
                throw new ServiceException("长者表维护 提交失败 ∑(°△°‖)" + e.getMessage(), HttpStatus.BAD_REQUEST);
            }
        }
        //评估原因维护
        if (taskBase.getBaseId() != null && taskBase.getBaseId() == 1) {
            log.info("任务总表更新 评估原因 提交开始");
            try {
                saveReason(taskBaseList.get(1));
            } catch (Exception e) {
                log.error("任务总表更新 评估原因 提交失败 ∑(°△°‖){}", e.getMessage());
                throw new ServiceException("任务总表更新 评估原因 提交失败 ∑(°△°‖)" + e.getMessage(), HttpStatus.BAD_REQUEST);
            }
        }

        // 联系人信息维护
        // 检查是否有baseId为3的任务基础数据（信息提供者与联系人基本信息表）
        boolean hasContactInfo = taskBaseList.stream()
            .anyMatch(item -> item.getBaseId() != null && item.getBaseId() == 3);

        if (hasContactInfo) {
            log.info("联系人信息提交开始");
            try {
                // 获取任务ID
                String taskId = taskBaseList.getFirst().getTaskId();

                // 从列表中提取信息提供者姓名和电话
                String relativeName = null;
                String relativePhone = null;

                for (AeaTaskBaseBo item : taskBaseList) {
                    if (item.getTitleId() != null) {
                        // 信息提供者姓名
                        if (item.getTitleId() == 96) {
                            relativeName = item.getContent();
                            // 信息提供者电话
                        } else if (item.getTitleId() == 97) {
                            relativePhone = item.getContent();
                        }
                    }
                }

                // 如果找到了姓名和电话，直接处理联系人信息
                if (relativeName != null && relativePhone != null) {
                    // 获取任务关联的老人ID
                    AeaTaskTotal taskTotal = taskTotalMapper.selectById(taskId);
                    if (taskTotal != null && taskTotal.getElderId() != null) {
                        Long elderId = taskTotal.getElderId();

                        // 更新老人基本信息表中的亲属信息
                        AeaElder elder = elderMapper.selectById(elderId);
                        if (elder != null) {
                            elder.setRelativeName(relativeName);
                            elder.setRelativePhone(relativePhone);
                            elderMapper.updateById(elder);

                            // 处理亲属表信息
                            // 查询该老人已有的亲属信息
                            List<AeaElderFamily> existingFamilies = elderFamilyMapper.selectList(
                                new LambdaQueryWrapper<AeaElderFamily>()
                                    .eq(AeaElderFamily::getPersonId, elderId)
                            );

                            boolean found = false;

                            // 查找是否已存在该亲属（根据姓名匹配）
                            for (AeaElderFamily existingFamily : existingFamilies) {
                                if (existingFamily.getKinName() != null &&
                                    existingFamily.getKinName().equals(relativeName)) {
                                    // 找到匹配的亲属，更新电话号码
                                    existingFamily.setKinPhone(relativePhone);
                                    elderFamilyMapper.updateById(existingFamily);
                                    found = true;
                                    break;
                                }
                            }

                            // 如果没找到匹配的亲属，则新增
                            if (!found) {
                                AeaElderFamily newFamily = new AeaElderFamily();
                                newFamily.setPersonId(elderId);
                                newFamily.setKinName(relativeName);
                                newFamily.setKinPhone(relativePhone);
                                elderFamilyMapper.insert(newFamily);
                            }

                            log.info("联系人信息提交成功，任务ID: {}, 老人ID: {}, 联系人: {}, 电话: {}",
                                taskId, elderId, relativeName, relativePhone);
                        }
                    }
                } else {
                    // 如果没有直接找到姓名和电话，使用原来的方法
                    AeaTaskBaseBo contactTaskBase = new AeaTaskBaseBo();
                    contactTaskBase.setTaskId(taskId);
                    saveContact(contactTaskBase);
                }
            } catch (Exception e) {
                log.error("联系人提交维护失败: {}", e.getMessage());
                throw new ServiceException("联系人提交维护失败: " + e.getMessage(), HttpStatus.BAD_REQUEST);
            }
        }
        return R.ok(true);
    }

    @Override
    public KinInfoVo getKinInfo(String taskId) {
        // 创建返回对象
        KinInfoVo kinInfoVo = new KinInfoVo();

        // 先从任务基础表中查询信息提供者信息和联系人信息
        List<AeaTaskBase> aeaTaskBases = taskBaseMapper.selectList(new LambdaQueryWrapper<AeaTaskBase>()
            .eq(AeaTaskBase::getTaskId, taskId)
            .eq(AeaTaskBase::getBaseId, 3)
            .in(AeaTaskBase::getTitleId, Arrays.asList(87L, 88L, 96L, 97L))
        );

        // 从任务基础表中提取信息
        // 信息提供者姓名 (titleId=87)
        String infoProviderName = null;
        String kinName = null;
        String kinPhone = null;

        for (AeaTaskBase taskBase : aeaTaskBases) {
            if (taskBase.getTitleId() != null) {
                switch (taskBase.getTitleId().intValue()) {
                    // 信息提供者姓名
                    case 87:
                        infoProviderName = taskBase.getContent();
                        break;
                    // 联系人姓名
                    case 96:
                        kinName = taskBase.getContent();
                        break;
                    // 联系人电话
                    case 97:
                        kinPhone = taskBase.getContent();
                        break;
                }
            }
        }

        // 如果从任务基础表中找到了联系人信息
        boolean foundContactInfo = false;
        if (kinName != null || kinPhone != null) {
            kinInfoVo.setKinName(kinName);
            kinInfoVo.setKinPhone(kinPhone);
            foundContactInfo = true;
        }

        // 如果从任务基础表中找到了信息提供者姓名
        if (infoProviderName != null) {
            kinInfoVo.setInfoProvider(infoProviderName);
            // 如果找到了联系人信息和信息提供者姓名，可以直接返回
            if (foundContactInfo) {
                return kinInfoVo;
            }
        }

        // 如果任务基础表中没有找到完整信息，则从老人表中查询补充
        AeaTaskTotalVo aeaTaskTotalVo = taskTotalMapper.selectVoById(taskId);
        if (aeaTaskTotalVo != null && aeaTaskTotalVo.getElderId() != null) {
            AeaElderVo aeaElderVo = elderMapper.selectVoById(aeaTaskTotalVo.getElderId());
            if (aeaElderVo != null) {
                // 如果没有找到联系人信息，从老人表中获取
                if (!foundContactInfo) {
                    kinInfoVo.setKinName(aeaElderVo.getRelativeName());
                    kinInfoVo.setKinPhone(aeaElderVo.getRelativePhone());
                }

                // 如果没有找到信息提供者姓名，从老人表中获取
                if (infoProviderName == null) {
                    kinInfoVo.setInfoProvider(aeaElderVo.getName());
                }

                return kinInfoVo;
            }
        }

        // 如果没有找到任何信息，返回null
        return kinInfoVo.getKinName() != null || kinInfoVo.getKinPhone() != null || kinInfoVo.getInfoProvider() != null ?
            kinInfoVo : null;
    }

    /**
     * 长者信息维护方法，执行长者数据的新增或更新操作，并同步关联任务总表信息。
     * 通过任务ID关联的表单数据构建长者对象，处理基础信息持久化及关联关系维护。
     *
     * @param taskBaseDto 任务基础数据传输对象，包含任务ID、长者ID、证件照等核心字段
     *                    需包含完整的表单字段映射关系（titleId->content）
     */
    public void saveElder(AeaTaskBaseBo taskBaseDto) {
        // 创建长者实体对象
        AeaElder elder = new AeaElder();

        // 根据任务ID和baseId=2查询任务基础数据列表
        // 该数据集合包含当前任务关联的所有长者基础信息表单字段
        List<AeaTaskBase> aeaTaskBases = taskBaseMapper.selectList(new LambdaQueryWrapper<AeaTaskBase>()
            .eq(AeaTaskBase::getTaskId, taskBaseDto.getTaskId())
            .eq(AeaTaskBase::getBaseId, 2)
        );

        // 遍历任务基础数据集合，按titleId映射到长者实体属性
        // 通过switch-case实现字段类型路由，完成表单数据到实体对象的转换
        aeaTaskBases.forEach(taskBase -> {
            switch (Integer.parseInt(String.valueOf(taskBase.getTitleId()))) {
                case 9:     // 姓名
                    elder.setName(taskBase.getContent());
                    break;
                case 10:    //性别
                    elder.setGender((long) (StringUtils.equals(taskBase.getContent(), "11") ? 1 : 0));
                    break;
                case 13:    //出生日期
                    elder.setBirthday(stringToDate(taskBase.getContent()));
                    break;
                case 14:
                    //身高
                    String height = taskBase.getContent();
                    if (height != null && !height.matches("^(1[0-9]{2}|2[0-4][0-9]|250(\\.0)?)(\\.\\d{1,2})?$")) {
                        throw new IllegalArgumentException("身高必须在100-250cm之间，支持两位小数");
                    }
                    elder.setHeight(height);
                    break;
                case 15:    //体重
                    String weight = taskBase.getContent();
                    if (weight != null && !weight.matches("^(3[0-9]|([4-9][0-9]|1[0-9]{2}|2[0-9]{2}|300)(\\.\\d{1,2})?)$")) {
                        throw new IllegalArgumentException("体重必须在30-300kg之间，支持两位小数");
                    }
                    elder.setWeight(weight);
                    break;
                case 16:    //民族
                    elder.setEthnic(taskBase.getContent());
                    break;
                case 19:    //宗教信仰
                    elder.setReligiousBelief(taskBase.getContent());
                    break;
                case 22:    //公民身份证号码
                    String idCard = taskBase.getContent();
                    boolean validCard = IdcardUtil.isValidCard(idCard);
                    if (!validCard) {
                        throw new IllegalArgumentException("身份证号码格式不正确");
                    }
                    elder.setIdCardNumber(idCard);
                    break;
                case 23:    //文化程度id
                    elder.setEducationCode(taskBase.getContent());
                    //文化程度名称
                    elder.setEducationName(getContent(taskBase.getContent()));
                    break;
                case 30:    //居住情况
                    elder.setLiveWithWhoCode(taskBase.getContent());
                case 39:    //婚姻状况
                    elder.setMaritalCode(taskBase.getContent());
                    break;
                case 45:    //支付方式
                    elder.setPaymentWay(taskBase.getContent());
                    break;
                case 54:    //经济来源
                    elder.setIncomeBy(taskBase.getContent());
                    break;
            }
        });

        // 计算当前年龄（基于出生日期）
        long age = Period.between(dateToLocalDate(elder.getBirthday()), LocalDate.now()).getYears();
        if (age < 40 || age > 150) {
            throw new IllegalArgumentException("年龄必须在40-150岁之间");
        }
        elder.setAge(age);
        // 设置证件照和近期照片信息
        elder.setIdPhotos(taskBaseDto.getIdPhotos());
        elder.setRecentPhoto(taskBaseDto.getRecentPhoto());

        // 查询关联任务总表的长者ID
        Long elderId = null;
        AeaTaskTotal ageTaskTotalSearch = taskTotalMapper.selectById(taskBaseDto.getTaskId());
        if (ageTaskTotalSearch != null){
            elderId = ageTaskTotalSearch.getElderId();
        }

        // 长者信息持久化操作：更新或新增分支处理
        // 优先使用传入的elderId，其次使用查询到的关联ID
//        if (taskBaseDto.getElderId() != null || elderId != null) {
//            log.info("长者表维护 更新elderMapper 提交开始");
//
//            // 判断两个id不为空 取非空的那个
//            if (taskBaseDto.getElderId() != null && taskBaseDto.getElderId() != 0) {
//                elder.setId(taskBaseDto.getElderId());
//            } else if (elderId != null) {
//                elder.setId(elderId);
//            }
//            try {
//                elderMapper.updateById(elder);
//            } catch (Exception e) {
//                log.error("长者表维护 更新elderMapper 提交失败 ∑(°△°‖){}", e.getMessage());
//            }
//        } else {
//            log.info("长者表维护 新增elderMapper 提交开始");
//            try {
//                elderMapper.insert(elder);
//            } catch (Exception e) {
//                log.error("长者表维护 插入elderMapper 提交失败 ∑(°△°‖){}", e.getMessage());
//            }
//        }
        try {
            log.info("长者表维护 新增或更新elderMapper 提交开始");
            elder.setId(taskBaseDto.getElderId() != null ? taskBaseDto.getElderId() : elderId);
            elderMapper.insertOrUpdate(elder);
        }catch (Exception e){
            throw new BaseException("长者表维护 新增或更新elderMapper 提交失败 ∑(°△°‖){}");
        }

        // 任务总表关联信息同步处理
        // 通过身份证号+姓名双重校验确保长者记录唯一性
        try {
            LambdaQueryWrapper<AeaElder> queryWrapper = new LambdaQueryWrapper<AeaElder>()
                .eq(AeaElder::getIdCardNumber, elder.getIdCardNumber())
                .eq(StringUtils.isNotBlank(elder.getName()), AeaElder::getName, elder.getName());

            List<AeaElder> elderList = elderMapper.selectList(queryWrapper);
            AeaElder aeaElder = null;

            // 主分支：存在匹配的长者记录
            if (elderList != null && !elderList.isEmpty()) {
                aeaElder = elderList.getFirst();
                if (elder.getId() == null) {
                    elder.setId(aeaElder.getId());
                    elderMapper.updateById(elder);
                    log.info("根据身份证号和姓名找到已存在长者记录，已更新长者信息，ID: {}", aeaElder.getId());
                }
                taskTotalMapper.update(new LambdaUpdateWrapper<AeaTaskTotal>()
                    .eq(AeaTaskTotal::getId, taskBaseDto.getTaskId())
                    .set(AeaTaskTotal::getElderId, elder.getId())
                );
            }
            // 分支2：无匹配记录但存在ID（更新场景）
            else if (elder.getId() != null) {
                aeaElder = elderMapper.selectById(elder.getId());
                if (aeaElder != null) {
                    taskTotalMapper.update(new LambdaUpdateWrapper<AeaTaskTotal>()
                        .eq(AeaTaskTotal::getId, taskBaseDto.getTaskId())
                        .set(AeaTaskTotal::getElderId, elder.getId())
                    );
                }
            }

            // 实时通知模块：发送评估动态消息
            // 获取任务总表信息并构造通知参数
            AeaTaskTotal aeaTaskTotal = taskTotalMapper.selectById(taskBaseDto.getTaskId());
            String assessName = remoteAppUserService.selectNickNameById(aeaTaskTotal.getAssessorId());
            String assessNameDeputy = remoteAppUserService.selectNickNameById(aeaTaskTotal.getAssessorIdDeputy());
            String elderName = (aeaElder != null) ? aeaElder.getName() : elder.getName();
            Map<String, Object> params = new HashMap<>();
            params.put("assessName", assessName);
            params.put("assessNameDeputy", assessNameDeputy);
            params.put("elderName", elderName);
            params.put("status", "评估中");
            commonService.sendRealTimeAssessDynamic(JSON.toJSONString(params));

        } catch (Exception e) {
            log.error("elderId 更新taskTotalMapper 失败 ∑(°△°‖){}", e.getMessage());
            throw new BaseException("elderId 提交失败 ∑(°△°‖){}");
        }

    }

    @Transactional
    public void saveContact(AeaTaskBaseBo taskBaseDto) {
        if (taskBaseDto.getTaskId() == null) {
            throw new ServiceException("任务id不能为空");
        }
        AeaTaskTotal aeaTaskTotal1 = taskTotalMapper.selectById(taskBaseDto.getTaskId());
        if (aeaTaskTotal1 == null) {
            throw new ServiceException("任务不存在");
        }
        if (aeaTaskTotal1.getElderId() == null) {
            throw new ServiceException("任务中不存在老人");
        }
        AeaElder aeaElder = elderMapper.selectById(aeaTaskTotal1.getElderId());
        if (aeaElder == null) {
            throw new ServiceException("老人信息不存在");
        }

        // 查询信息提供者姓名和电话
        String relativeName = null;
        String relativePhone = null;
        // 只查询信息提供者姓名和电话
        List<AeaTaskBase> aeaTaskBases = taskBaseMapper.selectList(new LambdaQueryWrapper<AeaTaskBase>()
            .eq(AeaTaskBase::getTaskId, taskBaseDto.getTaskId())
            .eq(AeaTaskBase::getBaseId, 2)
            .in(AeaTaskBase::getTitleId, Arrays.asList(96L, 97L))
        );

        // 提取姓名和电话
        for (AeaTaskBase taskBase : aeaTaskBases) {
            if (taskBase.getTitleId() != null) {
                switch (taskBase.getTitleId().intValue()) {
                    // 信息提供者姓名
                    case 96:
                        relativeName = taskBase.getContent();
                        break;
                    // 信息提供者电话
                    case 97:
                        relativePhone = taskBase.getContent();
                        break;
                }
            }
        }

        // 更新老人基本信息表中的亲属信息
        if (relativeName != null || relativePhone != null) {
            if (relativeName != null) {
                aeaElder.setRelativeName(relativeName);
            }
            if (relativePhone != null) {
                aeaElder.setRelativePhone(relativePhone);
            }
            elderMapper.updateById(aeaElder);

            // 处理亲属表信息
            if (relativeName != null && relativePhone != null) {
                // 查询该老人已有的亲属信息
                List<AeaElderFamily> existingFamilies = elderFamilyMapper.selectList(
                    new LambdaQueryWrapper<AeaElderFamily>()
                        .eq(AeaElderFamily::getPersonId, aeaElder.getId())
                );

                boolean found = false;

                // 查找是否已存在该亲属（根据姓名匹配）
                for (AeaElderFamily existingFamily : existingFamilies) {
                    if (existingFamily.getKinName() != null &&
                        existingFamily.getKinName().equals(relativeName)) {
                        // 找到匹配的亲属，更新电话号码
                        existingFamily.setKinPhone(relativePhone);
                        elderFamilyMapper.updateById(existingFamily);
                        found = true;
                        break;
                    }
                }

                // 如果没找到匹配的亲属，则新增
                if (!found) {
                    AeaElderFamily newFamily = new AeaElderFamily();
                    newFamily.setPersonId(aeaElder.getId());
                    newFamily.setKinName(relativeName);
                    newFamily.setKinPhone(relativePhone);
                    elderFamilyMapper.insert(newFamily);
                }
            }
        }
    }

    /**
     * 评估原因维护
     *
     * @param taskBase 入参
     */
    public void saveReason(AeaTaskBaseBo taskBase) {
        String content = taskBase.getContent();
        if (Long.valueOf(3).equals(taskBase.getTitleId())) {
            log.info("评估原因维护 提交开始");
            if ("5".equals(content)) {
                content = "7";
            }
            //更新 total表 评估原因字段
            taskTotalMapper.update(new LambdaUpdateWrapper<AeaTaskTotal>()
                .eq(AeaTaskTotal::getId, taskBase.getTaskId())
                .set(AeaTaskTotal::getReasonCode, content)
            );
        }
    }

    private String getContent(String educationCode){
        return switch (educationCode) {
            case "24" -> "文盲";
            case "25" -> "小学";
            case "26" -> "初中";
            case "27" -> "高中/技校/中专";
            case "28" -> "大学专科及以上";
            case "29" -> "不详";
            default -> "未知";
        };
    }

    /**
     * 查询评估任务基础记录
     *
     * @param id 主键
     * @return 评估任务基础记录
     */
    @Override
    public AeaTaskBaseVo queryById(String id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询评估任务基础记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 评估任务基础记录分页列表
     */
    @Override
    public TableDataInfo<AeaTaskBaseVo> queryPageList(AeaTaskBaseBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AeaTaskBase> lqw = buildQueryWrapper(bo);
        Page<AeaTaskBaseVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的评估任务基础记录列表
     *
     * @param bo 查询条件
     * @return 评估任务基础记录列表
     */
    @Override
    public List<AeaTaskBaseVo> queryList(AeaTaskBaseBo bo) {
        LambdaQueryWrapper<AeaTaskBase> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AeaTaskBase> buildQueryWrapper(AeaTaskBaseBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AeaTaskBase> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getBaseId() != null, AeaTaskBase::getBaseId, bo.getBaseId());
        lqw.eq(bo.getOptionId() != null, AeaTaskBase::getOptionId, bo.getOptionId());
        lqw.eq(StringUtils.isNotBlank(bo.getContent()), AeaTaskBase::getContent, bo.getContent());
        return lqw;
    }

    /**
     * 新增评估任务基础记录
     *
     * @param bo 评估任务基础记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(AeaTaskBaseBo bo) {
        AeaTaskBase add = MapstructUtils.convert(bo, AeaTaskBase.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            if (add != null) {
                bo.setId(add.getId());
            }
        }
        return flag;
    }

    /**
     * 修改评估任务基础记录
     *
     * @param bo 评估任务基础记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(AeaTaskBaseBo bo) {
        AeaTaskBase update = MapstructUtils.convert(bo, AeaTaskBase.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AeaTaskBase entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除评估任务基础记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        return baseMapper.deleteByIds(ids) > 0;
    }

    private LocalDate dateToLocalDate(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    public static Date stringToDate(String dateString) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");

        try {
            return formatter.parse(dateString);
        } catch (ParseException e) {
            // 处理解析错误
            System.err.println("Invalid date format: " + dateString);
            return null;
        }
    }
}
