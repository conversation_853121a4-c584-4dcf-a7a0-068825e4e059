package org.dromara.cyly.aea.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.dromara.cyly.aea.domain.AeaElder;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.util.Date;
import java.util.List;

/**
 * 长者用户业务对象 aea_elder
 *
 * <AUTHOR>
 * @date 2025-01-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AeaElder.class, reverseConvertGenerate = false)
public class AeaElderBo extends BaseEntity {

    /**
     * 长者id
     */
    @NotNull(message = "长者id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 机构ID
     */
    @NotNull(message = "机构ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long orgId;

    /**
     * 长者姓名
     */
    @NotBlank(message = "长者姓名不能为空", groups = {AddGroup.class, EditGroup.class})
    private String name;

    /**
     * 长者性别 1男0女
     */
    @NotNull(message = "长者性别 1男0女不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long gender;

    /**
     * 出生日期
     */
    @NotNull(message = "出生日期不能为空", groups = {AddGroup.class, EditGroup.class})
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date birthday;

    /**
     * 年龄
     */
    @NotNull(message = "年龄不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long age;

    /**
     * 身高
     */
    @NotBlank(message = "身高不能为空", groups = {AddGroup.class, EditGroup.class})
    private String height;

    /**
     * 体重
     */
    @NotBlank(message = "体重不能为空", groups = {AddGroup.class, EditGroup.class})
    private String weight;

    /**
     * 婚姻状况编码 1未婚 2已婚 3丧偶 4离异
     */
    @NotBlank(message = "婚姻状况编码 1未婚 2已婚 3丧偶 4离异不能为空", groups = {AddGroup.class, EditGroup.class})
    private String maritalCode;

    /**
     * 身份证类型
     * 1 居民身份证
     * 2 社保卡
     */
    @NotNull(message = "身份证类型 1 居民身份证 2 社保卡不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long idCardType;

    /**
     * 身份证号
     */
    @NotBlank(message = "身份证号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String idCardNumber;

    /**
     * 微信账号
     */
    @NotBlank(message = "微信账号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String wechatPhone;

    /**
     * 固定电话
     */
    @NotBlank(message = "固定电话不能为空", groups = {AddGroup.class, EditGroup.class})
    private String homePhone;

    /**
     * 精神健康 1有精神病史 2无
     */
    @NotBlank(message = "精神健康 1有精神病史 2无不能为空", groups = {AddGroup.class, EditGroup.class})
    private String mentalHealth;

    /**
     * 进行评估原因  1首次评估  2常规评估  3即时评估  4因对评估结果有疑问进行复评  5退出服务评估-包括服务最后三天   6退出服务跟进评估  7其他-例如上诉、研究
     */
    @NotBlank(message = "进行评估原因  1首次评估  2常规评估  3即时评估  4因对评估结果有疑问进行复评  5退出服务评估-包括服务最后三天   6退出服务跟进评估  7其他-例如上诉、研究不能为空", groups = {AddGroup.class, EditGroup.class})
    private String reasonForEvaluation;

    /**
     * 最近评估等级
     * 1能力完好
     * 2能力轻度受损(轻度失能)
     * 3能力中度受损(中度失能)
     * 4能力重度受损(重度失能)
     * 5能力完全丧失(完全失能)
     */
    @NotBlank(message = "最近评估等级 1能力完好 2能力轻度受损(轻度失能) 3能力中度受损(中度失能) 4能力重度受损(重度失能) 5能力完全丧失(完全失能)不能为空", groups = {AddGroup.class, EditGroup.class})
    private String latestEvalGrade;

    /**
     * 经济来源  1退休金/养老金 2子女补贴 3亲友资助 4国家普惠型补贴 5个人储蓄6其他补贴
     */
    @NotBlank(message = "经济来源  1退休金/养老金 2子女补贴 3亲友资助 4国家普惠型补贴 5个人储蓄6其他补贴不能为空", groups = {AddGroup.class, EditGroup.class})
    private String incomeBy;

    /**
     * 社保号码
     */
    @NotBlank(message = "社保号码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String ssNumber;

    /**
     * 支付方式
     */
    @NotBlank(message = "支付方式不能为空", groups = {AddGroup.class, EditGroup.class})
    private String paymentWay;

    /**
     * 类别(居家，机构）
     */
    @NotNull(message = "类别(居家，机构）不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long kind;

    /**
     * 状态
     */
    @NotNull(message = "状态不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long status;

    /**
     * 民族
     */
    @NotBlank(message = "民族不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long ethnic;

    /**
     * 省（居住地）
     */
    @NotBlank(message = "省（居住地）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String residenceProvince;

    /**
     * 市（居住地）
     */
    @NotBlank(message = "市（居住地）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String residenceCity;

    /**
     * 区/县（居住地）
     */
    @NotBlank(message = "区/县（居住地）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String residenceDistrict;

    /**
     * 街道/镇（居住地）
     */
    @NotBlank(message = "街道/镇（居住地）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String residenceStreet;

    /**
     * 社区/街道（居住地）
     */
    @NotBlank(message = "社区/街道（居住地）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String residenceCommunity;

    /**
     * 详细地址（居住地）
     */
    @NotBlank(message = "详细地址（居住地）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String residenceAddressDetail;

    /**
     * 省（户籍地）
     */
    @NotBlank(message = "省（户籍地）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String idCardProvince;

    /**
     * 市（户籍地）
     */
    @NotBlank(message = "市（户籍地）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String idCardCity;

    /**
     * 区/县（户籍地）
     */
    @NotBlank(message = "区/县（户籍地）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String idCardDistrict;

    /**
     * 街道/镇（户籍地）
     */
    @NotBlank(message = "街道/镇（户籍地）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String idCardStreet;

    /**
     * 社区/村委（户籍地）
     */
    @NotBlank(message = "社区/村委（户籍地）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String idCardCommunity;

    /**
     * 详细地址（户籍地）
     */
    @NotBlank(message = "详细地址（户籍地）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String idCardAddressDetail;

    /**
     * 政治面貌：1中共党员, 2共青团员, 3民主党派, 4无党派人士, 5群众
     */
    @NotNull(message = "政治面貌：1中共党员, 2共青团员, 3民主党派, 4无党派人士, 5群众不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long politicalAffiliation;

    /**
     * 老人特殊分类
     * 1 特困
     * 2 低保
     * 3 低保边缘
     */
    @NotNull(message = "老人特殊分类 1 特困 2 低保 3 低保边缘不能为空", groups = {AddGroup.class, EditGroup.class})
    private String specialType;

    /**
     * 亲属名字
     */
    @NotBlank(message = "亲属名字不能为空", groups = {AddGroup.class, EditGroup.class})
    private String relativeName;

    /**
     * 亲属联系电话
     */
    @NotBlank(message = "亲属联系电话不能为空", groups = {AddGroup.class, EditGroup.class})
    private String relativePhone;

    /**
     * 亲属身份证号
     */
    @NotBlank(message = "亲属身份证号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String relativeIdNumber;

    /**
     * 亲属与老人关系：1配偶, 2子女, 3父母, 4兄弟姐妹, 5孙子/女, 6其他亲属
     */
    @NotNull(message = "亲属与老人关系：1配偶, 2子女, 3父母, 4兄弟姐妹, 5孙子/女, 6其他亲属不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long relativeRelationship;

    /**
     * 亲属居住的详细地址
     */
    @NotBlank(message = "亲属居住的详细地址不能为空", groups = {AddGroup.class, EditGroup.class})
    private String relativeResidenceAddress;

    /**
     * 教育程度代码
     */
    @NotBlank(message = "教育程度代码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String educationCode;

    /**
     * 教育程度名称
     */
    @NotBlank(message = "教育程度名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String educationName;

    /**
     * 居住状况代码
     */
    @NotBlank(message = "居住状况代码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String residencyCode;

    /**
     * 居住状况名称
     */
    @NotBlank(message = "居住状况名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String residencyName;

    /**
     * 居住情况（1独居、2与配偶居住、3与子女居住、4与父母居住、5与姐妹兄弟居住、6与其它亲属居住、7与非亲属关系的人居住、8养老机构）
     */
    @NotBlank(message = "居住情况（1独居、2与配偶居住、3与子女居住、4与父母居住、5与姐妹兄弟居住、6与其它亲属居住、7与非亲属关系的人居住、8养老机构）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String liveWithWhoCode;

    /**
     * 居住情况（1独居、2与配偶居住、3与子女居住、4与父母居住、5与姐妹兄弟居住、6与其它亲属居住、7与非亲属关系的人居住、8养老机构）
     */
    @NotBlank(message = "居住情况（1独居、2与配偶居住、3与子女居住、4与父母居住、5与姐妹兄弟居住、6与其它亲属居住、7与非亲属关系的人居住、8养老机构）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String liveWithWhoName;

    /**
     * 入院时间（机构老人）
     */
    @NotNull(message = "入院时间（机构老人）不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long inTime;

    /**
     * 房号（机构老人）
     */
    @NotBlank(message = "房号（机构老人）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String roomNum;

    /**
     * 床号（机构老人）
     */
    @NotBlank(message = "床号（机构老人）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String bedNum;

    /**
     * 护理小组id（机构老人）
     */
    @NotNull(message = "护理小组id（机构老人）不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long grpId;

    /**
     * 负责组员id（机构老人）
     */
    @NotNull(message = "负责组员id（机构老人）不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long grpUserId;

    /**
     * 负责组员姓名（机构老人）
     */
    @NotBlank(message = "负责组员姓名（机构老人）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String grpUserName;

    /**
     * 负责组员手机（机构老人）
     */
    @NotBlank(message = "负责组员手机（机构老人）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String grpUserMobile;

    /**
     * 照护级别
     */
    @NotNull(message = "照护级别不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long careLevel;

    /**
     * 注意事项
     */
    @NotBlank(message = "注意事项不能为空", groups = {AddGroup.class, EditGroup.class})
    private String note;

    /**
     * 头像
     */
    @NotBlank(message = "头像不能为空", groups = {AddGroup.class, EditGroup.class})
    private String avatar;

    /**
     * 地理位置
     */
    @NotBlank(message = "地理位置不能为空", groups = {AddGroup.class, EditGroup.class})
    private String location;

    /**
     * 宗教信仰
     */
    @NotBlank(message = "宗教信仰不能为空", groups = {AddGroup.class, EditGroup.class})
    private String religiousBelief;

    /**
     * 参保地
     */
    @NotBlank(message = "参保地不能为空", groups = {AddGroup.class, EditGroup.class})
    private String insuredPlace;

    /**
     * 失能时间
     */
    @NotNull(message = "失能时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date disabilityTime;

    /**
     * 是否首次申请
     */
    @NotNull(message = "是否首次申请不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date applying;

    /**
     * 联系电话
     */
    @NotBlank(message = "联系电话不能为空", groups = {AddGroup.class, EditGroup.class})
    private String phone;

    /**
     * 保障方式
     */
    @NotBlank(message = "保障方式不能为空", groups = {AddGroup.class, EditGroup.class})
    private String guaranteeMethod;

    /**
     * 照护者
     */
    @NotBlank(message = "照护者不能为空", groups = {AddGroup.class, EditGroup.class})
    private String caregiver;

    /**
     * 评估参照日期
     */
    @NotNull(message = "评估参照日期不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date evaReferenceDate;

    /**
     * 申请人期望的照顾目标
     */
    @NotBlank(message = "申请人期望的照顾目标不能为空", groups = {AddGroup.class, EditGroup.class})
    private String expectedCareGoals;

    /**
     * 上次入住医院至今时间
     */
    @NotBlank(message = "上次入住医院至今时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private String untilNow;

    /**
     * 申请人对入住长期照顾院舍决定的控制
     */
    @NotBlank(message = "申请人对入住长期照顾院舍决定的控制不能为空", groups = {AddGroup.class, EditGroup.class})
    private String controlOfDecisions;

    /**
     * 开始入住时间
     */
    @NotNull(message = "开始入住时间不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date startDate;

    /**
     * 出生地
     */
    @NotBlank(message = "出生地不能为空", groups = {AddGroup.class, EditGroup.class})
    private String placeOfBirth;

    /**
     * 种族
     */
    @NotBlank(message = "种族不能为空", groups = {AddGroup.class, EditGroup.class})
    private String race;

    /**
     * 过去5年内入住院舍记录
     */
    @NotBlank(message = "过去5年内入住院舍记录不能为空", groups = {AddGroup.class, EditGroup.class})
    private String pastRecord;

    /**
     * 主要使用语言
     */
    @NotBlank(message = "主要使用语言不能为空", groups = {AddGroup.class, EditGroup.class})
    private String mainLanguage;

    /**
     * 既往工作
     */
    @NotBlank(message = "既往工作不能为空", groups = {AddGroup.class, EditGroup.class})
    private String previousWork;

    private List<AeaElderFamilyBo> kinList;
}
