package org.dromara.cyly.aea.aspect;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.dromara.cyly.aea.dubbo.RemoteEstimateStatisticsServiceImpl;
import org.dromara.cyly.aea.handler.EvaluatingInformationWebSocketHandler;
import org.dromara.cyly.aea.handler.HomepageStatisticsWebSocketHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Aspect
@Component
@Slf4j
public class EvaluatingInformationAspect {

    @Autowired
    private EvaluatingInformationWebSocketHandler evaluatingInformationWebSocketHandler;

    @Autowired
    private HomepageStatisticsWebSocketHandler homepageStatisticsWebSocketHandler;

    private final ObjectMapper objectMapper = new ObjectMapper();
    // 切面表达式，指定切入点为 TaskTotalController 中的 report 方法
    @AfterReturning("execution(* org.dromara.cyly.aea.controller.AeaTaskTotalController.report())")
    public void afterReportMethodExecution() throws JsonProcessingException {
        // 在 report 方法执行完毕后调用 sendEvaluatingInformation
          // 将对象转换为 JSON 字符串
        System.out.println("评估完成发送Websocket消息");
        log.info("评估完成发送Websocket消息");
        evaluatingInformationWebSocketHandler.sendEvaluatingInformation();
        homepageStatisticsWebSocketHandler.sendHomepageStatistics();
    }
}
