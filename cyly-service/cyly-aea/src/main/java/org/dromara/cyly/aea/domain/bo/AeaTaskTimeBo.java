package org.dromara.cyly.aea.domain.bo;

import org.dromara.cyly.aea.domain.AeaTaskTime;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;

import java.util.Date;

/**
 * 记录评估任务模块耗时业务对象 aea_task_time
 *
 * <AUTHOR>
 * @date 2025-03-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AeaTaskTime.class, reverseConvertGenerate = false)
public class AeaTaskTimeBo extends BaseEntity {

    /**
     * 主键id
     */
    @NotNull(message = "主键id不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 任务id
     */
    @NotBlank(message = "任务id不能为空", groups = { AddGroup.class, EditGroup.class })
    private String taskId;

    /**
     * 1: 评估信息表
       2: 评估对象基本信息表
       3: 信息供应者与联系人基本信息表
       4: 疾病诊断和用药情况表
       5: 健康相关问题
       6: 自理能力评估表
       7: 基础运动能力评估表
       8: 精神状态评估表
       9: 感知觉与社会参与评估表
     */
    @NotNull(message = "1、评估信息表，2、评估对象基本信息表，3、信息供应者与联系人基本信息表，4、疾病诊断和用药情况表，5、健康相关问题，6、自理能力评估表，7、基础运动能力评估表，8、精神状态评估表，9、感知觉与社会参与评估表不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long moduleId;

    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date startTime;

    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date endTime;

    /**
     * 模块耗时
     */
    @NotNull(message = "模块耗时不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long duration;


}
