package org.dromara.cyly.aea.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.cyly.aea.domain.AeaHomeCare;
import org.dromara.cyly.aea.domain.bo.HomeCareBo;
import org.dromara.cyly.aea.domain.vo.HomeCareVo;
import org.dromara.cyly.aea.mapper.AeaHomeCareMapper;
import org.dromara.cyly.aea.service.IAeaHomeCareService;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 《InterRAI家居照护评估》 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-07
 */
@Service
public class AeaHomeCareServiceImpl implements IAeaHomeCareService {

    @Autowired
    private AeaHomeCareMapper homeCareMapper;

    /**
     * 新增《InterRAI家居照护评估》内容
     *
     * @param bo 《InterRAI家居照护评估》内容
     * @return 是否新增成功
     */
    @Transactional
    @Override
    public Boolean insertByBo(HomeCareBo bo) {
        AeaHomeCare add = MapstructUtils.convert(bo, AeaHomeCare.class);
        validEntityBeforeSave(add);

        // 获取传进来的封面url
        String coverUrl = add.getCoverUrl();

        // 检查传入的coverUrl是否为空
        if (coverUrl != null && !coverUrl.isEmpty()) {
            // 查询所有书号为3的数据，检查是否存在任意一条记录的coverUrl不为空
            LambdaQueryWrapper<AeaHomeCare> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AeaHomeCare::getBook, 3)
                // 查找coverUrl不为空的数据
                .isNotNull(AeaHomeCare::getCoverUrl);

            if (homeCareMapper.selectCount(queryWrapper) > 0) {
                // 存在至少一条记录的coverUrl不为空，抛出异常
                throw new ServiceException("《InterRAI家居照护评估》的封面已存在");
            }
        }

        //查找是否包含"节"，是true，否false
        String sectionNumber = add.getSectionNumber();

        boolean isParentFlag = sectionNumber.matches("^第.*节(.*)?$");

        //符合条件，则为节，设置parentId为0
        if (isParentFlag) {

            // 提取节号中前面的大写字母部分，使用正则表达式提取
            String keyPart = sectionNumber.replaceAll("第([A-Z])节.*", "$1");

            // 检查book=3和提取的大写字母对应的记录是否已存在
            LambdaQueryWrapper<AeaHomeCare> existenceCheckWrapper = new LambdaQueryWrapper<>();
            existenceCheckWrapper.eq(AeaHomeCare::getBook, 3)
                .like(AeaHomeCare::getSectionNumber, "第" + keyPart + "节");

            if (homeCareMapper.selectCount(existenceCheckWrapper) > 0) {
                throw new ServiceException("您输入的节号已存在!");
            }

            add.setBook(3);
            add.setParentId(0L);
            boolean addFlag = homeCareMapper.insert(add) > 0;
            if (addFlag) {
                add.setId(add.getId());
            }
            return addFlag;
        } else {
            //判断新增的节号，是子项还是孙项还是不符合格式的项
            String parentFlag = getString(sectionNumber);

            //模糊查询对应的父项
            LambdaQueryWrapper<AeaHomeCare> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AeaHomeCare::getBook, 3);
            queryWrapper.like(AeaHomeCare::getSectionNumber, parentFlag);
            List<AeaHomeCare> parentList = homeCareMapper.selectList(queryWrapper);
            if (!parentList.isEmpty()) {
                AeaHomeCare parent = parentList.get(0);
                //设置所属书籍标识
                add.setBook(3);
                //获取父项id设置为其子项的parentId
                add.setParentId(parent.getId());
            }

            boolean addFlags = homeCareMapper.insert(add) > 0;
            if (addFlags) {
                add.setId(add.getId());
            }
            return addFlags;
        }
    }

    @NotNull
    private static String getString(String sectionNumber) {
        return getString(sectionNumber);
    }

    /**
     * 修改《InterRAI家居照护评估》内容
     *
     * @param bo 《InterRAI家居照护评估》内容
     * @return 是否修改成功
     */
    @Transactional
    @Override
    public Boolean updateByBo(HomeCareBo bo) {

        // 转换为实体
        AeaHomeCare update = MapstructUtils.convert(bo, AeaHomeCare.class);
        validEntityBeforeSave(update);

        // 获取传入的封面URL
        String coverUrl = update.getCoverUrl();

        if (coverUrl != null && !coverUrl.isEmpty()) {
            // 清除所有书号为 3 的记录中 coverUrl 不为空的内容
            clearAllCoverUrlsByBookId();

            // 将当前记录的封面 URL 设置为新的封面 URL
            update.setCoverUrl(coverUrl);
        } else {
            // 如果 coverUrl 为空，则确保不进行更新当前记录的封面 URL
            update.setCoverUrl(null);
        }

        // 更新当前记录
        return homeCareMapper.updateById(update) > 0;
    }

    // 清除书号为 3 中所有 coverUrl 不为空的记录的内容
    private void clearAllCoverUrlsByBookId() {
        // 查询 coverUrl 不为空的所有记录
        LambdaQueryWrapper<AeaHomeCare> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AeaHomeCare::getBook, 3)
            .isNotNull(AeaHomeCare::getCoverUrl);

        // 查询符合条件的所有记录
        List<AeaHomeCare> records = homeCareMapper.selectList(queryWrapper);

        // 执行逐条更新将 coverUrl 设为 null
        for (AeaHomeCare record : records) {
            record.setCoverUrl(null);
            homeCareMapper.updateById(record);
        }
    }

    private void validEntityBeforeSave(AeaHomeCare entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 分页查询《InterRAI家居照护评估》列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 《InterRAI家居照护评估》列表
     */
    @Override
    public TableDataInfo<HomeCareVo> queryPageList(HomeCareBo bo, PageQuery pageQuery) {
        // 将HomeCareBo转换为对应的AeaHomeCare类型，保持类型一致
        AeaHomeCare query = MapstructUtils.convert(bo, AeaHomeCare.class);
        validEntityBeforeSave(query);

        String keyword = query.getSectionNumber();
        LambdaQueryWrapper<AeaHomeCare> wrapper = new LambdaQueryWrapper<>();

        if (keyword.isEmpty()) {
            // 查询所有书号为3且parentId为0的记录
            wrapper.eq(AeaHomeCare::getBook, 3)
                .eq(AeaHomeCare::getParentId, 0)
                .orderByAsc(AeaHomeCare::getId);

            Page<HomeCareVo> parentPage = homeCareMapper.selectVoPage(pageQuery.build(), wrapper);

            // 填充封面URL到结果中
            fillCoverUrls(parentPage.getRecords());

            // 获取每个父项的子项，并进行关联
            for (HomeCareVo parent : parentPage.getRecords()) {
                getChildren(parent);
            }

            return TableDataInfo.build(parentPage);
        } else {
            // 如果keyword不为空，进行模糊查询，书号为3
            wrapper.like(AeaHomeCare::getSectionNumber, keyword)
                .eq(AeaHomeCare::getBook, 3);

            Page<HomeCareVo> parentPage = homeCareMapper.selectVoPage(pageQuery.build(), wrapper);
            List<HomeCareVo> parentList = parentPage.getRecords();

            // 填充父项的封面URL
            fillCoverUrls(parentList);

            // 递归获取子项和孙项
            for (HomeCareVo parent : parentList) {
                getChildren(parent);
            }

            // 填充子项和孙项的封面 URL
            fillChildCoverUrls(parentList);

            return TableDataInfo.build(parentPage);
        }
    }

    // 填充父项的封面 URL
    private void fillCoverUrls(List<HomeCareVo> records) {
        // 存储第一个有效的封面 URL
        String validCoverUrl = null;

        // 查询第一个有效的coverUrl，针对书号为3的记录
        LambdaQueryWrapper<AeaHomeCare> coverWrapper = new LambdaQueryWrapper<>();
        coverWrapper.eq(AeaHomeCare::getBook, 3)
            .isNotNull(AeaHomeCare::getCoverUrl)
            .ne(AeaHomeCare::getCoverUrl, "");
        coverWrapper.last("LIMIT 1");

        List<AeaHomeCare> covers = homeCareMapper.selectList(coverWrapper);
        if (!covers.isEmpty()) {
            validCoverUrl = covers.getFirst().getCoverUrl();
        }

        // 遍历records，为每条记录填充coverUrl
        for (HomeCareVo record : records) {
            if (record.getCoverUrl() == null || record.getCoverUrl().isEmpty()) {
                record.setCoverUrl(validCoverUrl);
            }
        }
    }

    // 填充子项和孙项的封面 URL
    private void fillChildCoverUrls(List<HomeCareVo> parentList) {
        for (HomeCareVo parent : parentList) {
            if (parent.getChildren()!= null) {
                // 填充子项的coverUrl，直接使用当前类型的对象
                parent.getChildren().forEach(child -> fillCoverUrls(Collections.singletonList(child)));
            }
        }
    }

    //获取子项和孙项
    private void getChildren(HomeCareVo parent) {
        // 查询子项，使用正确的实体类型AeaHomeCare构建查询条件
        LambdaQueryWrapper<AeaHomeCare> childWrapper = new LambdaQueryWrapper<>();
        childWrapper.eq(AeaHomeCare::getParentId, parent.getId());
        List<AeaHomeCare> children = homeCareMapper.selectList(childWrapper);

        // 如果有子项，处理并递归获取子项的子项（孙项）
        if (!children.isEmpty()) {

            List<HomeCareVo> uniqueChildren = new ArrayList<>();

            // 将子项转换为Vo列表，使用正确的转换类型HomeCareVo
            for (AeaHomeCare child : children) {
                HomeCareVo childVo = MapstructUtils.convert(child, HomeCareVo.class);
                uniqueChildren.add(childVo);
            }

            parent.setChildren(uniqueChildren);

            // 递归获取每个子项的子项（孙项）
            for (HomeCareVo childVo : uniqueChildren) {
                getChildren(childVo);
            }
        }
    }

    /**
     * 查询《InterRAI家居照护评估》详细内容
     *
     * @param id 主键
     * @return 《InterRAI家居照护评估》详细内容
     */
    @Override
    public HomeCareVo queryById(Long id) {
        // 通过 ID 获取书籍信息
        HomeCareVo bookInfo = homeCareMapper.selectVoById(id);
        if (bookInfo == null) {
            throw new ServiceException("指定 ID 的书籍不存在");
        }

        // 获取书籍号
        Integer bookId = bookInfo.getBook();

        // 通过书籍号查找该书籍的所有内容
        LambdaQueryWrapper<AeaHomeCare> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AeaHomeCare::getBook, bookId);
        List<AeaHomeCare> allContentList = homeCareMapper.selectList(queryWrapper);

        // 遍历所有内容，查找封面 URL
        for (AeaHomeCare content : allContentList) {
            String coverUrl = content.getCoverUrl();
            if (coverUrl != null && !coverUrl.isEmpty()) {
                bookInfo.setCoverUrl(coverUrl);
                // 找到第一个有效 URL 后可以退出循环
                break;
            }
        }

        return bookInfo;
    }

    /**
     * 校验并批量删除《InterRAI家居照护评估》内容
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return homeCareMapper.deleteByIds(ids) > 0;
    }
}
