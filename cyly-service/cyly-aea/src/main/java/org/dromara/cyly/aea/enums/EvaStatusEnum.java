package org.dromara.cyly.aea.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
public enum EvaStatusEnum {
    UNEVALUATED("0", "等待中"),
    EVALUATED("1", "执行中"),
    END_EVALUATED("2", "已结束"),
    SUSPENDED("3", "已暂停"),
    CANCELED("4", "已取消");

    private final String code;
    private final String description;

    EvaStatusEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public static String getDescriptionByCode(String reasonCode) {
        return Arrays.stream(EvaStatusEnum.values())
            .filter(evaStatus -> evaStatus.getCode().equals(reasonCode))
            .map(EvaStatusEnum::getDescription)
            .findFirst()
            .orElse(null); // 如果没有找到，返回 null
    }

    public static List<StatusVO> toList() {
        return Arrays.stream(EvaStatusEnum.values())
            .map(status -> new StatusVO(status.code, status.description))
            .collect(Collectors.toList());
    }

    /**
     * Value Object 类，用于封装 code 和 description。
     */
    @Getter
    public static class StatusVO {
        private final String code;
        private final String description;

        public StatusVO(String code, String description) {
            this.code = code;
            this.description = description;
        }

    }
}
