package org.dromara.cyly.aea.service;


import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.cyly.aea.domain.bo.AeaCaregiverInfoBo;
import org.dromara.cyly.aea.domain.vo.AeaCaregiverInfoVo;

import java.util.Collection;
import java.util.List;

/**
 * 护理员信息Service接口
 *
 * <AUTHOR> Li
 * @date 2025-01-13
 */
public interface IAeaCaregiverInfoService {

    /**
     * 查询护理员信息
     *
     * @param id 主键
     * @return 护理员信息
     */
    AeaCaregiverInfoVo queryById(Long id);

    /**
     * 分页查询护理员信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 护理员信息分页列表
     */
    TableDataInfo<AeaCaregiverInfoVo> queryPageList(AeaCaregiverInfoBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的护理员信息列表
     *
     * @param bo 查询条件
     * @return 护理员信息列表
     */
    List<AeaCaregiverInfoVo> queryList(AeaCaregiverInfoBo bo);

    /**
     * 新增护理员信息
     *
     * @param bo 护理员信息
     * @return 是否新增成功
     */
    Boolean insertByBo(AeaCaregiverInfoBo bo);

    /**
     * 修改护理员信息
     *
     * @param bo 护理员信息
     * @return 是否修改成功
     */
    Boolean updateByBo(AeaCaregiverInfoBo bo);

    /**
     * 校验并批量删除护理员信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
