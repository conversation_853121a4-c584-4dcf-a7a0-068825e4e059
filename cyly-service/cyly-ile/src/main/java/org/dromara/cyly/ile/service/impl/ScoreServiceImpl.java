package org.dromara.cyly.ile.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.AppLoginHelper;
import org.dromara.cyly.ile.domain.*;
import org.dromara.cyly.ile.domain.bo.EvaluateTaskBo;
import org.dromara.cyly.ile.domain.bo.ScoreBo;
import org.dromara.cyly.ile.domain.bo.TitleScoreBo;
import org.dromara.cyly.ile.domain.vo.EvaluateRecordVo;
import org.dromara.cyly.ile.domain.vo.EvaluateTaskVo;
import org.dromara.cyly.ile.domain.vo.ModuleScore;
import org.dromara.cyly.ile.domain.vo.ScoreVo;
import org.dromara.cyly.ile.enums.ScoreType;
import org.dromara.cyly.ile.mapper.*;
import org.dromara.cyly.ile.service.IScoreService;
import org.dromara.system.api.RemoteAppUserService;
import org.dromara.system.api.RemoteUserService;
import org.dromara.system.api.model.AppLoginUser;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.Arrays;
import java.util.List;

/**
 * 得分Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-11-01
 */


@RequiredArgsConstructor
@Service
@Slf4j
public class ScoreServiceImpl implements IScoreService {

    private final ScoreMapper baseMapper;
    private final TitleInfoMapper titleInfoMapper;
    private final ReentrantLock lock = new ReentrantLock();
    private final ScoreMapper scoreMapper;
    private final EvaluateTaskMapper evaluateTaskMapper;
    private final TopicEvaluateMapper topicEvaluateMapper;
    private final TitleScoreMapper titleScoreMapper;
    @DubboReference
    RemoteAppUserService remoteAppUserService;

    @DubboReference
    RemoteUserService remoteUserService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean initializeScore(EvaluateTaskBo bo) {
        lock.lock();
        try {
            // 根据版本ID查询题目列表
            List<TitleInfo> titleList = titleInfoMapper.selectList(new LambdaQueryWrapper<TitleInfo>()
                .eq(TitleInfo::getVersion, bo.getVersionId())
            );

            if (titleList == null || titleList.isEmpty()) {
                // 如果没有找到对应的题目，则返回false
                return false;
            }

            List<Long> deptIds = Arrays.stream(bo.getDeptId().split(","))
                .map(Long::parseLong)
                .toList();

            // 构建待插入的得分记录列表
            List<Score> scoreRecords = deptIds.stream()
                .flatMap(deptId -> titleList.stream().map(title -> {
                    Score score = new Score();
                    // 设置评估任务的信息
                    score.setTaskId(bo.getTaskId());
                    // 使用当前循环中的deptId
                    score.setDeptId(deptId);
                    score.setTitleInfoId(title.getId());
                    // 设置与题目相关的信息
                    score.setNumber(title.getNumber());
                    score.setTitleId(title.getTitleId());
                    score.setTitlePid(title.getTitlePid());
                    score.setTitleVersion(title.getVersion());
                    // 初始状态为null
                    score.setStatus(null);
                    return score;
                })).collect(Collectors.toList());

            // 批量插入构建好的得分记录到数据库中
            return baseMapper.insertBatch(scoreRecords);
        } catch (Exception e) {
            log.error("初始化失败");
            return false;
        } finally {
            lock.unlock();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class) // 明确指定回滚异常类型
    public Boolean updateScore(List<TitleScoreBo> titleScoreBoList, Long flag) {
        log.info("【评分更新】方法开始执行，参数：题目数量={}, flag={}",
            (titleScoreBoList != null) ? titleScoreBoList.size() : 0, flag);
        // region 1.加锁保证线程安全
        lock.lock();
        try {
            // region 2.获取用户信息
            AppLoginUser loginUser = AppLoginHelper.getLoginUser();
            if (loginUser == null) {
                log.error("【评分更新】获取登录用户信息失败");
                throw new ServiceException("用户未登录");
            }

            // 通过RPC获取ILE用户信息（注意：此处需捕获远程调用异常）
            AppLoginUser ileUser;
            try {
                ileUser = remoteAppUserService.getAppUserInfo(loginUser.getUserId(), "000000", "ile");
                log.debug("【评分更新】获取ILE用户信息成功，用户ID={}, 部门ID={}", ileUser.getUserId(), ileUser.getDeptId());
            } catch (Exception e) {
                log.error("【评分更新】获取ILE用户信息失败，用户ID={}", loginUser.getUserId(), e);
                throw new ServiceException("用户信息获取失败");
            }
            // region 3.参数校验
            if (CollectionUtils.isEmpty(titleScoreBoList)) {
                log.warn("【评分更新】提交数据为空，用户ID={}", ileUser.getUserId());
                throw new ServiceException("需要提交的题目得分为空");
            }

            /*
             * 校验所有题目PID一致性：
             * - 取第一个题目的PID作为基准值
             * - 遍历检查后续题目是否匹配
             */
            // 使用get(0)替代getFirst()保证兼容性
            Long referencePid = titleScoreBoList.getFirst().getPid();
            for (TitleScoreBo titleScore : titleScoreBoList) {
                if (!referencePid.equals(titleScore.getPid())) {
                    log.warn("【评分更新】题目PID不一致，期望PID={}, 实际PID={}", referencePid, titleScore.getPid());
                    throw new ServiceException("数据信息不一致");
                }
            }
            // region 4.查询数据库现有记录
            LambdaQueryWrapper<Score> queryWrapper = new LambdaQueryWrapper<Score>()
                .eq(Score::getTitlePid, referencePid)
                .eq(Score::getTaskId, ileUser.getTaskId())
                .eq(Score::getDeptId, ileUser.getDeptId());
            Long dbCount = scoreMapper.selectCount(queryWrapper);
            log.debug("【评分更新】数据库中存在PID={}的记录数：{}", referencePid, dbCount);

            /*
             * 处理待更新分数列表：
             * - 当数据库记录数与提交数量不一致时，筛选出需要保留的旧记录
             * - 否则初始化空列表
             */
            List<Score> scores;
            if (!dbCount.equals((long) titleScoreBoList.size())) {
                Set<Long> submittedIds = titleScoreBoList.stream()
                    .map(TitleScoreBo::getScoreId)
                    .collect(Collectors.toSet());
                List<Score> dbScores = scoreMapper.selectList(queryWrapper);
                scores = dbScores.stream()
                    .filter(score -> !submittedIds.contains(score.getId()))
                    .collect(Collectors.toList());
                log.info("【评分更新】检测到数据不一致，保留旧记录数量={}", scores.size());
            } else {
                scores = new ArrayList<>();
            }
            // endregion

            // region 5.构建新分数记录
            /*
             * 根据flag处理更新模式：
             * - flag为空：全量更新，转换所有提交数据为Score对象
             * - flag非空：条件更新，仅处理匹配flag的记录
             */
            if (flag == null) {
                for (TitleScoreBo bo : titleScoreBoList) {
                    scores.add(newScore(ileUser, bo));
                }
                log.info("【评分更新】全量更新模式，新增记录数={}", titleScoreBoList.size());
            } else {
                titleScoreBoList.stream()
                    .filter(bo -> flag.equals(bo.getScoreId()))
                    .findFirst()
                    .ifPresent(bo -> {
                        scores.add(newScore(ileUser, bo));
                        log.debug("【评分更新】条件更新模式，处理scoreId={}", flag);
                    });
            }
            // endregion

            // region 6.处理父级评分
            // 6.1 查询评估任务版本信息
            EvaluateTaskVo evaluateTaskVo = evaluateTaskMapper.selectVoById(ileUser.getTaskId());
            if (evaluateTaskVo == null) {
                log.error("【评分更新】未找到评估任务，任务ID={}", ileUser.getTaskId());
                throw new ServiceException("评估任务不存在");
            }

            // 6.2 查询父级评分记录（注意：此处需确保非空）
            LambdaQueryWrapper<Score> fatherQuery = new LambdaQueryWrapper<Score>()
                .eq(Score::getTitleId, referencePid)
                .eq(Score::getTaskId, ileUser.getTaskId())
                .eq(Score::getDeptId, ileUser.getDeptId())
                .eq(Score::getTitleVersion, evaluateTaskVo.getVersionId());
            Score fatherScore = scoreMapper.selectOne(fatherQuery);
            if (fatherScore == null) {
                log.error("【评分更新】未找到父级评分记录，PID={}", referencePid);
                throw new ServiceException("父级评分记录不存在");
            }

            // 6.3 计算总分（注意：分数转换需处理格式异常）
            double totalScore;
            try {
                totalScore = (flag == null) ?
                    scores.stream().mapToDouble(s -> parseScore(s.getScore())).sum() :
                    titleScoreBoList.stream().mapToDouble(bo -> parseScore(bo.getScore())).sum();
            } catch (NumberFormatException e) {
                log.error("【评分更新】分数格式错误", e);
                throw new ServiceException("分数格式不正确");
            }
            fatherScore.setScore(String.valueOf(totalScore));
            log.info("【评分更新】计算总分完成，总分={}", totalScore);
            // endregion

            // region 7.必填项校验逻辑
            /*
             * 校验规则：
             * - 当flag存在时，统计提交数据中的必填项数量
             * - 当flag为空时，查询数据库中的必填项数量
             */
            Integer requiredCount = titleScoreMapper.requiredCount(
                fatherScore.getTitleId(),
                ileUser.getTaskId(),
                ileUser.getDeptId(),
                evaluateTaskVo.getVersionId().toString()
            );

            int actualRequired = (flag != null) ?
                (int) titleScoreBoList.stream().filter(bo -> bo.getRequired() == 1).count() :
                titleScoreMapper.countRequiredTitles(
                    scores.stream().map(Score::getId).collect(Collectors.toList())
                );

            if (actualRequired != requiredCount) {
                log.warn("【评分更新】必填项数量不符，要求数量={}, 实际数量={}", requiredCount, actualRequired);
                throw new ServiceException("必填题目数量不足");
            }
            // endregion

            // region 8.更新评分状态
            /*
             * 状态设置规则：
             * - 当flag存在且完成数达标时，状态设为1
             * - 其他情况根据业务规则设置状态
             */
            if (flag != null) {
                Integer completeCount = titleScoreMapper.completeCount(
                    fatherScore.getTitleId(),
                    ileUser.getTaskId(),
                    ileUser.getDeptId(),
                    evaluateTaskVo.getVersionId().toString()
                );
                fatherScore.setStatus(completeCount >= actualRequired ? 1 : null);
            } else {
                fatherScore.setStatus(1);
            }
            // endregion

            // region 9.校验总分合法性
            TitleInfo titleInfo = titleInfoMapper.selectById(fatherScore.getTitleInfoId());
            Double maxScore = Optional.ofNullable(titleInfo.getTotalScore())
                .orElseGet(() -> Optional.ofNullable(titleInfo.getSecondaryScore())
                    .orElseGet(() -> Optional.ofNullable(titleInfo.getThreeLevelScore())
                        .orElse(titleInfo.getFourLevelScore())));

            double current = Double.parseDouble(fatherScore.getScore());
            if (current > maxScore) {
                log.error("【评分更新】总分超过上限，当前={}, 最大={}", current, maxScore);
                throw new ServiceException("累计分值超过上限");
            }
            // endregion

            // region 10.执行批量更新
            scores.add(fatherScore);
            if (!scoreMapper.updateBatchById(scores)) {
                log.error("【评分更新】批量更新失败，影响记录数={}", scores.size());
                throw new ServiceException("数据保存失败");
            }
            log.info("【评分更新】批量更新成功，影响记录数={}", scores.size());
            // endregion

            // region 11.递归更新父节点
            if (fatherScore.getTitlePid() != 0) {
                List<TitleScore> children = titleScoreMapper.getTitleScoreChildrenList(
                    fatherScore.getTitlePid(),
                    ileUser.getTaskId(),
                    ileUser.getDeptId(),
                    evaluateTaskVo.getVersionId().toString()
                );
                updateScore(MapstructUtils.convert(children, TitleScoreBo.class), fatherScore.getId());
                log.debug("【评分更新】递归更新父节点，PID={}", fatherScore.getTitlePid());
            } else if (fatherScore.getStatus() != null) {
                topicEvaluateMapper.update(
                    null,
                    new LambdaUpdateWrapper<TopicEvaluate>()
                        .eq(TopicEvaluate::getDeptId, ileUser.getDeptId())
                        .eq(TopicEvaluate::getTaskId, ileUser.getTaskId())
                        .eq(TopicEvaluate::getTitleId, fatherScore.getTitleId())
                        .set(TopicEvaluate::getIsShow, 1)
                );
            }
            // endregion

            return true;
        } catch (ServiceException e) {
            log.error("【评分更新】业务处理失败：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("【评分更新】系统异常：", e);
            throw new ServiceException("系统处理异常:" + e.getMessage());
        } finally {
            lock.unlock();
            log.debug("【评分更新】锁已释放");
        }
    }

    // region 辅助方法

    /**
     * 安全转换分数字符串为double
     *
     * @param score 分数字符串（可空）
     * @return 转换后的数值（默认0.0）
     */
    private double parseScore(String score) {
        if (StringUtils.isBlank(score)) return 0.0;
        try {
            return Double.parseDouble(score);
        } catch (NumberFormatException e) {
            log.warn("分数转换异常，原始值：{}", score);
            return 0.0;
        }
    }

    /**
     * 创建新的评分对象
     * 该方法用于将用户对某个标题的评分信息封装到一个Score对象中它从一个TitleScoreBo对象中提取相关信息，
     * 并结合当前登录用户的信息，生成一个新的评分记录这个评分记录可以用于存储或更新数据库中的评分信息
     *
     * @param loginUser    当前登录的用户信息，用于设置评分记录的用户ID和用户名
     * @param titleScoreBo 包含标题评分信息的业务对象，从中提取Score对象的属性值
     * @return 返回一个新创建的Score对象，该对象包含了从titleScoreBo中提取的评分信息以及登录用户的用户信息
     */
    private Score newScore(AppLoginUser loginUser, TitleScoreBo titleScoreBo) {
        // 创建一个新的Score对象
        Score score = new Score();
        // 设置评分记录的ID
        score.setId(titleScoreBo.getScoreId());
        // 设置标题ID
        score.setTitleId(titleScoreBo.getId());
        // 设置标题的父ID
        score.setTitlePid(titleScoreBo.getPid());
        // 根据评分是否存在设置状态，如果评分为空则状态为0，否则为1
        score.setStatus(1);
        // 设置评分状态
        score.setScoreStatus(titleScoreBo.getScoreStatus());
        // 设置评分变动的原因
        score.setReason(titleScoreBo.getReason());
        // 设置评分记录的用户ID
        score.setSysUserId(loginUser.getUserId());
        // 设置评分记录的用户名
        score.setSysUserName(loginUser.getUserName());
        // 设置评分值
        score.setScore(titleScoreBo.getScore());
        // 设置图片URL
        score.setImageUrl(titleScoreBo.getImageUrl());
        // 返回新创建的Score对象
        return score;
    }

    @Override
    @Transactional
    public void deleteScores(Long taskId) {
        lock.lock();
        try {
            // 查询任务ID下是否有状态大于0的记录
            int count = Math.toIntExact(baseMapper.selectCount(new LambdaQueryWrapper<Score>()
                .eq(Score::getTaskId, taskId)
                // 检查状态是否有大于0的数据
                .gt(Score::getStatus, 0)
            ));

            if (count > 0) {
                // 如果有状态大于0的记录，则不允许删除
                throw new RuntimeException("此任务在进行中，不可删除");
            }

            // 如果没有状态大于0的记录，则执行删除操作
            int rowsDeleted = baseMapper.delete(new LambdaQueryWrapper<Score>().eq(Score::getTaskId, taskId));
            if (rowsDeleted == 0) {
                throw new RuntimeException("删除失败");
            }
        } catch (Exception e) {
            log.error("删除评估任务失败", e);
            throw new RuntimeException("删除异常:" + e);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 查询得分
     */

    @Override
    public ScoreVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询得分列表
     */

    @Override
    public TableDataInfo<ScoreVo> queryPageList(ScoreBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<Score> lqw = buildQueryWrapper(bo);
        Page<ScoreVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询得分列表
     */

    @Override
    public List<ScoreVo> queryList(ScoreBo bo) {
        LambdaQueryWrapper<Score> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<Score> buildQueryWrapper(ScoreBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<Score> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getTaskId() != null, Score::getTaskId, bo.getTaskId());
        lqw.eq(bo.getDeptId() != null, Score::getDeptId, bo.getDeptId());
        lqw.eq(bo.getSysUserId() != null, Score::getSysUserId, bo.getSysUserId());
        lqw.eq(bo.getTitleId() != null, Score::getTitleId, bo.getTitleId());
        lqw.eq(bo.getTitlePid() != null, Score::getTitlePid, bo.getTitlePid());
        lqw.eq(StringUtils.isNotBlank(bo.getScore()), Score::getScore, bo.getScore());
        lqw.eq(StringUtils.isNotBlank(bo.getImageUrl()), Score::getImageUrl, bo.getImageUrl());
        lqw.eq(bo.getScoreStatus() != null, Score::getScoreStatus, bo.getScoreStatus());
        lqw.eq(bo.getScoreStatus() != null, Score::getStatus, bo.getStatus());
        lqw.like(StringUtils.isNotBlank(bo.getSysUserName()), Score::getSysUserName, bo.getSysUserName());
        lqw.eq(StringUtils.isNotBlank(bo.getReason()), Score::getReason, bo.getReason());
        return lqw;
    }

    /**
     * 新增得分
     */

    @Override
    public Boolean insertByBo(ScoreBo bo) {
        Score add = BeanUtil.toBean(bo, Score.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /*
     * 修改得分
     */

    @Override
    public Boolean updateByBo(ScoreBo bo) {
        Score update = BeanUtil.toBean(bo, Score.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */

    private void validEntityBeforeSave(Score entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除得分
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        return baseMapper.deleteByIds(ids) > 0;
    }


    @Override
    public EvaluateRecordVo usereva(ScoreBo bo) {
        AppLoginUser loginUser = AppLoginHelper.getLoginUser();
        AppLoginUser ileUser = null;
        if (loginUser != null) {
            ileUser = remoteAppUserService.getAppUserInfo(loginUser.getUserId(), "000000", "ile");
        }
        Long taskId = ileUser.getTaskId();
        EvaluateTaskVo evaluateTaskVo = evaluateTaskMapper.selectVoOne(new LambdaQueryWrapper<EvaluateTask>()
            .eq(EvaluateTask::getTaskId, taskId)
        );
        if (evaluateTaskVo == null) {
            throw new RuntimeException("任务不存在");
        }
        Long versionId = evaluateTaskVo.getVersionId();
        // 1. 查询用户相关的Score记录（任务、部门、用户ID）
        List<Score> userScores = scoreMapper.selectList(new LambdaQueryWrapper<Score>()
            .eq(Score::getTaskId, ileUser.getTaskId())
            .eq(Score::getDeptId, ileUser.getDeptId())
            .eq(Score::getSysUserId, ileUser.getUserId()));

        // 2. 获取所有一级标题的Score记录（TitlePid=0）
        List<Score> level1Scores = userScores.stream()
            .filter(score -> score.getTitlePid() == 0)
            .collect(Collectors.toList());

        // 3. 批量查询一级标题的TitleInfo（优化N+1查询）
        List<TitleInfo> level1Titles = new ArrayList<>();
        if (!level1Scores.isEmpty()) {
            // 提取所有Number并去重
            List<String> level1Numbers = level1Scores.stream()
                .map(Score::getNumber)
                .distinct()
                .collect(Collectors.toList());

            // 批量查询TitleInfo
            level1Titles = titleInfoMapper.selectList(new LambdaQueryWrapper<TitleInfo>()
                .eq(TitleInfo::getTitlePid, 0)
                .in(TitleInfo::getNumber, level1Numbers)
                .eq(TitleInfo::getVersion, versionId));
        }

        // 4. 获取所有相关TitleInfo（替代原注释代码）
        List<Long> titleIds = userScores.stream()
            .map(Score::getTitleId)
            .collect(Collectors.toList());
        List<TitleInfo> allTitles = titleInfoMapper.selectList(new LambdaQueryWrapper<TitleInfo>()
            .in(TitleInfo::getTitleId, titleIds)
            .eq(TitleInfo::getVersion, versionId));

        // 5. 获取所有评分记录（修复用户ID条件）
        List<Score> allScores = scoreMapper.selectList(
            new LambdaQueryWrapper<Score>()
                .eq(Score::getTaskId, ileUser.getTaskId())
                .eq(Score::getDeptId, ileUser.getDeptId())
                .eq(Score::getSysUserId, ileUser.getUserId())  // 修复：eq替换ge
                .isNotNull(Score::getScore)
        );

        // 6. 构建父子关系Map（添加空过滤）
        Map<Long, List<TitleInfo>> parentChildMap = allTitles.stream()
            .filter(Objects::nonNull)
            .filter(title -> title.getTitlePid() != null)
            .collect(Collectors.groupingBy(
                TitleInfo::getTitlePid,
                Collectors.toList()
            ));

        // 7. 构建得分Map（简化合并策略）
        Map<Long, Score> scoreMap = allScores.stream()
            .filter(score -> score.getTitleId() != null)
            .collect(Collectors.toMap(
                Score::getTitleId,
                Function.identity(),
                (oldVal, newVal) -> oldVal
            ));

        // 8. 构造返回数据结构
        EvaluateRecordVo result = new EvaluateRecordVo();
        List<EvaluateRecordVo.FristLevelVo> records = new ArrayList<>();  // 修复拼写：FirstLevelVo

        for (TitleInfo level1 : level1Titles) {
            EvaluateRecordVo.FristLevelVo firstLevel = new EvaluateRecordVo.FristLevelVo();
            firstLevel.setTitle(level1.getEvaluationProject());

            // 9. 处理二级标题（复用scoreMap避免重复查询）
            List<TitleInfo> level2List = parentChildMap.getOrDefault(level1.getTitleId(), new ArrayList<>());

            List<EvaluateRecordVo.RecordVo> children = level2List.stream()
                .map(level2 -> {
                    EvaluateRecordVo.RecordVo record = new EvaluateRecordVo.RecordVo();
                    record.setTitle(level2.getEvaluationProject());

                    // 10. 从预加载的scoreMap获取状态（代替baseMapper查询）
                    Score score = scoreMap.get(level2.getTitleId());
                    if (score != null) {
                        record.setScoreStatus(score.getScoreStatus());
                    }

                    // 11. 计算得分
                    String totalScore = calculateLevel2Score(level2.getTitleId(), parentChildMap, scoreMap);
                    record.setScore(totalScore);

                    // 12. 处理评定记录
                    Score latestScore = getLatestScore(level2.getTitleId(), parentChildMap, scoreMap);
                    if (latestScore != null) {
                        record.setEvaluateTime(DateUtil.format(latestScore.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
                        // 批量获取用户昵称（建议在外部批量获取）
                        record.setEvaluator(remoteAppUserService.selectNickNameById(latestScore.getSysUserId()));
                    }

                    return record;
                })
                .filter(record -> record.getScore() != null && !"0.00".equals(record.getScore()))
                .collect(Collectors.toList());

            firstLevel.setChildren(children);
            records.add(firstLevel);
        }

        result.setFristLevelVos(records);  // 同步修正setter名称
        return result;
    }

    private String calculateLevel2Score(Long titleId, Map<Long, List<TitleInfo>> parentChildMap, Map<Long, Score> scoreMap) {
        double totalScore = 0.0;

        //获取子标题
        List<TitleInfo> children = parentChildMap.getOrDefault(titleId, new ArrayList<>());

        if (children.isEmpty()) {
            // 如果是四级标题，返回得分
            Score score = scoreMap.get(titleId);
            if (score != null && score.getScore() != null) {
                return String.format("%.2f", Double.valueOf(score.getScore()));
            }
            return "0.00";
        } else {
            // 递归计算子标题的得分
            for (TitleInfo child : children) {
                if (child.getTitleId() != null) {
                    String childScore = calculateLevel2Score(child.getTitleId(),
                        parentChildMap, scoreMap);
                    if (!childScore.isEmpty()) {
                        try {
                            totalScore += Double.parseDouble(childScore);
                        } catch (NumberFormatException e) {
                            log.error("分数格式错误: {}", childScore);
                        }
                    }
                }
            }
        }


        return String.format("%.2f", totalScore);

    }


    /**
     * 获取最新的评定记录
     */

    private Score getLatestScore(Long titleId,
                                 Map<Long, List<TitleInfo>> parentChildMap,
                                 Map<Long, Score> scoreMap) {

        // 获取子标题
        List<TitleInfo> children = parentChildMap.getOrDefault(titleId, new ArrayList<>());

        if (children.isEmpty()) {
            return scoreMap.get(titleId);
        }

        // 获取所有子标题中最新的评定记录
        Score latestScore = null;
        for (TitleInfo child : children) {
            Score childScore = getLatestScore(child.getTitleId(), parentChildMap, scoreMap);
            if (childScore != null) {
                if (latestScore == null ||
                    childScore.getCreateTime().after(latestScore.getCreateTime())) {
                    latestScore = childScore;
                }
            }
        }

        return latestScore;
    }

    @Override
    public Map<String, Object> getAllScores() throws Exception {

        AppLoginUser loginUser = AppLoginHelper.getLoginUser();
        AppLoginUser ileUser = null;
        if (loginUser != null) {
            ileUser = remoteAppUserService.getAppUserInfo(loginUser.getUserId(), "000000", "ile");
        }
        long taskId = 0;
        long deptId = 0;
        if (ileUser != null) {
            taskId = ileUser.getTaskId();
            deptId = ileUser.getDeptId();
        }
        EvaluateTaskVo evaluateTaskVo = evaluateTaskMapper.selectVoOne(new LambdaQueryWrapper<EvaluateTask>()
            .eq(EvaluateTask::getTaskId, taskId)
        );

        List<ModuleScore> moduleScores = baseMapper.getModuleScore(deptId, taskId);

        if (moduleScores.isEmpty()) {
            throw new RuntimeException("未找到相关数据");
        }

        try {
            Map<String, Object> result = new HashMap<>();
            List<ModuleScore> adjustedScores = new ArrayList<>();

            // 动态获取 DEDUCTION 值，例如从配置文件或业务逻辑计算

            for (ModuleScore ms : moduleScores) {
                ModuleScore adjustedScore = adjustScore(ms, taskId, deptId, evaluateTaskVo.getVersionId());
                if (adjustedScore != null) {
                    adjustedScores.add(adjustedScore);
                } else {
                    // 如果调整后的分数为 null，则创建一个得分为 0 的 ModuleScore 对象
                    ModuleScore zeroScore = new ModuleScore();
                    zeroScore.setEvaluationProject(ScoreType.fromDescription(ms.getEvaluationProject()).getPrefix());
                    zeroScore.setScore(0.0);
                    adjustedScores.add(zeroScore);
                }
            }

            // 计算总分
            double sum = adjustedScores.stream().mapToDouble(ms -> ms != null ? ms.getScore() : 0.0).sum();
            result.put("total", Math.round(sum * 100.0) / 100.0);
            result.put("moduleScore", adjustedScores);

            return result;
        } catch (Exception e) {
            log.error("计算得分失败: {}", e.getMessage());
            throw new RuntimeException("计算得分失败", e);
        }
    }


    /**
     * 调整分数方法
     * 根据给定的模块分数和任务、部门、版本ID，调整分数并返回调整后的模块分数对象
     *
     * @param ms 模块分数对象，包含需要调整的原始分数信息
     * @param taskId 任务ID，用于查询未评分的总分数
     * @param deptId 部门ID，与任务ID和版本ID一起用于查询特定条件下的未评分总分数
     * @param versionId 版本ID，用于区分不同版本的评分标准
     * @return 返回调整后的模块分数对象，如果输入无效或无法计算，则返回null
     */
    private ModuleScore adjustScore(ModuleScore ms, long taskId, long deptId, long versionId) {
        // 检查模块分数对象及其分数是否为空，为空则直接返回null
        if (ms == null || ms.getScore() == null) {
            return null;
        }
        // 根据评价项目描述获取对应的分数类型
        ScoreType scoreType = ScoreType.fromDescription(ms.getEvaluationProject());
        // 如果没有匹配的 ScoreType，返回 null
        if (scoreType == null) {
            return null;
        }
        // 获取分数类型的原始总分
        double originalTotal = scoreType.getTotalScore();
        // 查询未评分的总分数，并计算扣分
        List<Map<String, Object>> notScoreTotalScore = baseMapper.getNotScoreTotalScore(deptId, taskId, versionId);
        double deduction = 0;
        for (Map<String, Object> map : notScoreTotalScore) {
            Object prefixObj = map.get("prefix");
            if (prefixObj != null) {
                ScoreType prefix = ScoreType.fromNumber(prefixObj.toString());
                if (prefix != null) {
                    Object deductionObj = map.get("deduction");
                    if (deductionObj != null) {
                        try {
                            deduction = Double.parseDouble(deductionObj.toString());
                            break;
                            // 如果需要使用 deduction 变量，请在这里添加相应的逻辑
                        } catch (NumberFormatException e) {
                            // 处理转换异常，例如记录日志或跳过该条目
                            log.info("Invalid deduction value: {}", deductionObj);
                        }
                    }
                }
            }
        }
        // 计算有效总分，并根据原始分数计算最终分数
        double effectiveTotal = originalTotal - deduction;
        double finalScore;
        if (ms.getScore() == null) {
            finalScore = 0.0;
        } else {
            finalScore = (ms.getScore() / effectiveTotal) * originalTotal;
        }
        // 创建并返回调整后的模块分数对象
        ModuleScore adjustedScore = new ModuleScore();
        adjustedScore.setEvaluationProject(scoreType.getPrefix());
        adjustedScore.setScore(Math.round(finalScore * 100.0) / 100.0);
        return adjustedScore;
    }


    /**
     * 写入总得分
     */
    @Override
    public Map<String, Object> getModuleScore() {
        // 基础参数准备：从登录用户获取机构ID、任务ID，并查询关联的评估版本ID
        AppLoginUser loginUser = AppLoginHelper.getLoginUser();
        // 获取ILE场景的特殊用户信息（含任务ID和部门ID）
        AppLoginUser ileUser = null;
        if (loginUser != null) {
            ileUser = remoteAppUserService.getAppUserInfo(loginUser.getUserId(), "000000", "ile");
        }
        long taskId = 0;
        long deptId = 0;
        if (ileUser != null) {
            taskId = ileUser.getTaskId();
            deptId = ileUser.getDeptId();
        }
        // 根据任务ID查询评估任务信息，获取版本ID用于后续规则查询
        EvaluateTaskVo evaluateTaskVo = evaluateTaskMapper.selectVoOne(
            new LambdaQueryWrapper<EvaluateTask>().eq(EvaluateTask::getTaskId, taskId)
        );
        long versionId = evaluateTaskVo.getVersionId();

        try {
            // 核心计算流程：获取各模块的基础得分数据（不含扣分）
            List<ModuleScore> moduleScores = baseMapper.getModuleScore(deptId, taskId);
            if (moduleScores.isEmpty()) {
                throw new RuntimeException("未找到模块得分数据");
            }

            // 处理两种扣分规则 ---------------------------------------------------
            // 1. 配置性扣分：获取不参与得分的规则扣分（如违规项直接扣除总分）
            List<Map<String, Object>> notScoreTotalScores = baseMapper.getNotScoreTotalScore(deptId, taskId, versionId);
            // 按模块前缀汇总配置性扣分（相同模块扣分值累加）
            Map<String, Double> notScoreTotalMap = notScoreTotalScores.stream()
                .filter(map -> map.get("prefix") != null && map.get("deduction") != null && ((Number) map.get("deduction")).doubleValue() > 0)
                .collect(Collectors.toMap(
                    map -> (String) map.get("prefix"),
                    map -> ((Number) map.get("deduction")).doubleValue(),
                    Double::sum  // 合并相同前缀的扣分值
                ));

            // 2. 实际扣分：获取各模块在实际评分过程中的扣分
            List<Map<String, Object>> deductionScore = baseMapper.getDeductionScore(deptId, taskId, versionId);
            // 按模块前缀汇总实际扣分（相同模块扣分值累加）
            Map<String, Double> deductionMap = deductionScore.stream()
                .filter(map -> map.get("prefix") != null && map.get("deduction") != null && ((Number) map.get("deduction")).doubleValue() > 0)
                .collect(Collectors.toMap(
                    map -> (String) map.get("prefix"),
                    map -> ((Number) map.get("deduction")).doubleValue(),
                    Double::sum  // 合并相同前缀的扣分值
                ));

            // 模块得分计算（含扣分调整）---------------------------------------------
            List<Map<String, Object>> scoreDetails = new ArrayList<>();
            double totalScore = 0.0;          // 调整后的总分（所有模块调整得分之和）
            double totalDeduction = 0.0;      // 总扣分（所有模块实际扣分之和）
            double totalAdjustedScore = 0.0;  // 原始得分总和（调整前的实际得分之和）

            for (ModuleScore ms : moduleScores) {
                if (ms == null || ms.getEvaluationProject() == null) continue;

                // 根据评估项目获取评分类型（含模块前缀、总分等信息）
                ScoreType scoreType = ScoreType.fromDescription(ms.getEvaluationProject());
                String moduleKey = scoreType.getNumber();  // 模块唯一标识（如A1、B2）

                // 获取各项数值
                double originalTotal = scoreType.getTotalScore();  // 模块原始总分（理论最大值）
                double actualScore = Optional.ofNullable(ms.getScore()).orElse(0.0);  // 原始得分（扣分前的实际得分）
                double deduction = notScoreTotalMap.getOrDefault(moduleKey, 0.0);      // 配置性扣分（不参与实际得分计算）
                double realDeduction = deductionMap.getOrDefault(moduleKey, 0.0);      // 实际扣分（从总分中扣除）
                totalDeduction += realDeduction;

                // 计算调整得分：扣除配置性扣分后，按比例还原实际得分到原总分体系
                double effectiveTotal = originalTotal - deduction;  // 实际可用总分（扣除配置性扣分后的总分）
                double adjustedScore = (effectiveTotal > 0) ?
                    (actualScore / effectiveTotal) * originalTotal : 0.0;  // 将实际得分按比例映射到原总分
                adjustedScore = Math.round(adjustedScore * 100.0) / 100.0;  // 保留两位小数

                // 构建明细数据（按顺序保证输出结构）
                Map<String, Object> detail = new LinkedHashMap<>();
                detail.put("module", scoreType.getPrefix());      // 模块标识（如A、B）
                detail.put("originalTotal", originalTotal);      // 模块原始总分
                detail.put("actualScore", actualScore);          // 原始得分（扣除配置性扣分后的得分）
                detail.put("deduction", realDeduction);           // 实际扣分（从总分中扣除的值）
                detail.put("adjustedScore", adjustedScore);      // 调整后得分（映射到原总分的实际得分）
                scoreDetails.add(detail);

                // 累计总分
                totalAdjustedScore += actualScore;  // 原始得分总和（用于验证）
                totalScore += adjustedScore;        // 调整后的总分
            }

            // 组装最终结果集 ----------------------------------------------------
            Map<String, Object> result = new LinkedHashMap<>();
            result.put("moduleScore", scoreDetails);            // 各模块得分明细
            result.put("total", Math.round(totalScore * 100.0) / 100.0);          // 调整后的总分（含扣分影响）
            result.put("totalDeduction", Math.round(totalDeduction * 100.0) / 100.0);  // 总扣分数
            result.put("totalAdjustedScore", totalAdjustedScore);  // 原始得分总和（不含总分调整）
            return result;

        } catch (Exception e) {
            // 异常处理：记录日志并包装错误信息
            log.error("评分计算失败 | 机构ID:{} | 任务ID:{} | 错误:{}", deptId, taskId, e.getMessage());
            throw new RuntimeException("评分计算失败: " + e.getMessage());
        }
    }
}

