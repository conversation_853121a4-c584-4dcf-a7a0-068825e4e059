package org.dromara.cyly.digital.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.cyly.digital.domain.DigitalEmployeeSkills;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T09:18:56+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class DigitalEmployeeSkillsBoToDigitalEmployeeSkillsMapper__1Impl implements DigitalEmployeeSkillsBoToDigitalEmployeeSkillsMapper__1 {

    @Override
    public DigitalEmployeeSkills convert(DigitalEmployeeSkillsBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        DigitalEmployeeSkills digitalEmployeeSkills = new DigitalEmployeeSkills();

        digitalEmployeeSkills.setCreateBy( arg0.getCreateBy() );
        digitalEmployeeSkills.setCreateDept( arg0.getCreateDept() );
        digitalEmployeeSkills.setCreateTime( arg0.getCreateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            digitalEmployeeSkills.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        digitalEmployeeSkills.setSearchValue( arg0.getSearchValue() );
        digitalEmployeeSkills.setUpdateBy( arg0.getUpdateBy() );
        digitalEmployeeSkills.setUpdateTime( arg0.getUpdateTime() );
        digitalEmployeeSkills.setId( arg0.getId() );
        digitalEmployeeSkills.setLevel( arg0.getLevel() );
        digitalEmployeeSkills.setName( arg0.getName() );
        digitalEmployeeSkills.setNursingLevel( arg0.getNursingLevel() );
        digitalEmployeeSkills.setParentId( arg0.getParentId() );
        digitalEmployeeSkills.setRemake( arg0.getRemake() );
        digitalEmployeeSkills.setSort( arg0.getSort() );

        return digitalEmployeeSkills;
    }

    @Override
    public DigitalEmployeeSkills convert(DigitalEmployeeSkillsBo arg0, DigitalEmployeeSkills arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateTime( arg0.getCreateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setId( arg0.getId() );
        arg1.setLevel( arg0.getLevel() );
        arg1.setName( arg0.getName() );
        arg1.setNursingLevel( arg0.getNursingLevel() );
        arg1.setParentId( arg0.getParentId() );
        arg1.setRemake( arg0.getRemake() );
        arg1.setSort( arg0.getSort() );

        return arg1;
    }
}
