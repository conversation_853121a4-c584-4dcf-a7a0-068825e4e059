package org.dromara.common.log.event;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.AppOperLogBoToOperLogEventMapper__7;
import org.dromara.system.domain.bo.SysOperLogBo;
import org.dromara.system.domain.bo.SysOperLogBoToOperLogEventMapper__7;
import org.dromara.system.domain.bo.SysOperLogBoToSysOperLogMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysOperLogBoToSysOperLogMapper__7.class,AppOperLogBoToOperLogEventMapper__7.class,SysOperLogBoToOperLogEventMapper__7.class,OperLogEventToAppOperLogBoMapper__7.class},
    imports = {}
)
public interface OperLogEventToSysOperLogBoMapper__7 extends BaseMapper<OperLogEvent, SysOperLogBo> {
}
