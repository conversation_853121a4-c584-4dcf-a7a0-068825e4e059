package org.dromara.common.log.event;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.AppOperLogBo;
import org.dromara.system.domain.bo.AppOperLogBoToAppOperLogMapper__7;
import org.dromara.system.domain.bo.AppOperLogBoToOperLogEventMapper__7;
import org.dromara.system.domain.bo.SysOperLogBoToOperLogEventMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {AppOperLogBoToAppOperLogMapper__7.class,AppOperLogBoToOperLogEventMapper__7.class,SysOperLogBoToOperLogEventMapper__7.class,OperLogEventToSysOperLogBoMapper__7.class},
    imports = {}
)
public interface OperLogEventToAppOperLogBoMapper__7 extends BaseMapper<OperLogEvent, AppOperLogBo> {
}
