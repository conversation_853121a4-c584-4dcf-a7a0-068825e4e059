package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.DictAddress;
import org.dromara.system.domain.DictAddressToDictAddressVoMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {DictAddressToDictAddressVoMapper__7.class},
    imports = {}
)
public interface DictAddressVoToDictAddressMapper__7 extends BaseMapper<DictAddressVo, DictAddress> {
}
