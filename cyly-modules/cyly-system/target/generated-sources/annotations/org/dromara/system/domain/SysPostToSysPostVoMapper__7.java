package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysPostBoToSysPostMapper__7;
import org.dromara.system.domain.vo.SysPostVo;
import org.dromara.system.domain.vo.SysPostVoToSysPostMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysPostBoToSysPostMapper__7.class,SysPostVoToSysPostMapper__7.class},
    imports = {}
)
public interface SysPostToSysPostVoMapper__7 extends BaseMapper<SysPost, SysPostVo> {
}
