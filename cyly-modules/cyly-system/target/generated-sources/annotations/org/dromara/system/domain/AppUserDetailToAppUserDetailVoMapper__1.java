package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__388;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.AppUserDetailBoToAppUserDetailMapper__1;
import org.dromara.system.domain.vo.AppUserDetailVo;
import org.dromara.system.domain.vo.AppUserDetailVoToAppUserDetailMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__388.class,
    uses = {AppUserDetailVoToAppUserDetailMapper__1.class,AppUserDetailBoToAppUserDetailMapper__1.class},
    imports = {}
)
public interface AppUserDetailToAppUserDetailVoMapper__1 extends BaseMapper<AppUserDetail, AppUserDetailVo> {
}
