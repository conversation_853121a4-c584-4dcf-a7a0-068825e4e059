package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysLogininforBoToSysLogininforMapper__6;
import org.dromara.system.domain.vo.SysLogininforVo;
import org.dromara.system.domain.vo.SysLogininforVoToSysLogininforMapper__6;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysLogininforBoToSysLogininforMapper__6.class,SysLogininforVoToSysLogininforMapper__6.class},
    imports = {}
)
public interface SysLogininforToSysLogininforVoMapper__6 extends BaseMapper<SysLogininfor, SysLogininforVo> {
}
