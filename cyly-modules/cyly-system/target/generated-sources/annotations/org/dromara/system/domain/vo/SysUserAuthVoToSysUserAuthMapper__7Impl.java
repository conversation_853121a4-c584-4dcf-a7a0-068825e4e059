package org.dromara.system.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.system.domain.SysUserAuth;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T09:53:44+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class SysUserAuthVoToSysUserAuthMapper__7Impl implements SysUserAuthVoToSysUserAuthMapper__7 {

    @Override
    public SysUserAuth convert(SysUserAuthVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysUserAuth sysUserAuth = new SysUserAuth();

        sysUserAuth.setAuthIds( arg0.getAuthIds() );
        sysUserAuth.setId( arg0.getId() );
        sysUserAuth.setUserId( arg0.getUserId() );

        return sysUserAuth;
    }

    @Override
    public SysUserAuth convert(SysUserAuthVo arg0, SysUserAuth arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setAuthIds( arg0.getAuthIds() );
        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );

        return arg1;
    }
}
