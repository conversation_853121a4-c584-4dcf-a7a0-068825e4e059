package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysUserDetail;
import org.dromara.system.domain.SysUserDetailToSysUserDetailVoMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysUserDetailToSysUserDetailVoMapper__7.class},
    imports = {}
)
public interface SysUserDetailVoToSysUserDetailMapper__7 extends BaseMapper<SysUserDetailVo, SysUserDetail> {
}
