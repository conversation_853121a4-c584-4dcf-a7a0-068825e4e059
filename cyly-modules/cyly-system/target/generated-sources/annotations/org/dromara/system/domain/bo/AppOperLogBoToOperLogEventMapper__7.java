package org.dromara.system.domain.bo;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.common.log.event.OperLogEvent;
import org.dromara.common.log.event.OperLogEventToAppOperLogBoMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {AppOperLogBoToAppOperLogMapper__7.class,OperLogEventToAppOperLogBoMapper__7.class},
    imports = {}
)
public interface AppOperLogBoToOperLogEventMapper__7 extends BaseMapper<AppOperLogBo, OperLogEvent> {
}
