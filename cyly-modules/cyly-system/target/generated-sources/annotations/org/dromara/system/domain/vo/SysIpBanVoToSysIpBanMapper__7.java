package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysIpBan;
import org.dromara.system.domain.SysIpBanToSysIpBanVoMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysIpBanToSysIpBanVoMapper__7.class},
    imports = {}
)
public interface SysIpBanVoToSysIpBanMapper__7 extends BaseMapper<SysIpBanVo, SysIpBan> {
}
