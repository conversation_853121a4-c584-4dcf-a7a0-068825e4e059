package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__394;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.AppUserPermissionBoToAppUserPermissionMapper;
import org.dromara.system.domain.vo.AppUserPermissionVo;
import org.dromara.system.domain.vo.AppUserPermissionVoToAppUserPermissionMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__394.class,
    uses = {AppUserPermissionVoToAppUserPermissionMapper.class,AppUserPermissionBoToAppUserPermissionMapper.class},
    imports = {}
)
public interface AppUserPermissionToAppUserPermissionVoMapper extends BaseMapper<AppUserPermission, AppUserPermissionVo> {
}
