package org.dromara.system.domain;

import javax.annotation.processing.Generated;
import org.dromara.system.domain.vo.AppUserDetailVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T09:20:26+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AppUserDetailToAppUserDetailVoMapper__1Impl implements AppUserDetailToAppUserDetailVoMapper__1 {

    @Override
    public AppUserDetailVo convert(AppUserDetail arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppUserDetailVo appUserDetailVo = new AppUserDetailVo();

        appUserDetailVo.setAge( arg0.getAge() );
        appUserDetailVo.setAppUserName( arg0.getAppUserName() );
        appUserDetailVo.setBirthday( arg0.getBirthday() );
        appUserDetailVo.setColleges( arg0.getColleges() );
        appUserDetailVo.setContactsPhone( arg0.getContactsPhone() );
        appUserDetailVo.setDegree( arg0.getDegree() );
        appUserDetailVo.setDetailId( arg0.getDetailId() );
        appUserDetailVo.setEmergencyContacts( arg0.getEmergencyContacts() );
        appUserDetailVo.setEthnic( arg0.getEthnic() );
        appUserDetailVo.setIdCard( arg0.getIdCard() );
        appUserDetailVo.setIdentityType( arg0.getIdentityType() );
        appUserDetailVo.setMarriage( arg0.getMarriage() );
        appUserDetailVo.setName( arg0.getName() );
        appUserDetailVo.setOrigin( arg0.getOrigin() );
        appUserDetailVo.setPostId( arg0.getPostId() );
        appUserDetailVo.setRemark( arg0.getRemark() );
        appUserDetailVo.setSkill( arg0.getSkill() );
        appUserDetailVo.setSpecialized( arg0.getSpecialized() );
        appUserDetailVo.setStatus( arg0.getStatus() );
        appUserDetailVo.setWorkExp( arg0.getWorkExp() );

        return appUserDetailVo;
    }

    @Override
    public AppUserDetailVo convert(AppUserDetail arg0, AppUserDetailVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setAge( arg0.getAge() );
        arg1.setAppUserName( arg0.getAppUserName() );
        arg1.setBirthday( arg0.getBirthday() );
        arg1.setColleges( arg0.getColleges() );
        arg1.setContactsPhone( arg0.getContactsPhone() );
        arg1.setDegree( arg0.getDegree() );
        arg1.setDetailId( arg0.getDetailId() );
        arg1.setEmergencyContacts( arg0.getEmergencyContacts() );
        arg1.setEthnic( arg0.getEthnic() );
        arg1.setIdCard( arg0.getIdCard() );
        arg1.setIdentityType( arg0.getIdentityType() );
        arg1.setMarriage( arg0.getMarriage() );
        arg1.setName( arg0.getName() );
        arg1.setOrigin( arg0.getOrigin() );
        arg1.setPostId( arg0.getPostId() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setSkill( arg0.getSkill() );
        arg1.setSpecialized( arg0.getSpecialized() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setWorkExp( arg0.getWorkExp() );

        return arg1;
    }
}
