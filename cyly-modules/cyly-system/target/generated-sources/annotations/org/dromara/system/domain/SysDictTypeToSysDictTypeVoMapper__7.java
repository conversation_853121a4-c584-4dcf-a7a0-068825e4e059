package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysDictTypeBoToSysDictTypeMapper__7;
import org.dromara.system.domain.vo.SysDictTypeVo;
import org.dromara.system.domain.vo.SysDictTypeVoToSysDictTypeMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysDictTypeBoToSysDictTypeMapper__7.class,SysDictTypeVoToSysDictTypeMapper__7.class},
    imports = {}
)
public interface SysDictTypeToSysDictTypeVoMapper__7 extends BaseMapper<SysDictType, SysDictTypeVo> {
}
