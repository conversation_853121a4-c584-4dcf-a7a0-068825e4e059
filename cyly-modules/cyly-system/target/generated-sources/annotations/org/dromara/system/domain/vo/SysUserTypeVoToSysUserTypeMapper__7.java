package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysUserType;
import org.dromara.system.domain.SysUserTypeToSysUserTypeVoMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysUserTypeToSysUserTypeVoMapper__7.class},
    imports = {}
)
public interface SysUserTypeVoToSysUserTypeMapper__7 extends BaseMapper<SysUserTypeVo, SysUserType> {
}
