package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.AppUserDetailBoToAppUserDetailMapper__6;
import org.dromara.system.domain.vo.AppUserDetailVo;
import org.dromara.system.domain.vo.AppUserDetailVoToAppUserDetailMapper__6;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {AppUserDetailVoToAppUserDetailMapper__6.class,AppUserDetailBoToAppUserDetailMapper__6.class},
    imports = {}
)
public interface AppUserDetailToAppUserDetailVoMapper__6 extends BaseMapper<AppUserDetail, AppUserDetailVo> {
}
