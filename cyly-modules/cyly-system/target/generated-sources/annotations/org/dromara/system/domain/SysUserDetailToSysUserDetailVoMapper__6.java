package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysUserDetailBoToSysUserDetailMapper__6;
import org.dromara.system.domain.vo.SysUserDetailVo;
import org.dromara.system.domain.vo.SysUserDetailVoToSysUserDetailMapper__6;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysUserDetailVoToSysUserDetailMapper__6.class,SysUserDetailBoToSysUserDetailMapper__6.class},
    imports = {}
)
public interface SysUserDetailToSysUserDetailVoMapper__6 extends BaseMapper<SysUserDetail, SysUserDetailVo> {
}
