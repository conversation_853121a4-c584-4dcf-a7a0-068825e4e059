package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysVersionBoToSysVersionMapper__6;
import org.dromara.system.domain.vo.SysVersionVo;
import org.dromara.system.domain.vo.SysVersionVoToSysVersionMapper__6;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysVersionBoToSysVersionMapper__6.class,SysVersionVoToSysVersionMapper__6.class},
    imports = {}
)
public interface SysVersionToSysVersionVoMapper__6 extends BaseMapper<SysVersion, SysVersionVo> {
}
