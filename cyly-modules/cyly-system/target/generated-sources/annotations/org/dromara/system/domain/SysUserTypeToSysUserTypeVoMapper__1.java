package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__388;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysUserTypeBoToSysUserTypeMapper__1;
import org.dromara.system.domain.vo.SysUserTypeVo;
import org.dromara.system.domain.vo.SysUserTypeVoToSysUserTypeMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__388.class,
    uses = {SysUserTypeBoToSysUserTypeMapper__1.class,SysUserTypeVoToSysUserTypeMapper__1.class},
    imports = {}
)
public interface SysUserTypeToSysUserTypeVoMapper__1 extends BaseMapper<SysUserType, SysUserTypeVo> {
}
