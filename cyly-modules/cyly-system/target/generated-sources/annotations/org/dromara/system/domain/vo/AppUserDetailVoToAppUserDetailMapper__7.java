package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.AppUserDetail;
import org.dromara.system.domain.AppUserDetailToAppUserDetailVoMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {AppUserDetailToAppUserDetailVoMapper__7.class},
    imports = {}
)
public interface AppUserDetailVoToAppUserDetailMapper__7 extends BaseMapper<AppUserDetailVo, AppUserDetail> {
}
