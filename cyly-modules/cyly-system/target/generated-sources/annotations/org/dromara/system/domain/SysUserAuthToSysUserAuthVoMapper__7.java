package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysUserAuthBoToSysUserAuthMapper__7;
import org.dromara.system.domain.vo.SysUserAuthVo;
import org.dromara.system.domain.vo.SysUserAuthVoToSysUserAuthMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysUserAuthVoToSysUserAuthMapper__7.class,SysUserAuthBoToSysUserAuthMapper__7.class},
    imports = {}
)
public interface SysUserAuthToSysUserAuthVoMapper__7 extends BaseMapper<SysUserAuth, SysUserAuthVo> {
}
