package org.dromara.system.domain.bo;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.common.log.event.OperLogEvent;
import org.dromara.common.log.event.OperLogEventToSysOperLogBoMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysOperLogBoToSysOperLogMapper__7.class,OperLogEventToSysOperLogBoMapper__7.class},
    imports = {}
)
public interface SysOperLogBoToOperLogEventMapper__7 extends BaseMapper<SysOperLogBo, OperLogEvent> {
}
