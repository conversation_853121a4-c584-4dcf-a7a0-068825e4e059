package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.AppLogininfor;
import org.dromara.system.domain.AppLogininforToAppLogininforVoMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {AppLogininforToAppLogininforVoMapper__7.class},
    imports = {}
)
public interface AppLogininforVoToAppLogininforMapper__7 extends BaseMapper<AppLogininforVo, AppLogininfor> {
}
