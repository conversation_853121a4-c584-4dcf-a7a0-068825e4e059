package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysUserBoToSysUserMapper__7;
import org.dromara.system.domain.vo.SysRoleVoToSysRoleMapper__7;
import org.dromara.system.domain.vo.SysUserVo;
import org.dromara.system.domain.vo.SysUserVoToSysUserMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysRoleVoToSysRoleMapper__7.class,SysRoleToSysRoleVoMapper__7.class,SysUserVoToSysUserMapper__7.class,SysUserBoToSysUserMapper__7.class},
    imports = {}
)
public interface SysUserToSysUserVoMapper__7 extends BaseMapper<SysUser, SysUserVo> {
}
