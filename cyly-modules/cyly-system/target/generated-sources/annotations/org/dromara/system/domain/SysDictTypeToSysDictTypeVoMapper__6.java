package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysDictTypeBoToSysDictTypeMapper__6;
import org.dromara.system.domain.vo.SysDictTypeVo;
import org.dromara.system.domain.vo.SysDictTypeVoToSysDictTypeMapper__6;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysDictTypeBoToSysDictTypeMapper__6.class,SysDictTypeVoToSysDictTypeMapper__6.class},
    imports = {}
)
public interface SysDictTypeToSysDictTypeVoMapper__6 extends BaseMapper<SysDictType, SysDictTypeVo> {
}
