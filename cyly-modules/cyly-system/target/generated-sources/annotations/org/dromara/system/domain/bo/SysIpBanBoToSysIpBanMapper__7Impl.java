package org.dromara.system.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.system.domain.SysIpBan;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T09:53:56+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class SysIpBanBoToSysIpBanMapper__7Impl implements SysIpBanBoToSysIpBanMapper__7 {

    @Override
    public SysIpBan convert(SysIpBanBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysIpBan sysIpBan = new SysIpBan();

        sysIpBan.setCreateBy( arg0.getCreateBy() );
        sysIpBan.setCreateDept( arg0.getCreateDept() );
        sysIpBan.setCreateTime( arg0.getCreateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysIpBan.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysIpBan.setSearchValue( arg0.getSearchValue() );
        sysIpBan.setUpdateBy( arg0.getUpdateBy() );
        sysIpBan.setUpdateTime( arg0.getUpdateTime() );
        sysIpBan.setBanEnd( arg0.getBanEnd() );
        sysIpBan.setBanId( arg0.getBanId() );
        sysIpBan.setBanReason( arg0.getBanReason() );
        sysIpBan.setBanStart( arg0.getBanStart() );
        sysIpBan.setBanStatus( arg0.getBanStatus() );
        sysIpBan.setIpAddress( arg0.getIpAddress() );
        sysIpBan.setIpNetwork( arg0.getIpNetwork() );

        return sysIpBan;
    }

    @Override
    public SysIpBan convert(SysIpBanBo arg0, SysIpBan arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateTime( arg0.getCreateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setBanEnd( arg0.getBanEnd() );
        arg1.setBanId( arg0.getBanId() );
        arg1.setBanReason( arg0.getBanReason() );
        arg1.setBanStart( arg0.getBanStart() );
        arg1.setBanStatus( arg0.getBanStatus() );
        arg1.setIpAddress( arg0.getIpAddress() );
        arg1.setIpNetwork( arg0.getIpNetwork() );

        return arg1;
    }
}
