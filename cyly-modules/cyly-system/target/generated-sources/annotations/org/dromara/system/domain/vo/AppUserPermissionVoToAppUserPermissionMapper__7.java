package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.AppUserPermission;
import org.dromara.system.domain.AppUserPermissionToAppUserPermissionVoMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {AppUserPermissionToAppUserPermissionVoMapper__7.class},
    imports = {}
)
public interface AppUserPermissionVoToAppUserPermissionMapper__7 extends BaseMapper<AppUserPermissionVo, AppUserPermission> {
}
