package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__394;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.AppOperLog;
import org.dromara.system.domain.AppOperLogToAppOperLogVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__394.class,
    uses = {AppOperLogToAppOperLogVoMapper.class},
    imports = {}
)
public interface AppOperLogVoToAppOperLogMapper extends BaseMapper<AppOperLogVo, AppOperLog> {
}
