package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.AppUserDetailBoToAppUserDetailMapper__7;
import org.dromara.system.domain.vo.AppUserDetailVo;
import org.dromara.system.domain.vo.AppUserDetailVoToAppUserDetailMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {AppUserDetailVoToAppUserDetailMapper__7.class,AppUserDetailBoToAppUserDetailMapper__7.class},
    imports = {}
)
public interface AppUserDetailToAppUserDetailVoMapper__7 extends BaseMapper<AppUserDetail, AppUserDetailVo> {
}
