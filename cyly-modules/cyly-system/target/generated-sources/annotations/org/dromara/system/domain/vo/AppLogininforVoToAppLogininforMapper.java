package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__394;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.AppLogininfor;
import org.dromara.system.domain.AppLogininforToAppLogininforVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__394.class,
    uses = {AppLogininforToAppLogininforVoMapper.class},
    imports = {}
)
public interface AppLogininforVoToAppLogininforMapper extends BaseMapper<AppLogininforVo, AppLogininfor> {
}
