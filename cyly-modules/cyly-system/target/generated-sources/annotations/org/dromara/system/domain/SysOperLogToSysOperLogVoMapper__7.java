package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysOperLogBoToSysOperLogMapper__7;
import org.dromara.system.domain.vo.SysOperLogVo;
import org.dromara.system.domain.vo.SysOperLogVoToSysOperLogMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysOperLogVoToSysOperLogMapper__7.class,SysOperLogBoToSysOperLogMapper__7.class},
    imports = {}
)
public interface SysOperLogToSysOperLogVoMapper__7 extends BaseMapper<SysOperLog, SysOperLogVo> {
}
