package org.dromara.system.domain;

import javax.annotation.processing.Generated;
import org.dromara.system.domain.vo.DictAreaFullVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T09:53:54+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class DictAreaToDictAreaFullVoMapper__7Impl implements DictAreaToDictAreaFullVoMapper__7 {

    @Override
    public DictAreaFullVo convert(DictArea arg0) {
        if ( arg0 == null ) {
            return null;
        }

        DictAreaFullVo dictAreaFullVo = new DictAreaFullVo();

        dictAreaFullVo.setAncestors( arg0.getAncestors() );
        dictAreaFullVo.setAreaId( arg0.getAreaId() );
        dictAreaFullVo.setAreaName( arg0.getAreaName() );
        dictAreaFullVo.setHigherAreaName( arg0.getHigherAreaName() );
        if ( arg0.getLevel() != null ) {
            dictAreaFullVo.setLevel( arg0.getLevel().intValue() );
        }
        dictAreaFullVo.setParentId( arg0.getParentId() );

        return dictAreaFullVo;
    }

    @Override
    public DictAreaFullVo convert(DictArea arg0, DictAreaFullVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setAncestors( arg0.getAncestors() );
        arg1.setAreaId( arg0.getAreaId() );
        arg1.setAreaName( arg0.getAreaName() );
        arg1.setHigherAreaName( arg0.getHigherAreaName() );
        if ( arg0.getLevel() != null ) {
            arg1.setLevel( arg0.getLevel().intValue() );
        }
        else {
            arg1.setLevel( null );
        }
        arg1.setParentId( arg0.getParentId() );

        return arg1;
    }
}
