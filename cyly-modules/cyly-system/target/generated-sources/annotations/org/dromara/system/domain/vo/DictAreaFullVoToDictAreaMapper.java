package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__394;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.DictArea;
import org.dromara.system.domain.DictAreaToDictAreaFullVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__394.class,
    uses = {DictAreaToDictAreaFullVoMapper.class},
    imports = {}
)
public interface DictAreaFullVoToDictAreaMapper extends BaseMapper<DictAreaFullVo, DictArea> {
}
