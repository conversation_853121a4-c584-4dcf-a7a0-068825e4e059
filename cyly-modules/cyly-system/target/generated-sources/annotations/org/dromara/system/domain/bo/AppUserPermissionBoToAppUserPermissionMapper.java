package org.dromara.system.domain.bo;

import io.github.linpeilie.AutoMapperConfig__394;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.AppUserPermission;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__394.class,
    uses = {},
    imports = {}
)
public interface AppUserPermissionBoToAppUserPermissionMapper extends BaseMapper<AppUserPermissionBo, AppUserPermission> {
}
