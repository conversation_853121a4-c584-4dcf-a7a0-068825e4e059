package org.dromara.system.domain;

import javax.annotation.processing.Generated;
import org.dromara.system.domain.vo.AppLogininforVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T09:20:50+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AppLogininforToAppLogininforVoMapper__6Impl implements AppLogininforToAppLogininforVoMapper__6 {

    @Override
    public AppLogininforVo convert(AppLogininfor arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppLogininforVo appLogininforVo = new AppLogininforVo();

        appLogininforVo.setBrowser( arg0.getBrowser() );
        appLogininforVo.setClientKey( arg0.getClientKey() );
        appLogininforVo.setDeviceType( arg0.getDeviceType() );
        appLogininforVo.setInfoId( arg0.getInfoId() );
        appLogininforVo.setIpaddr( arg0.getIpaddr() );
        appLogininforVo.setLoginLocation( arg0.getLoginLocation() );
        appLogininforVo.setLoginTime( arg0.getLoginTime() );
        appLogininforVo.setMsg( arg0.getMsg() );
        appLogininforVo.setOs( arg0.getOs() );
        appLogininforVo.setStatus( arg0.getStatus() );
        appLogininforVo.setTenantId( arg0.getTenantId() );
        appLogininforVo.setUserName( arg0.getUserName() );

        return appLogininforVo;
    }

    @Override
    public AppLogininforVo convert(AppLogininfor arg0, AppLogininforVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setBrowser( arg0.getBrowser() );
        arg1.setClientKey( arg0.getClientKey() );
        arg1.setDeviceType( arg0.getDeviceType() );
        arg1.setInfoId( arg0.getInfoId() );
        arg1.setIpaddr( arg0.getIpaddr() );
        arg1.setLoginLocation( arg0.getLoginLocation() );
        arg1.setLoginTime( arg0.getLoginTime() );
        arg1.setMsg( arg0.getMsg() );
        arg1.setOs( arg0.getOs() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setTenantId( arg0.getTenantId() );
        arg1.setUserName( arg0.getUserName() );

        return arg1;
    }
}
