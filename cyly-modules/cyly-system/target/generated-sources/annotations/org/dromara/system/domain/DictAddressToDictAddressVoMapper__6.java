package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.DictAddressBoToDictAddressMapper__6;
import org.dromara.system.domain.vo.DictAddressVo;
import org.dromara.system.domain.vo.DictAddressVoToDictAddressMapper__6;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {DictAddressVoToDictAddressMapper__6.class,DictAddressBoToDictAddressMapper__6.class},
    imports = {}
)
public interface DictAddressToDictAddressVoMapper__6 extends BaseMapper<DictAddress, DictAddressVo> {
}
