package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysUserAuthBoToSysUserAuthMapper__6;
import org.dromara.system.domain.vo.SysUserAuthVo;
import org.dromara.system.domain.vo.SysUserAuthVoToSysUserAuthMapper__6;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysUserAuthVoToSysUserAuthMapper__6.class,SysUserAuthBoToSysUserAuthMapper__6.class},
    imports = {}
)
public interface SysUserAuthToSysUserAuthVoMapper__6 extends BaseMapper<SysUserAuth, SysUserAuthVo> {
}
