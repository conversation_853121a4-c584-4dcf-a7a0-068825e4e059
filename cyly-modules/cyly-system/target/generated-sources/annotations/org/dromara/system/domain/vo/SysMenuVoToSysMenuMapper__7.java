package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysMenu;
import org.dromara.system.domain.SysMenuToSysMenuVoMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysMenuToSysMenuVoMapper__7.class,SysMenuToSysMenuVoMapper__7.class},
    imports = {}
)
public interface SysMenuVoToSysMenuMapper__7 extends BaseMapper<SysMenuVo, SysMenu> {
}
