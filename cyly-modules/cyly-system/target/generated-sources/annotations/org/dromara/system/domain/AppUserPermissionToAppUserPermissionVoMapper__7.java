package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.AppUserPermissionBoToAppUserPermissionMapper__7;
import org.dromara.system.domain.vo.AppUserPermissionVo;
import org.dromara.system.domain.vo.AppUserPermissionVoToAppUserPermissionMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {AppUserPermissionVoToAppUserPermissionMapper__7.class,AppUserPermissionBoToAppUserPermissionMapper__7.class},
    imports = {}
)
public interface AppUserPermissionToAppUserPermissionVoMapper__7 extends BaseMapper<AppUserPermission, AppUserPermissionVo> {
}
