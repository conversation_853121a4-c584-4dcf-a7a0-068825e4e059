package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysLogininforBoToSysLogininforMapper__7;
import org.dromara.system.domain.vo.SysLogininforVo;
import org.dromara.system.domain.vo.SysLogininforVoToSysLogininforMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysLogininforBoToSysLogininforMapper__7.class,SysLogininforVoToSysLogininforMapper__7.class},
    imports = {}
)
public interface SysLogininforToSysLogininforVoMapper__7 extends BaseMapper<SysLogininfor, SysLogininforVo> {
}
