package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__388;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.AppLogininforBoToAppLogininforMapper__1;
import org.dromara.system.domain.vo.AppLogininforVo;
import org.dromara.system.domain.vo.AppLogininforVoToAppLogininforMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__388.class,
    uses = {AppLogininforVoToAppLogininforMapper__1.class,AppLogininforBoToAppLogininforMapper__1.class},
    imports = {}
)
public interface AppLogininforToAppLogininforVoMapper__1 extends BaseMapper<AppLogininfor, AppLogininforVo> {
}
