package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.AppLogininforBoToAppLogininforMapper__7;
import org.dromara.system.domain.vo.AppLogininforVo;
import org.dromara.system.domain.vo.AppLogininforVoToAppLogininforMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {AppLogininforVoToAppLogininforMapper__7.class,AppLogininforBoToAppLogininforMapper__7.class},
    imports = {}
)
public interface AppLogininforToAppLogininforVoMapper__7 extends BaseMapper<AppLogininfor, AppLogininforVo> {
}
