package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__394;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.DictAddress;
import org.dromara.system.domain.DictAddressToDictAddressVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__394.class,
    uses = {DictAddressToDictAddressVoMapper.class},
    imports = {}
)
public interface DictAddressVoToDictAddressMapper extends BaseMapper<DictAddressVo, DictAddress> {
}
