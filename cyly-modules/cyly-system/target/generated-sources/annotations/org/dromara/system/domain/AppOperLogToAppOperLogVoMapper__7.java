package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.AppOperLogBoToAppOperLogMapper__7;
import org.dromara.system.domain.vo.AppOperLogVo;
import org.dromara.system.domain.vo.AppOperLogVoToAppOperLogMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {AppOperLogVoToAppOperLogMapper__7.class,AppOperLogBoToAppOperLogMapper__7.class},
    imports = {}
)
public interface AppOperLogToAppOperLogVoMapper__7 extends BaseMapper<AppOperLog, AppOperLogVo> {
}
