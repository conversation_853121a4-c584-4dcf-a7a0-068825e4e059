package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.DictAreaBoToDictAreaMapper__7;
import org.dromara.system.domain.vo.DictAreaFullVo;
import org.dromara.system.domain.vo.DictAreaFullVoToDictAreaMapper__7;
import org.dromara.system.domain.vo.DictAreaVoToDictAreaMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {DictAreaBoToDictAreaMapper__7.class,DictAreaVoToDictAreaMapper__7.class,DictAreaFullVoToDictAreaMapper__7.class,DictAreaToDictAreaVoMapper__7.class},
    imports = {}
)
public interface DictAreaToDictAreaFullVoMapper__7 extends BaseMapper<DictArea, DictAreaFullVo> {
}
