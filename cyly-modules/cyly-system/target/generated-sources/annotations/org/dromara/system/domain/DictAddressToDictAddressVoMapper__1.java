package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__388;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.DictAddressBoToDictAddressMapper__1;
import org.dromara.system.domain.vo.DictAddressVo;
import org.dromara.system.domain.vo.DictAddressVoToDictAddressMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__388.class,
    uses = {DictAddressVoToDictAddressMapper__1.class,DictAddressBoToDictAddressMapper__1.class},
    imports = {}
)
public interface DictAddressToDictAddressVoMapper__1 extends BaseMapper<DictAddress, DictAddressVo> {
}
