package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__394;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysIpBanBoToSysIpBanMapper;
import org.dromara.system.domain.vo.SysIpBanVo;
import org.dromara.system.domain.vo.SysIpBanVoToSysIpBanMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__394.class,
    uses = {SysIpBanVoToSysIpBanMapper.class,SysIpBanBoToSysIpBanMapper.class},
    imports = {}
)
public interface SysIpBanToSysIpBanVoMapper extends BaseMapper<SysIpBan, SysIpBanVo> {
}
