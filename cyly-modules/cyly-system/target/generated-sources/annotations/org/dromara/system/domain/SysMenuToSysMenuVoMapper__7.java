package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysMenuBoToSysMenuMapper__7;
import org.dromara.system.domain.vo.SysMenuVo;
import org.dromara.system.domain.vo.SysMenuVoToSysMenuMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysMenuVoToSysMenuMapper__7.class,SysMenuBoToSysMenuMapper__7.class},
    imports = {}
)
public interface SysMenuToSysMenuVoMapper__7 extends BaseMapper<SysMenu, SysMenuVo> {
}
