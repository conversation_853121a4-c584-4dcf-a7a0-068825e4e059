package org.dromara.system.domain;

import javax.annotation.processing.Generated;
import org.dromara.system.domain.vo.SysVersionVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T09:20:25+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class SysVersionToSysVersionVoMapper__1Impl implements SysVersionToSysVersionVoMapper__1 {

    @Override
    public SysVersionVo convert(SysVersion arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysVersionVo sysVersionVo = new SysVersionVo();

        sysVersionVo.setId( arg0.getId() );
        sysVersionVo.setSort( arg0.getSort() );
        sysVersionVo.setVersionCode( arg0.getVersionCode() );
        sysVersionVo.setVersionDetails( arg0.getVersionDetails() );
        sysVersionVo.setVersionName( arg0.getVersionName() );
        sysVersionVo.setVersionUrl( arg0.getVersionUrl() );

        return sysVersionVo;
    }

    @Override
    public SysVersionVo convert(SysVersion arg0, SysVersionVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setSort( arg0.getSort() );
        arg1.setVersionCode( arg0.getVersionCode() );
        arg1.setVersionDetails( arg0.getVersionDetails() );
        arg1.setVersionName( arg0.getVersionName() );
        arg1.setVersionUrl( arg0.getVersionUrl() );

        return arg1;
    }
}
