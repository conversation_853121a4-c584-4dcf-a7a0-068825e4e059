package org.dromara.system.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.system.domain.DictAddress;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T09:20:47+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class DictAddressBoToDictAddressMapper__6Impl implements DictAddressBoToDictAddressMapper__6 {

    @Override
    public DictAddress convert(DictAddressBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        DictAddress dictAddress = new DictAddress();

        dictAddress.setCreateBy( arg0.getCreateBy() );
        dictAddress.setCreateDept( arg0.getCreateDept() );
        dictAddress.setCreateTime( arg0.getCreateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            dictAddress.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        dictAddress.setSearchValue( arg0.getSearchValue() );
        dictAddress.setUpdateBy( arg0.getUpdateBy() );
        dictAddress.setUpdateTime( arg0.getUpdateTime() );
        dictAddress.setCode( arg0.getCode() );
        dictAddress.setFullName( arg0.getFullName() );
        dictAddress.setId( arg0.getId() );
        dictAddress.setLevel( arg0.getLevel() );
        dictAddress.setName( arg0.getName() );
        dictAddress.setParentId( arg0.getParentId() );
        dictAddress.setStatus( arg0.getStatus() );

        return dictAddress;
    }

    @Override
    public DictAddress convert(DictAddressBo arg0, DictAddress arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateTime( arg0.getCreateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setCode( arg0.getCode() );
        arg1.setFullName( arg0.getFullName() );
        arg1.setId( arg0.getId() );
        arg1.setLevel( arg0.getLevel() );
        arg1.setName( arg0.getName() );
        arg1.setParentId( arg0.getParentId() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
