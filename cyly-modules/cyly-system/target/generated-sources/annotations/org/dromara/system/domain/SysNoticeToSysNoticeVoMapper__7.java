package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysNoticeBoToSysNoticeMapper__7;
import org.dromara.system.domain.vo.SysNoticeVo;
import org.dromara.system.domain.vo.SysNoticeVoToSysNoticeMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysNoticeVoToSysNoticeMapper__7.class,SysNoticeBoToSysNoticeMapper__7.class},
    imports = {}
)
public interface SysNoticeToSysNoticeVoMapper__7 extends BaseMapper<SysNotice, SysNoticeVo> {
}
