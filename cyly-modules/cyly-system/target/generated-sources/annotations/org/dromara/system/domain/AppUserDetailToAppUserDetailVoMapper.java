package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__394;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.AppUserDetailBoToAppUserDetailMapper;
import org.dromara.system.domain.vo.AppUserDetailVo;
import org.dromara.system.domain.vo.AppUserDetailVoToAppUserDetailMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__394.class,
    uses = {AppUserDetailVoToAppUserDetailMapper.class,AppUserDetailBoToAppUserDetailMapper.class},
    imports = {}
)
public interface AppUserDetailToAppUserDetailVoMapper extends BaseMapper<AppUserDetail, AppUserDetailVo> {
}
