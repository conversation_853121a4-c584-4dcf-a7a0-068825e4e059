package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysTenantBoToSysTenantMapper__7;
import org.dromara.system.domain.vo.SysTenantVo;
import org.dromara.system.domain.vo.SysTenantVoToSysTenantMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysTenantBoToSysTenantMapper__7.class,SysTenantVoToSysTenantMapper__7.class},
    imports = {}
)
public interface SysTenantToSysTenantVoMapper__7 extends BaseMapper<SysTenant, SysTenantVo> {
}
