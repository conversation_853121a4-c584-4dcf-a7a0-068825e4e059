package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysDeptBoToSysDeptMapper__7;
import org.dromara.system.domain.vo.SysDeptVo;
import org.dromara.system.domain.vo.SysDeptVoToSysDeptMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysDeptVoToSysDeptMapper__7.class,SysDeptBoToSysDeptMapper__7.class},
    imports = {}
)
public interface SysDeptToSysDeptVoMapper__7 extends BaseMapper<SysDept, SysDeptVo> {
}
