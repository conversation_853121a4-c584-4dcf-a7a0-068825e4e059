package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysClientBoToSysClientMapper__7;
import org.dromara.system.domain.vo.SysClientVo;
import org.dromara.system.domain.vo.SysClientVoToSysClientMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysClientVoToSysClientMapper__7.class,SysClientBoToSysClientMapper__7.class},
    imports = {}
)
public interface SysClientToSysClientVoMapper__7 extends BaseMapper<SysClient, SysClientVo> {
}
