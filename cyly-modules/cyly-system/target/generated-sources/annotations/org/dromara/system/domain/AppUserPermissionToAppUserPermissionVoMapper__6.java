package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.AppUserPermissionBoToAppUserPermissionMapper__6;
import org.dromara.system.domain.vo.AppUserPermissionVo;
import org.dromara.system.domain.vo.AppUserPermissionVoToAppUserPermissionMapper__6;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {AppUserPermissionVoToAppUserPermissionMapper__6.class,AppUserPermissionBoToAppUserPermissionMapper__6.class},
    imports = {}
)
public interface AppUserPermissionToAppUserPermissionVoMapper__6 extends BaseMapper<AppUserPermission, AppUserPermissionVo> {
}
