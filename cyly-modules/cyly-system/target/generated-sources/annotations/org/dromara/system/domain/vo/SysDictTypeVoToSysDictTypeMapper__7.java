package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysDictType;
import org.dromara.system.domain.SysDictTypeToSysDictTypeVoMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysDictTypeToSysDictTypeVoMapper__7.class},
    imports = {}
)
public interface SysDictTypeVoToSysDictTypeMapper__7 extends BaseMapper<SysDictTypeVo, SysDictType> {
}
