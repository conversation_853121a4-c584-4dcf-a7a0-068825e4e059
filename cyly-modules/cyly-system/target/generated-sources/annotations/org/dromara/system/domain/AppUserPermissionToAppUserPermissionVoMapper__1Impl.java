package org.dromara.system.domain;

import javax.annotation.processing.Generated;
import org.dromara.system.domain.vo.AppUserPermissionVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T09:20:30+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AppUserPermissionToAppUserPermissionVoMapper__1Impl implements AppUserPermissionToAppUserPermissionVoMapper__1 {

    @Override
    public AppUserPermissionVo convert(AppUserPermission arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppUserPermissionVo appUserPermissionVo = new AppUserPermissionVo();

        appUserPermissionVo.setAppUserId( arg0.getAppUserId() );
        appUserPermissionVo.setDeptId( arg0.getDeptId() );
        appUserPermissionVo.setId( arg0.getId() );
        appUserPermissionVo.setRoleId( arg0.getRoleId() );

        return appUserPermissionVo;
    }

    @Override
    public AppUserPermissionVo convert(AppUserPermission arg0, AppUserPermissionVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setAppUserId( arg0.getAppUserId() );
        arg1.setDeptId( arg0.getDeptId() );
        arg1.setId( arg0.getId() );
        arg1.setRoleId( arg0.getRoleId() );

        return arg1;
    }
}
