package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__394;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysIpBan;
import org.dromara.system.domain.SysIpBanToSysIpBanVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__394.class,
    uses = {SysIpBanToSysIpBanVoMapper.class},
    imports = {}
)
public interface SysIpBanVoToSysIpBanMapper extends BaseMapper<SysIpBanVo, SysIpBan> {
}
