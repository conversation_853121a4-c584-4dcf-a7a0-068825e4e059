package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysSocial;
import org.dromara.system.domain.SysSocialToSysSocialVoMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysSocialToSysSocialVoMapper__7.class},
    imports = {}
)
public interface SysSocialVoToSysSocialMapper__7 extends BaseMapper<SysSocialVo, SysSocial> {
}
