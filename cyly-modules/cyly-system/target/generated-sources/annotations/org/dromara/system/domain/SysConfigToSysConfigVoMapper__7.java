package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysConfigBoToSysConfigMapper__7;
import org.dromara.system.domain.vo.SysConfigVo;
import org.dromara.system.domain.vo.SysConfigVoToSysConfigMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysConfigVoToSysConfigMapper__7.class,SysConfigBoToSysConfigMapper__7.class},
    imports = {}
)
public interface SysConfigToSysConfigVoMapper__7 extends BaseMapper<SysConfig, SysConfigVo> {
}
