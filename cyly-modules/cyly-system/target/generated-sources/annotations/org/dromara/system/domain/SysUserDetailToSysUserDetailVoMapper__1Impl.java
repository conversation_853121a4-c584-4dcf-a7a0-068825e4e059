package org.dromara.system.domain;

import javax.annotation.processing.Generated;
import org.dromara.system.domain.vo.SysUserDetailVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T09:20:28+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class SysUserDetailToSysUserDetailVoMapper__1Impl implements SysUserDetailToSysUserDetailVoMapper__1 {

    @Override
    public SysUserDetailVo convert(SysUserDetail arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysUserDetailVo sysUserDetailVo = new SysUserDetailVo();

        if ( arg0.getAge() != null ) {
            sysUserDetailVo.setAge( arg0.getAge().longValue() );
        }
        sysUserDetailVo.setBirthday( arg0.getBirthday() );
        sysUserDetailVo.setColleges( arg0.getColleges() );
        sysUserDetailVo.setContactsPhone( arg0.getContactsPhone() );
        sysUserDetailVo.setDegree( arg0.getDegree() );
        sysUserDetailVo.setEmergencyContacts( arg0.getEmergencyContacts() );
        sysUserDetailVo.setEthnic( arg0.getEthnic() );
        sysUserDetailVo.setIdCard( arg0.getIdCard() );
        if ( arg0.getMarriage() != null ) {
            sysUserDetailVo.setMarriage( arg0.getMarriage().longValue() );
        }
        sysUserDetailVo.setOrigin( arg0.getOrigin() );
        sysUserDetailVo.setRemark( arg0.getRemark() );
        sysUserDetailVo.setSkill( arg0.getSkill() );
        sysUserDetailVo.setSpecialized( arg0.getSpecialized() );
        sysUserDetailVo.setStatus( arg0.getStatus() );
        sysUserDetailVo.setUserId( arg0.getUserId() );
        sysUserDetailVo.setWorkExp( arg0.getWorkExp() );

        return sysUserDetailVo;
    }

    @Override
    public SysUserDetailVo convert(SysUserDetail arg0, SysUserDetailVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        if ( arg0.getAge() != null ) {
            arg1.setAge( arg0.getAge().longValue() );
        }
        else {
            arg1.setAge( null );
        }
        arg1.setBirthday( arg0.getBirthday() );
        arg1.setColleges( arg0.getColleges() );
        arg1.setContactsPhone( arg0.getContactsPhone() );
        arg1.setDegree( arg0.getDegree() );
        arg1.setEmergencyContacts( arg0.getEmergencyContacts() );
        arg1.setEthnic( arg0.getEthnic() );
        arg1.setIdCard( arg0.getIdCard() );
        if ( arg0.getMarriage() != null ) {
            arg1.setMarriage( arg0.getMarriage().longValue() );
        }
        else {
            arg1.setMarriage( null );
        }
        arg1.setOrigin( arg0.getOrigin() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setSkill( arg0.getSkill() );
        arg1.setSpecialized( arg0.getSpecialized() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setWorkExp( arg0.getWorkExp() );

        return arg1;
    }
}
