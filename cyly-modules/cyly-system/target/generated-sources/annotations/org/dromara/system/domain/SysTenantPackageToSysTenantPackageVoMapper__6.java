package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysTenantPackageBoToSysTenantPackageMapper__6;
import org.dromara.system.domain.vo.SysTenantPackageVo;
import org.dromara.system.domain.vo.SysTenantPackageVoToSysTenantPackageMapper__6;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysTenantPackageBoToSysTenantPackageMapper__6.class,SysTenantPackageVoToSysTenantPackageMapper__6.class},
    imports = {}
)
public interface SysTenantPackageToSysTenantPackageVoMapper__6 extends BaseMapper<SysTenantPackage, SysTenantPackageVo> {
}
