package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.DictAddressBoToDictAddressMapper__7;
import org.dromara.system.domain.vo.DictAddressVo;
import org.dromara.system.domain.vo.DictAddressVoToDictAddressMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {DictAddressVoToDictAddressMapper__7.class,DictAddressBoToDictAddressMapper__7.class},
    imports = {}
)
public interface DictAddressToDictAddressVoMapper__7 extends BaseMapper<DictAddress, DictAddressVo> {
}
