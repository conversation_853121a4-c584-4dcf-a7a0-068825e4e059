package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__394;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.AppUser;
import org.dromara.system.domain.AppUserToAppUserVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__394.class,
    uses = {AppUserToAppUserVoMapper.class},
    imports = {}
)
public interface AppUserVoToAppUserMapper extends BaseMapper<AppUserVo, AppUser> {
}
