package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysIpBanBoToSysIpBanMapper__7;
import org.dromara.system.domain.vo.SysIpBanVo;
import org.dromara.system.domain.vo.SysIpBanVoToSysIpBanMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysIpBanVoToSysIpBanMapper__7.class,SysIpBanBoToSysIpBanMapper__7.class},
    imports = {}
)
public interface SysIpBanToSysIpBanVoMapper__7 extends BaseMapper<SysIpBan, SysIpBanVo> {
}
