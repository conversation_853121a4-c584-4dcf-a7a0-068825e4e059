package org.dromara.system.domain;

import javax.annotation.processing.Generated;
import org.dromara.system.domain.vo.SysIpBanVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T09:53:40+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class SysIpBanToSysIpBanVoMapperImpl implements SysIpBanToSysIpBanVoMapper {

    @Override
    public SysIpBanVo convert(SysIpBan arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysIpBanVo sysIpBanVo = new SysIpBanVo();

        sysIpBanVo.setBanEnd( arg0.getBanEnd() );
        sysIpBanVo.setBanId( arg0.getBanId() );
        sysIpBanVo.setBanReason( arg0.getBanReason() );
        sysIpBanVo.setBanStart( arg0.getBanStart() );
        sysIpBanVo.setBanStatus( arg0.getBanStatus() );
        sysIpBanVo.setIpAddress( arg0.getIpAddress() );
        sysIpBanVo.setIpNetwork( arg0.getIpNetwork() );

        return sysIpBanVo;
    }

    @Override
    public SysIpBanVo convert(SysIpBan arg0, SysIpBanVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setBanEnd( arg0.getBanEnd() );
        arg1.setBanId( arg0.getBanId() );
        arg1.setBanReason( arg0.getBanReason() );
        arg1.setBanStart( arg0.getBanStart() );
        arg1.setBanStatus( arg0.getBanStatus() );
        arg1.setIpAddress( arg0.getIpAddress() );
        arg1.setIpNetwork( arg0.getIpNetwork() );

        return arg1;
    }
}
