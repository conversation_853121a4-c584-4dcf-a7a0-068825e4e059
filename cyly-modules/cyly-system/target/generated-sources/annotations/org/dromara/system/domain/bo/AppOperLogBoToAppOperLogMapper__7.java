package org.dromara.system.domain.bo;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.common.log.event.OperLogEventToAppOperLogBoMapper__7;
import org.dromara.system.domain.AppOperLog;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {AppOperLogBoToOperLogEventMapper__7.class,OperLogEventToAppOperLogBoMapper__7.class},
    imports = {}
)
public interface AppOperLogBoToAppOperLogMapper__7 extends BaseMapper<AppOperLogBo, AppOperLog> {
}
