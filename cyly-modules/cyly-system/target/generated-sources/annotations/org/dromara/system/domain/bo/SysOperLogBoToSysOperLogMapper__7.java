package org.dromara.system.domain.bo;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.common.log.event.OperLogEventToSysOperLogBoMapper__7;
import org.dromara.system.domain.SysOperLog;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysOperLogBoToOperLogEventMapper__7.class,OperLogEventToSysOperLogBoMapper__7.class},
    imports = {}
)
public interface SysOperLogBoToSysOperLogMapper__7 extends BaseMapper<SysOperLogBo, SysOperLog> {
}
