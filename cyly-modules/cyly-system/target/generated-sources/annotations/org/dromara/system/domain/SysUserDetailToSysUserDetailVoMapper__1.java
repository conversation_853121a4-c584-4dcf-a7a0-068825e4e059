package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__388;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysUserDetailBoToSysUserDetailMapper__1;
import org.dromara.system.domain.vo.SysUserDetailVo;
import org.dromara.system.domain.vo.SysUserDetailVoToSysUserDetailMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__388.class,
    uses = {SysUserDetailVoToSysUserDetailMapper__1.class,SysUserDetailBoToSysUserDetailMapper__1.class},
    imports = {}
)
public interface SysUserDetailToSysUserDetailVoMapper__1 extends BaseMapper<SysUserDetail, SysUserDetailVo> {
}
