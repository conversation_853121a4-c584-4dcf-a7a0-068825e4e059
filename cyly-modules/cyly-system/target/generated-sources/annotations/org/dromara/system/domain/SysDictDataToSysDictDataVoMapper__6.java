package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysDictDataBoToSysDictDataMapper__6;
import org.dromara.system.domain.vo.SysDictDataVo;
import org.dromara.system.domain.vo.SysDictDataVoToSysDictDataMapper__6;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysDictDataBoToSysDictDataMapper__6.class,SysDictDataVoToSysDictDataMapper__6.class},
    imports = {}
)
public interface SysDictDataToSysDictDataVoMapper__6 extends BaseMapper<SysDictData, SysDictDataVo> {
}
