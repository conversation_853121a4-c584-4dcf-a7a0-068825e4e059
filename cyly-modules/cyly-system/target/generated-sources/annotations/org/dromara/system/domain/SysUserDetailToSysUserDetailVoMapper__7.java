package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysUserDetailBoToSysUserDetailMapper__7;
import org.dromara.system.domain.vo.SysUserDetailVo;
import org.dromara.system.domain.vo.SysUserDetailVoToSysUserDetailMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysUserDetailVoToSysUserDetailMapper__7.class,SysUserDetailBoToSysUserDetailMapper__7.class},
    imports = {}
)
public interface SysUserDetailToSysUserDetailVoMapper__7 extends BaseMapper<SysUserDetail, SysUserDetailVo> {
}
