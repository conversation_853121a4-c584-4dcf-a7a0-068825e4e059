package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysDictDataBoToSysDictDataMapper__7;
import org.dromara.system.domain.vo.SysDictDataVo;
import org.dromara.system.domain.vo.SysDictDataVoToSysDictDataMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysDictDataBoToSysDictDataMapper__7.class,SysDictDataVoToSysDictDataMapper__7.class},
    imports = {}
)
public interface SysDictDataToSysDictDataVoMapper__7 extends BaseMapper<SysDictData, SysDictDataVo> {
}
