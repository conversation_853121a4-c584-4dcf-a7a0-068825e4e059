package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysSocialBoToSysSocialMapper__7;
import org.dromara.system.domain.vo.SysSocialVo;
import org.dromara.system.domain.vo.SysSocialVoToSysSocialMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysSocialBoToSysSocialMapper__7.class,SysSocialVoToSysSocialMapper__7.class},
    imports = {}
)
public interface SysSocialToSysSocialVoMapper__7 extends BaseMapper<SysSocial, SysSocialVo> {
}
