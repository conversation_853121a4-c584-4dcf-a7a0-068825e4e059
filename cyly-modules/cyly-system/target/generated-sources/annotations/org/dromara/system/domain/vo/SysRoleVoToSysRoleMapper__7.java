package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysRole;
import org.dromara.system.domain.SysRoleToSysRoleVoMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysRoleToSysRoleVoMapper__7.class},
    imports = {}
)
public interface SysRoleVoToSysRoleMapper__7 extends BaseMapper<SysRoleVo, SysRole> {
}
