package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__394;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.AppUserDetail;
import org.dromara.system.domain.AppUserDetailToAppUserDetailVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__394.class,
    uses = {AppUserDetailToAppUserDetailVoMapper.class},
    imports = {}
)
public interface AppUserDetailVoToAppUserDetailMapper extends BaseMapper<AppUserDetailVo, AppUserDetail> {
}
