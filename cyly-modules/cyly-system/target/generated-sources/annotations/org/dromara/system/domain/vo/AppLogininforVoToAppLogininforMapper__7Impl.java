package org.dromara.system.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.system.domain.AppLogininfor;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T09:53:46+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AppLogininforVoToAppLogininforMapper__7Impl implements AppLogininforVoToAppLogininforMapper__7 {

    @Override
    public AppLogininfor convert(AppLogininforVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppLogininfor appLogininfor = new AppLogininfor();

        appLogininfor.setBrowser( arg0.getBrowser() );
        appLogininfor.setClientKey( arg0.getClientKey() );
        appLogininfor.setDeviceType( arg0.getDeviceType() );
        appLogininfor.setInfoId( arg0.getInfoId() );
        appLogininfor.setIpaddr( arg0.getIpaddr() );
        appLogininfor.setLoginLocation( arg0.getLoginLocation() );
        appLogininfor.setLoginTime( arg0.getLoginTime() );
        appLogininfor.setMsg( arg0.getMsg() );
        appLogininfor.setOs( arg0.getOs() );
        appLogininfor.setStatus( arg0.getStatus() );
        appLogininfor.setTenantId( arg0.getTenantId() );
        appLogininfor.setUserName( arg0.getUserName() );

        return appLogininfor;
    }

    @Override
    public AppLogininfor convert(AppLogininforVo arg0, AppLogininfor arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setBrowser( arg0.getBrowser() );
        arg1.setClientKey( arg0.getClientKey() );
        arg1.setDeviceType( arg0.getDeviceType() );
        arg1.setInfoId( arg0.getInfoId() );
        arg1.setIpaddr( arg0.getIpaddr() );
        arg1.setLoginLocation( arg0.getLoginLocation() );
        arg1.setLoginTime( arg0.getLoginTime() );
        arg1.setMsg( arg0.getMsg() );
        arg1.setOs( arg0.getOs() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setTenantId( arg0.getTenantId() );
        arg1.setUserName( arg0.getUserName() );

        return arg1;
    }
}
