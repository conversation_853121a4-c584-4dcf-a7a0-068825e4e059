package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__394;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysUserTypeBoToSysUserTypeMapper;
import org.dromara.system.domain.vo.SysUserTypeVo;
import org.dromara.system.domain.vo.SysUserTypeVoToSysUserTypeMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__394.class,
    uses = {SysUserTypeBoToSysUserTypeMapper.class,SysUserTypeVoToSysUserTypeMapper.class},
    imports = {}
)
public interface SysUserTypeToSysUserTypeVoMapper extends BaseMapper<SysUserType, SysUserTypeVo> {
}
