package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysVersionBoToSysVersionMapper__7;
import org.dromara.system.domain.vo.SysVersionVo;
import org.dromara.system.domain.vo.SysVersionVoToSysVersionMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysVersionBoToSysVersionMapper__7.class,SysVersionVoToSysVersionMapper__7.class},
    imports = {}
)
public interface SysVersionToSysVersionVoMapper__7 extends BaseMapper<SysVersion, SysVersionVo> {
}
