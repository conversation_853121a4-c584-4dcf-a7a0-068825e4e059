package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysTenantPackageBoToSysTenantPackageMapper__7;
import org.dromara.system.domain.vo.SysTenantPackageVo;
import org.dromara.system.domain.vo.SysTenantPackageVoToSysTenantPackageMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysTenantPackageBoToSysTenantPackageMapper__7.class,SysTenantPackageVoToSysTenantPackageMapper__7.class},
    imports = {}
)
public interface SysTenantPackageToSysTenantPackageVoMapper__7 extends BaseMapper<SysTenantPackage, SysTenantPackageVo> {
}
