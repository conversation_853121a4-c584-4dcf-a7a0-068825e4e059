package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysUserTypeBoToSysUserTypeMapper__6;
import org.dromara.system.domain.vo.SysUserTypeVo;
import org.dromara.system.domain.vo.SysUserTypeVoToSysUserTypeMapper__6;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysUserTypeBoToSysUserTypeMapper__6.class,SysUserTypeVoToSysUserTypeMapper__6.class},
    imports = {}
)
public interface SysUserTypeToSysUserTypeVoMapper__6 extends BaseMapper<SysUserType, SysUserTypeVo> {
}
