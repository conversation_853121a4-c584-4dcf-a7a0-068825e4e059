package org.dromara.system.domain;

import javax.annotation.processing.Generated;
import org.dromara.system.domain.vo.AppUserVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T09:53:39+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AppUserToAppUserVoMapperImpl implements AppUserToAppUserVoMapper {

    @Override
    public AppUserVo convert(AppUser arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppUserVo appUserVo = new AppUserVo();

        appUserVo.setAvatar( arg0.getAvatar() );
        appUserVo.setChatIdentity( arg0.getChatIdentity() );
        appUserVo.setCreateTime( arg0.getCreateTime() );
        appUserVo.setDeptId( arg0.getDeptId() );
        appUserVo.setEmail( arg0.getEmail() );
        appUserVo.setFaceUrl( arg0.getFaceUrl() );
        appUserVo.setLoginDate( arg0.getLoginDate() );
        appUserVo.setLoginIp( arg0.getLoginIp() );
        appUserVo.setNickName( arg0.getNickName() );
        appUserVo.setPassword( arg0.getPassword() );
        appUserVo.setPhoneNumber( arg0.getPhoneNumber() );
        appUserVo.setRemark( arg0.getRemark() );
        appUserVo.setRoleId( arg0.getRoleId() );
        appUserVo.setSalt( arg0.getSalt() );
        appUserVo.setSex( arg0.getSex() );
        appUserVo.setStatus( arg0.getStatus() );
        appUserVo.setSystemType( arg0.getSystemType() );
        appUserVo.setTaskId( arg0.getTaskId() );
        appUserVo.setTenantId( arg0.getTenantId() );
        appUserVo.setUserId( arg0.getUserId() );
        appUserVo.setUserName( arg0.getUserName() );
        appUserVo.setUserType( arg0.getUserType() );

        return appUserVo;
    }

    @Override
    public AppUserVo convert(AppUser arg0, AppUserVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setAvatar( arg0.getAvatar() );
        arg1.setChatIdentity( arg0.getChatIdentity() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setDeptId( arg0.getDeptId() );
        arg1.setEmail( arg0.getEmail() );
        arg1.setFaceUrl( arg0.getFaceUrl() );
        arg1.setLoginDate( arg0.getLoginDate() );
        arg1.setLoginIp( arg0.getLoginIp() );
        arg1.setNickName( arg0.getNickName() );
        arg1.setPassword( arg0.getPassword() );
        arg1.setPhoneNumber( arg0.getPhoneNumber() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setRoleId( arg0.getRoleId() );
        arg1.setSalt( arg0.getSalt() );
        arg1.setSex( arg0.getSex() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setSystemType( arg0.getSystemType() );
        arg1.setTaskId( arg0.getTaskId() );
        arg1.setTenantId( arg0.getTenantId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setUserName( arg0.getUserName() );
        arg1.setUserType( arg0.getUserType() );

        return arg1;
    }
}
