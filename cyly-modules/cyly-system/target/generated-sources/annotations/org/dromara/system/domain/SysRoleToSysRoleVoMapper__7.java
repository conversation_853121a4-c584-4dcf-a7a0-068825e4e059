package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysRoleBoToSysRoleMapper__7;
import org.dromara.system.domain.vo.SysRoleVo;
import org.dromara.system.domain.vo.SysRoleVoToSysRoleMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysRoleVoToSysRoleMapper__7.class,SysRoleBoToSysRoleMapper__7.class},
    imports = {}
)
public interface SysRoleToSysRoleVoMapper__7 extends BaseMapper<SysRole, SysRoleVo> {
}
