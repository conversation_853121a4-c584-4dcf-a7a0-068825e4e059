package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysUserTypeBoToSysUserTypeMapper__7;
import org.dromara.system.domain.vo.SysUserTypeVo;
import org.dromara.system.domain.vo.SysUserTypeVoToSysUserTypeMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysUserTypeBoToSysUserTypeMapper__7.class,SysUserTypeVoToSysUserTypeMapper__7.class},
    imports = {}
)
public interface SysUserTypeToSysUserTypeVoMapper__7 extends BaseMapper<SysUserType, SysUserTypeVo> {
}
