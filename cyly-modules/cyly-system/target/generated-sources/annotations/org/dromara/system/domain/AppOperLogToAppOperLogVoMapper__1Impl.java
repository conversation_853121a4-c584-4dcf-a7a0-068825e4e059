package org.dromara.system.domain;

import javax.annotation.processing.Generated;
import org.dromara.system.domain.vo.AppOperLogVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T09:20:24+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AppOperLogToAppOperLogVoMapper__1Impl implements AppOperLogToAppOperLogVoMapper__1 {

    @Override
    public AppOperLogVo convert(AppOperLog arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppOperLogVo appOperLogVo = new AppOperLogVo();

        appOperLogVo.setBusinessType( arg0.getBusinessType() );
        appOperLogVo.setCostTime( arg0.getCostTime() );
        appOperLogVo.setDeptName( arg0.getDeptName() );
        appOperLogVo.setErrorMsg( arg0.getErrorMsg() );
        appOperLogVo.setJsonResult( arg0.getJsonResult() );
        appOperLogVo.setMethod( arg0.getMethod() );
        appOperLogVo.setOperId( arg0.getOperId() );
        appOperLogVo.setOperIp( arg0.getOperIp() );
        appOperLogVo.setOperLocation( arg0.getOperLocation() );
        appOperLogVo.setOperName( arg0.getOperName() );
        appOperLogVo.setOperParam( arg0.getOperParam() );
        appOperLogVo.setOperTime( arg0.getOperTime() );
        appOperLogVo.setOperUrl( arg0.getOperUrl() );
        appOperLogVo.setOperatorType( arg0.getOperatorType() );
        appOperLogVo.setRequestMethod( arg0.getRequestMethod() );
        appOperLogVo.setStatus( arg0.getStatus() );
        appOperLogVo.setTenantId( arg0.getTenantId() );
        appOperLogVo.setTitle( arg0.getTitle() );

        return appOperLogVo;
    }

    @Override
    public AppOperLogVo convert(AppOperLog arg0, AppOperLogVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setBusinessType( arg0.getBusinessType() );
        arg1.setCostTime( arg0.getCostTime() );
        arg1.setDeptName( arg0.getDeptName() );
        arg1.setErrorMsg( arg0.getErrorMsg() );
        arg1.setJsonResult( arg0.getJsonResult() );
        arg1.setMethod( arg0.getMethod() );
        arg1.setOperId( arg0.getOperId() );
        arg1.setOperIp( arg0.getOperIp() );
        arg1.setOperLocation( arg0.getOperLocation() );
        arg1.setOperName( arg0.getOperName() );
        arg1.setOperParam( arg0.getOperParam() );
        arg1.setOperTime( arg0.getOperTime() );
        arg1.setOperUrl( arg0.getOperUrl() );
        arg1.setOperatorType( arg0.getOperatorType() );
        arg1.setRequestMethod( arg0.getRequestMethod() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setTenantId( arg0.getTenantId() );
        arg1.setTitle( arg0.getTitle() );

        return arg1;
    }
}
