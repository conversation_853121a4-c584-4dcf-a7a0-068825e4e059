package org.dromara.system.domain;

import javax.annotation.processing.Generated;
import org.dromara.system.domain.vo.SysUserVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T09:53:42+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class SysUserToSysUserVoMapperImpl implements SysUserToSysUserVoMapper {

    @Override
    public SysUserVo convert(SysUser arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysUserVo sysUserVo = new SysUserVo();

        sysUserVo.setAvatar( arg0.getAvatar() );
        sysUserVo.setCreateTime( arg0.getCreateTime() );
        sysUserVo.setDeptId( arg0.getDeptId() );
        sysUserVo.setEmail( arg0.getEmail() );
        sysUserVo.setFaceUrl( arg0.getFaceUrl() );
        sysUserVo.setLoginDate( arg0.getLoginDate() );
        sysUserVo.setLoginIp( arg0.getLoginIp() );
        sysUserVo.setNickName( arg0.getNickName() );
        sysUserVo.setPassword( arg0.getPassword() );
        sysUserVo.setPhoneNumber( arg0.getPhoneNumber() );
        sysUserVo.setRemark( arg0.getRemark() );
        sysUserVo.setSalt( arg0.getSalt() );
        sysUserVo.setSex( arg0.getSex() );
        sysUserVo.setStatus( arg0.getStatus() );
        sysUserVo.setTaskId( arg0.getTaskId() );
        sysUserVo.setTenantId( arg0.getTenantId() );
        sysUserVo.setUserId( arg0.getUserId() );
        sysUserVo.setUserName( arg0.getUserName() );
        sysUserVo.setUserType( arg0.getUserType() );

        return sysUserVo;
    }

    @Override
    public SysUserVo convert(SysUser arg0, SysUserVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setAvatar( arg0.getAvatar() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setDeptId( arg0.getDeptId() );
        arg1.setEmail( arg0.getEmail() );
        arg1.setFaceUrl( arg0.getFaceUrl() );
        arg1.setLoginDate( arg0.getLoginDate() );
        arg1.setLoginIp( arg0.getLoginIp() );
        arg1.setNickName( arg0.getNickName() );
        arg1.setPassword( arg0.getPassword() );
        arg1.setPhoneNumber( arg0.getPhoneNumber() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setSalt( arg0.getSalt() );
        arg1.setSex( arg0.getSex() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setTaskId( arg0.getTaskId() );
        arg1.setTenantId( arg0.getTenantId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setUserName( arg0.getUserName() );
        arg1.setUserType( arg0.getUserType() );

        return arg1;
    }
}
