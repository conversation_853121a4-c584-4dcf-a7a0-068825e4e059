package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__388;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.AppUserPermissionBoToAppUserPermissionMapper__1;
import org.dromara.system.domain.vo.AppUserPermissionVo;
import org.dromara.system.domain.vo.AppUserPermissionVoToAppUserPermissionMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__388.class,
    uses = {AppUserPermissionVoToAppUserPermissionMapper__1.class,AppUserPermissionBoToAppUserPermissionMapper__1.class},
    imports = {}
)
public interface AppUserPermissionToAppUserPermissionVoMapper__1 extends BaseMapper<AppUserPermission, AppUserPermissionVo> {
}
