package org.dromara.system.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.system.domain.SysUserType;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T09:53:42+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class SysUserTypeVoToSysUserTypeMapperImpl implements SysUserTypeVoToSysUserTypeMapper {

    @Override
    public SysUserType convert(SysUserTypeVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysUserType sysUserType = new SysUserType();

        sysUserType.setDeptName( arg0.getDeptName() );
        sysUserType.setParentId( arg0.getParentId() );
        sysUserType.setRemark( arg0.getRemark() );
        sysUserType.setRoleName( arg0.getRoleName() );
        sysUserType.setUserPowerId( arg0.getUserPowerId() );

        return sysUserType;
    }

    @Override
    public SysUserType convert(SysUserTypeVo arg0, SysUserType arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setDeptName( arg0.getDeptName() );
        arg1.setParentId( arg0.getParentId() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setRoleName( arg0.getRoleName() );
        arg1.setUserPowerId( arg0.getUserPowerId() );

        return arg1;
    }
}
