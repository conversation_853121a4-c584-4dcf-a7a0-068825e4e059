package org.dromara.system.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.system.domain.SysVersion;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-03T09:53:41+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class SysVersionBoToSysVersionMapperImpl implements SysVersionBoToSysVersionMapper {

    @Override
    public SysVersion convert(SysVersionBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysVersion sysVersion = new SysVersion();

        sysVersion.setCreateBy( arg0.getCreateBy() );
        sysVersion.setCreateDept( arg0.getCreateDept() );
        sysVersion.setCreateTime( arg0.getCreateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysVersion.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysVersion.setSearchValue( arg0.getSearchValue() );
        sysVersion.setUpdateBy( arg0.getUpdateBy() );
        sysVersion.setUpdateTime( arg0.getUpdateTime() );
        sysVersion.setId( arg0.getId() );
        sysVersion.setSort( arg0.getSort() );
        sysVersion.setVersionCode( arg0.getVersionCode() );
        sysVersion.setVersionDetails( arg0.getVersionDetails() );
        sysVersion.setVersionName( arg0.getVersionName() );
        sysVersion.setVersionUrl( arg0.getVersionUrl() );

        return sysVersion;
    }

    @Override
    public SysVersion convert(SysVersionBo arg0, SysVersion arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateTime( arg0.getCreateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setId( arg0.getId() );
        arg1.setSort( arg0.getSort() );
        arg1.setVersionCode( arg0.getVersionCode() );
        arg1.setVersionDetails( arg0.getVersionDetails() );
        arg1.setVersionName( arg0.getVersionName() );
        arg1.setVersionUrl( arg0.getVersionUrl() );

        return arg1;
    }
}
