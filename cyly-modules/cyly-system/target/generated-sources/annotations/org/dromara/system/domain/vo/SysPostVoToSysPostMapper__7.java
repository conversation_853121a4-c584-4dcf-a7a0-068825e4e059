package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysPost;
import org.dromara.system.domain.SysPostToSysPostVoMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysPostToSysPostVoMapper__7.class},
    imports = {}
)
public interface SysPostVoToSysPostMapper__7 extends BaseMapper<SysPostVo, SysPost> {
}
