package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysVersion;
import org.dromara.system.domain.SysVersionToSysVersionVoMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysVersionToSysVersionVoMapper__7.class},
    imports = {}
)
public interface SysVersionVoToSysVersionMapper__7 extends BaseMapper<SysVersionVo, SysVersion> {
}
