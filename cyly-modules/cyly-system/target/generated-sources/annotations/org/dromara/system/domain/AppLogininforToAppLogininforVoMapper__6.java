package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.AppLogininforBoToAppLogininforMapper__6;
import org.dromara.system.domain.vo.AppLogininforVo;
import org.dromara.system.domain.vo.AppLogininforVoToAppLogininforMapper__6;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {AppLogininforVoToAppLogininforMapper__6.class,AppLogininforBoToAppLogininforMapper__6.class},
    imports = {}
)
public interface AppLogininforToAppLogininforVoMapper__6 extends BaseMapper<AppLogininfor, AppLogininforVo> {
}
