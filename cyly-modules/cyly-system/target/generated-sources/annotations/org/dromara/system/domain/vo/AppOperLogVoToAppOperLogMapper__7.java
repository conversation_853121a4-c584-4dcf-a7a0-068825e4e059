package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.AppOperLog;
import org.dromara.system.domain.AppOperLogToAppOperLogVoMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {AppOperLogToAppOperLogVoMapper__7.class},
    imports = {}
)
public interface AppOperLogVoToAppOperLogMapper__7 extends BaseMapper<AppOperLogVo, AppOperLog> {
}
