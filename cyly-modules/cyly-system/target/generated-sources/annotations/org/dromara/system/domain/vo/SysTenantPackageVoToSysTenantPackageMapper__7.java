package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysTenantPackage;
import org.dromara.system.domain.SysTenantPackageToSysTenantPackageVoMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysTenantPackageToSysTenantPackageVoMapper__7.class},
    imports = {}
)
public interface SysTenantPackageVoToSysTenantPackageMapper__7 extends BaseMapper<SysTenantPackageVo, SysTenantPackage> {
}
