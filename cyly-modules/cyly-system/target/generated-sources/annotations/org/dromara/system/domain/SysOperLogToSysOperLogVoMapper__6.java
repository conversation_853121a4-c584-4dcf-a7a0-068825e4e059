package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysOperLogBoToSysOperLogMapper__6;
import org.dromara.system.domain.vo.SysOperLogVo;
import org.dromara.system.domain.vo.SysOperLogVoToSysOperLogMapper__6;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysOperLogVoToSysOperLogMapper__6.class,SysOperLogBoToSysOperLogMapper__6.class},
    imports = {}
)
public interface SysOperLogToSysOperLogVoMapper__6 extends BaseMapper<SysOperLog, SysOperLogVo> {
}
