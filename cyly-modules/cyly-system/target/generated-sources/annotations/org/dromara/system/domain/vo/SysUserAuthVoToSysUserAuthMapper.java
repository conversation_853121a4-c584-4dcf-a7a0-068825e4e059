package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__394;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysUserAuth;
import org.dromara.system.domain.SysUserAuthToSysUserAuthVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__394.class,
    uses = {SysUserAuthToSysUserAuthVoMapper.class},
    imports = {}
)
public interface SysUserAuthVoToSysUserAuthMapper extends BaseMapper<SysUserAuthVo, SysUserAuth> {
}
