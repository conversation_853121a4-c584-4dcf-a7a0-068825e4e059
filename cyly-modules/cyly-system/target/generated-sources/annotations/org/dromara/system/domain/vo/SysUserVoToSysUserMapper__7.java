package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysRoleToSysRoleVoMapper__7;
import org.dromara.system.domain.SysUser;
import org.dromara.system.domain.SysUserToSysUserVoMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysRoleVoToSysRoleMapper__7.class,SysRoleToSysRoleVoMapper__7.class,SysUserToSysUserVoMapper__7.class},
    imports = {}
)
public interface SysUserVoToSysUserMapper__7 extends BaseMapper<SysUserVo, SysUser> {
}
