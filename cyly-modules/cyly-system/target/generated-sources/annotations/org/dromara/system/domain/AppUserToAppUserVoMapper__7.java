package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.AppUserBoToAppUserMapper__7;
import org.dromara.system.domain.vo.AppUserVo;
import org.dromara.system.domain.vo.AppUserVoToAppUserMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {AppUserBoToAppUserMapper__7.class,AppUserVoToAppUserMapper__7.class},
    imports = {}
)
public interface AppUserToAppUserVoMapper__7 extends BaseMapper<AppUser, AppUserVo> {
}
