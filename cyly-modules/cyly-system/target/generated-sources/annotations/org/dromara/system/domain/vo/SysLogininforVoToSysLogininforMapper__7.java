package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__364;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysLogininfor;
import org.dromara.system.domain.SysLogininforToSysLogininforVoMapper__7;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__364.class,
    uses = {SysLogininforToSysLogininforVoMapper__7.class},
    imports = {}
)
public interface SysLogininforVoToSysLogininforMapper__7 extends BaseMapper<SysLogininforVo, SysLogininfor> {
}
