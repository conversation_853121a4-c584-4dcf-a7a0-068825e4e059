package io.github.linpeilie;

import org.dromara.common.log.event.OperLogEventToAppOperLogBoMapper;
import org.dromara.common.log.event.OperLogEventToAppOperLogBoMapper__1;
import org.dromara.common.log.event.OperLogEventToSysOperLogBoMapper;
import org.dromara.common.log.event.OperLogEventToSysOperLogBoMapper__1;
import org.dromara.system.domain.AppLogininforToAppLogininforVoMapper;
import org.dromara.system.domain.AppLogininforToAppLogininforVoMapper__1;
import org.dromara.system.domain.AppOperLogToAppOperLogVoMapper;
import org.dromara.system.domain.AppOperLogToAppOperLogVoMapper__1;
import org.dromara.system.domain.AppUserDetailToAppUserDetailVoMapper;
import org.dromara.system.domain.AppUserDetailToAppUserDetailVoMapper__1;
import org.dromara.system.domain.AppUserPermissionToAppUserPermissionVoMapper;
import org.dromara.system.domain.AppUserPermissionToAppUserPermissionVoMapper__1;
import org.dromara.system.domain.AppUserToAppUserVoMapper;
import org.dromara.system.domain.AppUserToAppUserVoMapper__1;
import org.dromara.system.domain.DictAddressToDictAddressVoMapper;
import org.dromara.system.domain.DictAddressToDictAddressVoMapper__1;
import org.dromara.system.domain.DictAreaToDictAreaFullVoMapper;
import org.dromara.system.domain.DictAreaToDictAreaFullVoMapper__1;
import org.dromara.system.domain.DictAreaToDictAreaVoMapper;
import org.dromara.system.domain.DictAreaToDictAreaVoMapper__1;
import org.dromara.system.domain.SysClientToSysClientVoMapper;
import org.dromara.system.domain.SysClientToSysClientVoMapper__1;
import org.dromara.system.domain.SysConfigToSysConfigVoMapper;
import org.dromara.system.domain.SysConfigToSysConfigVoMapper__1;
import org.dromara.system.domain.SysDeptToSysDeptVoMapper;
import org.dromara.system.domain.SysDeptToSysDeptVoMapper__1;
import org.dromara.system.domain.SysDictDataToSysDictDataVoMapper;
import org.dromara.system.domain.SysDictDataToSysDictDataVoMapper__1;
import org.dromara.system.domain.SysDictTypeToSysDictTypeVoMapper;
import org.dromara.system.domain.SysDictTypeToSysDictTypeVoMapper__1;
import org.dromara.system.domain.SysIpBanToSysIpBanVoMapper;
import org.dromara.system.domain.SysIpBanToSysIpBanVoMapper__1;
import org.dromara.system.domain.SysLogininforToSysLogininforVoMapper;
import org.dromara.system.domain.SysLogininforToSysLogininforVoMapper__1;
import org.dromara.system.domain.SysMenuToSysMenuVoMapper;
import org.dromara.system.domain.SysMenuToSysMenuVoMapper__1;
import org.dromara.system.domain.SysNoticeToSysNoticeVoMapper;
import org.dromara.system.domain.SysNoticeToSysNoticeVoMapper__1;
import org.dromara.system.domain.SysOperLogToSysOperLogVoMapper;
import org.dromara.system.domain.SysOperLogToSysOperLogVoMapper__1;
import org.dromara.system.domain.SysPostToSysPostVoMapper;
import org.dromara.system.domain.SysPostToSysPostVoMapper__1;
import org.dromara.system.domain.SysRoleToSysRoleVoMapper;
import org.dromara.system.domain.SysRoleToSysRoleVoMapper__1;
import org.dromara.system.domain.SysSocialToSysSocialVoMapper;
import org.dromara.system.domain.SysSocialToSysSocialVoMapper__1;
import org.dromara.system.domain.SysTenantPackageToSysTenantPackageVoMapper;
import org.dromara.system.domain.SysTenantPackageToSysTenantPackageVoMapper__1;
import org.dromara.system.domain.SysTenantToSysTenantVoMapper;
import org.dromara.system.domain.SysTenantToSysTenantVoMapper__1;
import org.dromara.system.domain.SysUserAuthToSysUserAuthVoMapper;
import org.dromara.system.domain.SysUserAuthToSysUserAuthVoMapper__1;
import org.dromara.system.domain.SysUserDetailToSysUserDetailVoMapper;
import org.dromara.system.domain.SysUserDetailToSysUserDetailVoMapper__1;
import org.dromara.system.domain.SysUserToSysUserVoMapper;
import org.dromara.system.domain.SysUserToSysUserVoMapper__1;
import org.dromara.system.domain.SysUserTypeToSysUserTypeVoMapper;
import org.dromara.system.domain.SysUserTypeToSysUserTypeVoMapper__1;
import org.dromara.system.domain.SysVersionToSysVersionVoMapper;
import org.dromara.system.domain.SysVersionToSysVersionVoMapper__1;
import org.dromara.system.domain.bo.AppLogininforBoToAppLogininforMapper;
import org.dromara.system.domain.bo.AppLogininforBoToAppLogininforMapper__1;
import org.dromara.system.domain.bo.AppOperLogBoToAppOperLogMapper;
import org.dromara.system.domain.bo.AppOperLogBoToAppOperLogMapper__1;
import org.dromara.system.domain.bo.AppOperLogBoToOperLogEventMapper;
import org.dromara.system.domain.bo.AppOperLogBoToOperLogEventMapper__1;
import org.dromara.system.domain.bo.AppUserBoToAppUserMapper;
import org.dromara.system.domain.bo.AppUserBoToAppUserMapper__1;
import org.dromara.system.domain.bo.AppUserDetailBoToAppUserDetailMapper;
import org.dromara.system.domain.bo.AppUserDetailBoToAppUserDetailMapper__1;
import org.dromara.system.domain.bo.AppUserPermissionBoToAppUserPermissionMapper;
import org.dromara.system.domain.bo.AppUserPermissionBoToAppUserPermissionMapper__1;
import org.dromara.system.domain.bo.DictAddressBoToDictAddressMapper;
import org.dromara.system.domain.bo.DictAddressBoToDictAddressMapper__1;
import org.dromara.system.domain.bo.DictAreaBoToDictAreaMapper;
import org.dromara.system.domain.bo.DictAreaBoToDictAreaMapper__1;
import org.dromara.system.domain.bo.SysClientBoToSysClientMapper;
import org.dromara.system.domain.bo.SysClientBoToSysClientMapper__1;
import org.dromara.system.domain.bo.SysConfigBoToSysConfigMapper;
import org.dromara.system.domain.bo.SysConfigBoToSysConfigMapper__1;
import org.dromara.system.domain.bo.SysDeptBoToSysDeptMapper;
import org.dromara.system.domain.bo.SysDeptBoToSysDeptMapper__1;
import org.dromara.system.domain.bo.SysDictDataBoToSysDictDataMapper;
import org.dromara.system.domain.bo.SysDictDataBoToSysDictDataMapper__1;
import org.dromara.system.domain.bo.SysDictTypeBoToSysDictTypeMapper;
import org.dromara.system.domain.bo.SysDictTypeBoToSysDictTypeMapper__1;
import org.dromara.system.domain.bo.SysIpBanBoToSysIpBanMapper;
import org.dromara.system.domain.bo.SysIpBanBoToSysIpBanMapper__1;
import org.dromara.system.domain.bo.SysLogininforBoToSysLogininforMapper;
import org.dromara.system.domain.bo.SysLogininforBoToSysLogininforMapper__1;
import org.dromara.system.domain.bo.SysMenuBoToSysMenuMapper;
import org.dromara.system.domain.bo.SysMenuBoToSysMenuMapper__1;
import org.dromara.system.domain.bo.SysNoticeBoToSysNoticeMapper;
import org.dromara.system.domain.bo.SysNoticeBoToSysNoticeMapper__1;
import org.dromara.system.domain.bo.SysOperLogBoToOperLogEventMapper;
import org.dromara.system.domain.bo.SysOperLogBoToOperLogEventMapper__1;
import org.dromara.system.domain.bo.SysOperLogBoToSysOperLogMapper;
import org.dromara.system.domain.bo.SysOperLogBoToSysOperLogMapper__1;
import org.dromara.system.domain.bo.SysPostBoToSysPostMapper;
import org.dromara.system.domain.bo.SysPostBoToSysPostMapper__1;
import org.dromara.system.domain.bo.SysRoleBoToSysRoleMapper;
import org.dromara.system.domain.bo.SysRoleBoToSysRoleMapper__1;
import org.dromara.system.domain.bo.SysSocialBoToSysSocialMapper;
import org.dromara.system.domain.bo.SysSocialBoToSysSocialMapper__1;
import org.dromara.system.domain.bo.SysTenantBoToSysTenantMapper;
import org.dromara.system.domain.bo.SysTenantBoToSysTenantMapper__1;
import org.dromara.system.domain.bo.SysTenantPackageBoToSysTenantPackageMapper;
import org.dromara.system.domain.bo.SysTenantPackageBoToSysTenantPackageMapper__1;
import org.dromara.system.domain.bo.SysUserAuthBoToSysUserAuthMapper;
import org.dromara.system.domain.bo.SysUserAuthBoToSysUserAuthMapper__1;
import org.dromara.system.domain.bo.SysUserBoToSysUserMapper;
import org.dromara.system.domain.bo.SysUserBoToSysUserMapper__1;
import org.dromara.system.domain.bo.SysUserDetailBoToSysUserDetailMapper;
import org.dromara.system.domain.bo.SysUserDetailBoToSysUserDetailMapper__1;
import org.dromara.system.domain.bo.SysUserTypeBoToSysUserTypeMapper;
import org.dromara.system.domain.bo.SysUserTypeBoToSysUserTypeMapper__1;
import org.dromara.system.domain.bo.SysVersionBoToSysVersionMapper;
import org.dromara.system.domain.bo.SysVersionBoToSysVersionMapper__1;
import org.dromara.system.domain.convert.AppLogininforBoConvert;
import org.dromara.system.domain.convert.AppOperLogBoConvert;
import org.dromara.system.domain.convert.AppUserBoConvert;
import org.dromara.system.domain.convert.AppUserDetailBoConvert;
import org.dromara.system.domain.convert.AppUserDetailVoConvert;
import org.dromara.system.domain.convert.AppUserVoConvert;
import org.dromara.system.domain.convert.SysClientVoConvert;
import org.dromara.system.domain.convert.SysDeptVoConvert;
import org.dromara.system.domain.convert.SysDictDataVoConvert;
import org.dromara.system.domain.convert.SysLogininforBoConvert;
import org.dromara.system.domain.convert.SysOperLogBoConvert;
import org.dromara.system.domain.convert.SysSocialBoConvert;
import org.dromara.system.domain.convert.SysSocialVoConvert;
import org.dromara.system.domain.convert.SysTenantVoConvert;
import org.dromara.system.domain.convert.SysUserBoConvert;
import org.dromara.system.domain.convert.SysUserVoConvert;
import org.dromara.system.domain.vo.AppLogininforVoToAppLogininforMapper;
import org.dromara.system.domain.vo.AppLogininforVoToAppLogininforMapper__1;
import org.dromara.system.domain.vo.AppOperLogVoToAppOperLogMapper;
import org.dromara.system.domain.vo.AppOperLogVoToAppOperLogMapper__1;
import org.dromara.system.domain.vo.AppUserDetailVoToAppUserDetailMapper;
import org.dromara.system.domain.vo.AppUserDetailVoToAppUserDetailMapper__1;
import org.dromara.system.domain.vo.AppUserPermissionVoToAppUserPermissionMapper;
import org.dromara.system.domain.vo.AppUserPermissionVoToAppUserPermissionMapper__1;
import org.dromara.system.domain.vo.AppUserVoToAppUserMapper;
import org.dromara.system.domain.vo.AppUserVoToAppUserMapper__1;
import org.dromara.system.domain.vo.DictAddressVoToDictAddressMapper;
import org.dromara.system.domain.vo.DictAddressVoToDictAddressMapper__1;
import org.dromara.system.domain.vo.DictAreaFullVoToDictAreaMapper;
import org.dromara.system.domain.vo.DictAreaFullVoToDictAreaMapper__1;
import org.dromara.system.domain.vo.DictAreaVoToDictAreaMapper;
import org.dromara.system.domain.vo.DictAreaVoToDictAreaMapper__1;
import org.dromara.system.domain.vo.SysClientVoToSysClientMapper;
import org.dromara.system.domain.vo.SysClientVoToSysClientMapper__1;
import org.dromara.system.domain.vo.SysConfigVoToSysConfigMapper;
import org.dromara.system.domain.vo.SysConfigVoToSysConfigMapper__1;
import org.dromara.system.domain.vo.SysDeptVoToSysDeptMapper;
import org.dromara.system.domain.vo.SysDeptVoToSysDeptMapper__1;
import org.dromara.system.domain.vo.SysDictDataVoToSysDictDataMapper;
import org.dromara.system.domain.vo.SysDictDataVoToSysDictDataMapper__1;
import org.dromara.system.domain.vo.SysDictTypeVoToSysDictTypeMapper;
import org.dromara.system.domain.vo.SysDictTypeVoToSysDictTypeMapper__1;
import org.dromara.system.domain.vo.SysIpBanVoToSysIpBanMapper;
import org.dromara.system.domain.vo.SysIpBanVoToSysIpBanMapper__1;
import org.dromara.system.domain.vo.SysLogininforVoToSysLogininforMapper;
import org.dromara.system.domain.vo.SysLogininforVoToSysLogininforMapper__1;
import org.dromara.system.domain.vo.SysMenuVoToSysMenuMapper;
import org.dromara.system.domain.vo.SysMenuVoToSysMenuMapper__1;
import org.dromara.system.domain.vo.SysNoticeVoToSysNoticeMapper;
import org.dromara.system.domain.vo.SysNoticeVoToSysNoticeMapper__1;
import org.dromara.system.domain.vo.SysOperLogVoToSysOperLogMapper;
import org.dromara.system.domain.vo.SysOperLogVoToSysOperLogMapper__1;
import org.dromara.system.domain.vo.SysPostVoToSysPostMapper;
import org.dromara.system.domain.vo.SysPostVoToSysPostMapper__1;
import org.dromara.system.domain.vo.SysRoleVoToSysRoleMapper;
import org.dromara.system.domain.vo.SysRoleVoToSysRoleMapper__1;
import org.dromara.system.domain.vo.SysSocialVoToSysSocialMapper;
import org.dromara.system.domain.vo.SysSocialVoToSysSocialMapper__1;
import org.dromara.system.domain.vo.SysTenantPackageVoToSysTenantPackageMapper;
import org.dromara.system.domain.vo.SysTenantPackageVoToSysTenantPackageMapper__1;
import org.dromara.system.domain.vo.SysTenantVoToSysTenantMapper;
import org.dromara.system.domain.vo.SysTenantVoToSysTenantMapper__1;
import org.dromara.system.domain.vo.SysUserAuthVoToSysUserAuthMapper;
import org.dromara.system.domain.vo.SysUserAuthVoToSysUserAuthMapper__1;
import org.dromara.system.domain.vo.SysUserDetailVoToSysUserDetailMapper;
import org.dromara.system.domain.vo.SysUserDetailVoToSysUserDetailMapper__1;
import org.dromara.system.domain.vo.SysUserTypeVoToSysUserTypeMapper;
import org.dromara.system.domain.vo.SysUserTypeVoToSysUserTypeMapper__1;
import org.dromara.system.domain.vo.SysUserVoToSysUserMapper;
import org.dromara.system.domain.vo.SysUserVoToSysUserMapper__1;
import org.dromara.system.domain.vo.SysVersionVoToSysVersionMapper;
import org.dromara.system.domain.vo.SysVersionVoToSysVersionMapper__1;
import org.mapstruct.Builder;
import org.mapstruct.MapperConfig;
import org.mapstruct.ReportingPolicy;

@MapperConfig(
    componentModel = "spring-lazy",
    uses = {ConverterMapperAdapter__364.class, SysConfigVoToSysConfigMapper__1.class, SysDictDataVoToSysDictDataMapper.class, SysUserDetailBoToSysUserDetailMapper__1.class, AppOperLogBoToOperLogEventMapper__1.class, SysUserAuthVoToSysUserAuthMapper__1.class, DictAreaVoToDictAreaMapper__1.class, SysUserDetailVoToSysUserDetailMapper.class, SysLogininforVoToSysLogininforMapper.class, DictAddressToDictAddressVoMapper__1.class, SysTenantVoToSysTenantMapper__1.class, SysConfigBoToSysConfigMapper.class, SysTenantPackageToSysTenantPackageVoMapper.class, SysPostBoToSysPostMapper.class, SysVersionToSysVersionVoMapper.class, SysDictTypeToSysDictTypeVoMapper__1.class, SysUserTypeVoToSysUserTypeMapper.class, SysUserDetailToSysUserDetailVoMapper__1.class, SysClientVoConvert.class, AppUserDetailVoConvert.class, SysDeptBoToSysDeptMapper__1.class, SysDictDataToSysDictDataVoMapper.class, SysUserBoConvert.class, SysLogininforToSysLogininforVoMapper__1.class, SysConfigBoToSysConfigMapper__1.class, SysIpBanToSysIpBanVoMapper.class, DictAddressBoToDictAddressMapper__1.class, SysRoleVoToSysRoleMapper.class, SysUserDetailVoToSysUserDetailMapper__1.class, SysClientVoToSysClientMapper.class, AppUserPermissionBoToAppUserPermissionMapper.class, SysDeptBoToSysDeptMapper.class, AppOperLogBoConvert.class, SysMenuVoToSysMenuMapper.class, SysDeptToSysDeptVoMapper__1.class, SysDictDataVoToSysDictDataMapper__1.class, SysUserTypeBoToSysUserTypeMapper__1.class, AppUserDetailVoToAppUserDetailMapper__1.class, OperLogEventToAppOperLogBoMapper__1.class, AppLogininforToAppLogininforVoMapper__1.class, SysDictDataBoToSysDictDataMapper__1.class, AppLogininforVoToAppLogininforMapper.class, AppUserToAppUserVoMapper__1.class, SysClientVoToSysClientMapper__1.class, SysClientBoToSysClientMapper.class, SysIpBanToSysIpBanVoMapper__1.class, SysDeptVoToSysDeptMapper__1.class, SysClientToSysClientVoMapper.class, SysNoticeToSysNoticeVoMapper__1.class, SysIpBanBoToSysIpBanMapper.class, SysLogininforVoToSysLogininforMapper__1.class, SysMenuVoToSysMenuMapper__1.class, SysUserDetailBoToSysUserDetailMapper.class, SysPostVoToSysPostMapper__1.class, AppUserPermissionToAppUserPermissionVoMapper.class, SysOperLogBoConvert.class, SysUserDetailToSysUserDetailVoMapper.class, SysTenantToSysTenantVoMapper.class, AppUserDetailToAppUserDetailVoMapper__1.class, SysOperLogBoToOperLogEventMapper__1.class, SysConfigVoToSysConfigMapper.class, DictAreaToDictAreaFullVoMapper.class, SysUserAuthToSysUserAuthVoMapper__1.class, AppUserDetailBoToAppUserDetailMapper.class, SysSocialBoToSysSocialMapper.class, AppOperLogBoToAppOperLogMapper__1.class, DictAreaFullVoToDictAreaMapper.class, SysSocialToSysSocialVoMapper__1.class, AppUserDetailBoToAppUserDetailMapper__1.class, SysDictTypeToSysDictTypeVoMapper.class, AppUserPermissionToAppUserPermissionVoMapper__1.class, SysUserTypeToSysUserTypeVoMapper__1.class, SysTenantPackageVoToSysTenantPackageMapper__1.class, SysPostBoToSysPostMapper__1.class, SysLogininforBoToSysLogininforMapper__1.class, AppUserPermissionBoToAppUserPermissionMapper__1.class, AppLogininforBoConvert.class, AppUserDetailBoConvert.class, SysUserVoConvert.class, AppOperLogBoToOperLogEventMapper.class, DictAreaFullVoToDictAreaMapper__1.class, SysIpBanVoToSysIpBanMapper.class, SysLogininforToSysLogininforVoMapper.class, SysPostVoToSysPostMapper.class, SysSocialToSysSocialVoMapper.class, SysDictTypeBoToSysDictTypeMapper.class, DictAreaToDictAreaVoMapper.class, OperLogEventToSysOperLogBoMapper.class, AppLogininforBoToAppLogininforMapper__1.class, SysUserAuthVoToSysUserAuthMapper.class, SysNoticeBoToSysNoticeMapper.class, DictAreaToDictAreaFullVoMapper__1.class, AppUserDetailVoToAppUserDetailMapper.class, SysVersionBoToSysVersionMapper__1.class, SysOperLogBoToSysOperLogMapper__1.class, SysSocialVoToSysSocialMapper.class, DictAddressBoToDictAddressMapper.class, SysClientToSysClientVoMapper__1.class, SysUserAuthBoToSysUserAuthMapper.class, DictAreaBoToDictAreaMapper.class, AppOperLogVoToAppOperLogMapper__1.class, SysDictTypeBoToSysDictTypeMapper__1.class, SysNoticeBoToSysNoticeMapper__1.class, SysRoleBoToSysRoleMapper__1.class, DictAreaToDictAreaVoMapper__1.class, SysUserToSysUserVoMapper.class, SysDictTypeVoToSysDictTypeMapper.class, SysDeptVoConvert.class, SysSocialVoConvert.class, SysUserToSysUserVoMapper__1.class, SysMenuBoToSysMenuMapper.class, SysRoleToSysRoleVoMapper__1.class, SysSocialVoToSysSocialMapper__1.class, AppOperLogBoToAppOperLogMapper.class, AppOperLogVoToAppOperLogMapper.class, SysOperLogToSysOperLogVoMapper__1.class, SysSocialBoToSysSocialMapper__1.class, SysConfigToSysConfigVoMapper.class, SysOperLogBoToOperLogEventMapper.class, SysUserTypeVoToSysUserTypeMapper__1.class, SysNoticeVoToSysNoticeMapper.class, SysTenantToSysTenantVoMapper__1.class, SysMenuBoToSysMenuMapper__1.class, AppUserVoConvert.class, DictAreaVoToDictAreaMapper.class, AppLogininforVoToAppLogininforMapper__1.class, OperLogEventToAppOperLogBoMapper.class, SysMenuToSysMenuVoMapper.class, SysVersionToSysVersionVoMapper__1.class, SysVersionVoToSysVersionMapper__1.class, SysTenantVoToSysTenantMapper.class, SysUserTypeBoToSysUserTypeMapper.class, SysUserBoToSysUserMapper.class, SysUserAuthToSysUserAuthVoMapper.class, DictAddressVoToDictAddressMapper__1.class, AppUserBoConvert.class, SysUserAuthBoToSysUserAuthMapper__1.class, AppUserToAppUserVoMapper.class, AppUserBoToAppUserMapper.class, SysLogininforBoConvert.class, OperLogEventToSysOperLogBoMapper__1.class, SysDictDataBoToSysDictDataMapper.class, SysUserVoToSysUserMapper.class, SysNoticeToSysNoticeVoMapper.class, SysDictDataToSysDictDataVoMapper__1.class, SysTenantVoConvert.class, SysDictDataVoConvert.class, SysVersionVoToSysVersionMapper.class, SysOperLogToSysOperLogVoMapper.class, SysIpBanVoToSysIpBanMapper__1.class, SysLogininforBoToSysLogininforMapper.class, SysVersionBoToSysVersionMapper.class, AppUserPermissionVoToAppUserPermissionMapper__1.class, AppLogininforToAppLogininforVoMapper.class, DictAreaBoToDictAreaMapper__1.class, SysDeptVoToSysDeptMapper.class, SysRoleVoToSysRoleMapper__1.class, SysTenantPackageBoToSysTenantPackageMapper__1.class, AppLogininforBoToAppLogininforMapper.class, AppUserPermissionVoToAppUserPermissionMapper.class, SysRoleBoToSysRoleMapper.class, DictAddressToDictAddressVoMapper.class, SysConfigToSysConfigVoMapper__1.class, SysDictTypeVoToSysDictTypeMapper__1.class, SysUserTypeToSysUserTypeVoMapper.class, SysTenantBoToSysTenantMapper__1.class, SysRoleToSysRoleVoMapper.class, SysUserVoToSysUserMapper__1.class, SysMenuToSysMenuVoMapper__1.class, SysNoticeVoToSysNoticeMapper__1.class, SysTenantPackageVoToSysTenantPackageMapper.class, AppUserBoToAppUserMapper__1.class, SysSocialBoConvert.class, SysTenantBoToSysTenantMapper.class, AppOperLogToAppOperLogVoMapper.class, SysTenantPackageBoToSysTenantPackageMapper.class, SysTenantPackageToSysTenantPackageVoMapper__1.class, SysOperLogVoToSysOperLogMapper__1.class, AppUserDetailToAppUserDetailVoMapper.class, SysClientBoToSysClientMapper__1.class, SysIpBanBoToSysIpBanMapper__1.class, AppUserVoToAppUserMapper.class, SysOperLogBoToSysOperLogMapper.class, DictAddressVoToDictAddressMapper.class, SysOperLogVoToSysOperLogMapper.class, SysDeptToSysDeptVoMapper.class, SysPostToSysPostVoMapper__1.class, AppOperLogToAppOperLogVoMapper__1.class, AppUserVoToAppUserMapper__1.class, SysUserBoToSysUserMapper__1.class, SysPostToSysPostVoMapper.class},
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    builder = @Builder(buildMethod = "build", disableBuilder = true)
)
public interface AutoMapperConfig__364 {
}
