<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.dromara</groupId>
    <artifactId>cyly-modules</artifactId>
    <version>2.3.0</version>
  </parent>
  <groupId>org.dromara</groupId>
  <artifactId>cyly-gen</artifactId>
  <version>2.3.0</version>
  <description>cyly-gen代码生成</description>
  <dependencies>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>cyly-common-nacos</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.velocity</groupId>
      <artifactId>velocity-engine-core</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>cyly-common-log</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>cyly-common-doc</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>cyly-common-web</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>cyly-common-mybatis</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>cyly-common-dubbo</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>cyly-common-tenant</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>cyly-common-security</artifactId>
    </dependency>
    <dependency>
      <groupId>org.anyline</groupId>
      <artifactId>anyline-environment-spring-data-jdbc</artifactId>
      <version>${anyline.version}</version>
    </dependency>
    <dependency>
      <groupId>org.anyline</groupId>
      <artifactId>anyline-data-jdbc-mysql</artifactId>
      <version>${anyline.version}</version>
    </dependency>
  </dependencies>
  <build>
    <finalName>${project.artifactId}</finalName>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>${spring-boot.version}</version>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
