version: 2
updates:
  # 配置Maven依赖检查
  - package-ecosystem: "maven"
    directory: "/"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 10
    # 指定审核人员（可选）
    # assignees:
    #   - "your-github-username"
    # 标签（可选）
    labels:
      - "dependencies"
      - "security"
    # 忽略某些依赖更新（可选）
    # ignore:
    #   - dependency-name: "org.springframework.boot"
    #     versions: ["3.0.x"]
    
  # 配置Docker依赖检查
  - package-ecosystem: "docker"
    directory: "/"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 5
    labels:
      - "docker"
      - "dependencies"
      
  # 配置GitHub Actions依赖检查（如果您使用GitHub Actions）
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 5
    labels:
      - "github-actions"
      - "dependencies"
