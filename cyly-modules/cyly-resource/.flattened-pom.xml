<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.dromara</groupId>
    <artifactId>cyly-modules</artifactId>
    <version>2.3.0</version>
  </parent>
  <groupId>org.dromara</groupId>
  <artifactId>cyly-resource</artifactId>
  <version>2.3.0</version>
  <description>cyly-resource资源服务</description>
  <dependencies>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>cyly-common-nacos</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>cyly-common-sentinel</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>cyly-common-doc</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>cyly-common-dubbo</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>cyly-common-seata</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>cyly-common-web</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>cyly-common-log</artifactId>
      <exclusions>
        <exclusion>
          <groupId>com.google.errorprone</groupId>
          <artifactId>error_prone_annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.checkerframework</groupId>
          <artifactId>checker-qual</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>cyly-common-oss</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>cyly-common-ratelimiter</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>cyly-common-mail</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>cyly-common-sms</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>cyly-common-mybatis</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>cyly-common-tenant</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>cyly-common-security</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>cyly-common-translation</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>cyly-common-websocket</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>cyly-common-sse</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>cyly-common-core</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>cyly-common-base</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>cyly-common-encrypt</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>cyly-api-system</artifactId>
      <exclusions>
        <exclusion>
          <groupId>commons-io</groupId>
          <artifactId>commons-io</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>cyly-api-resource</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>cyly-common-idempotent</artifactId>
    </dependency>
    <dependency>
      <groupId>org.dromara</groupId>
      <artifactId>cyly-api-xchat</artifactId>
    </dependency>
  </dependencies>
  <build>
    <finalName>${project.artifactId}</finalName>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>${spring-boot.version}</version>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
