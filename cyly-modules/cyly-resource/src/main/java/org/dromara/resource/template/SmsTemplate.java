package org.dromara.resource.template;

import lombok.Data;

/**
 * <AUTHOR> 22:39
 */
@Data
public class SmsTemplate {
    /**
     * 目标手机号，非中国大陆手机号码需要填写国家代码(如美国：+1-xxxxxxxxxx)或地区代码(如香港：+852-xxxxxxxx)
     */
    private String mobile;
    /**
     * 模板编号(如不指定则使用配置的默认模版)
     */
    private Integer templateid;
    /**
     * 验证码长度，默认为 4 位，取值范围为 4-10 （注：语音验证码的取值范围为 4-8位）。
     */
    private Integer codeLen;
    /**
     * 客户自定义验证码，长度为 4 ～ 10 位，支持字母和数字。
     * 如果设置了该参数，则codeLen参数无效
     */
    private String authCode;
    /**
     * 是否需要支持短信上行。true:需要，false:不需要
     * 说明：如果开通了短信上行抄送功能，该参数需要设置为true，其它情况设置无效
     */
    private Boolean needUp;

}
