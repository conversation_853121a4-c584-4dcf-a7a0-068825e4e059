package org.dromara.auth.controller;

import org.dromara.common.core.constant.Constants;
import org.dromara.common.core.constant.GlobalConstants;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.domain.model.KeyPairModel;
import org.dromara.common.encrypt.utils.EncryptUtils;
import org.dromara.common.redis.utils.RedisUtils;
import cn.hutool.core.util.IdUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 描述:
 * 安全相关服务
 *
 * <AUTHOR>
 * @since 2025-2-4 08:46:33
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/security")
public class SecurityController {

  @GetMapping("/sm2key")
  public R<Map<String,String>> getSm2Key() {
    String uuid = IdUtil.simpleUUID();
    // 生成一次性SM2密钥对并保存至redis
    String sm2Key = GlobalConstants.SM2_CODE_KEY + uuid;
    Map<String, String> sm2KeyMap = EncryptUtils.generateSm2KeyHex();
    KeyPairModel keyPair = new KeyPairModel();
    keyPair.setPrivateKey(sm2KeyMap.get(EncryptUtils.PRIVATE_KEY));
    keyPair.setPublicKey(sm2KeyMap.get(EncryptUtils.PUBLIC_KEY));
    RedisUtils.setCacheObject(sm2Key, keyPair, Duration.ofMinutes(Constants.SM2_KEY_EXPIRATION));
    Map<String, String> ajax = new HashMap<>();
    ajax.put("uuid", uuid);
    ajax.put("publicKey", keyPair.getPublicKey());
    return R.ok(ajax);
  }
}
