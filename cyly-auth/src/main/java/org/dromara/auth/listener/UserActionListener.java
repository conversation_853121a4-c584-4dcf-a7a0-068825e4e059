package org.dromara.auth.listener;

import cn.dev33.satoken.config.SaTokenConfig;
import cn.dev33.satoken.listener.SaTokenListener;
import cn.dev33.satoken.stp.SaLoginModel;
import cn.hutool.http.useragent.UserAgent;
import cn.hutool.http.useragent.UserAgentUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.dromara.common.core.constant.CacheConstants;
import org.dromara.common.core.constant.Constants;
import org.dromara.common.core.utils.MessageUtils;
import org.dromara.common.core.utils.ServletUtils;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.core.utils.ip.AddressUtils;
import org.dromara.common.log.event.AppLogininforEvent;
import org.dromara.common.log.event.LogininforEvent;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.satoken.utils.AdminLoginHelper;
import org.dromara.common.satoken.utils.AppLoginHelper;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.system.api.RemoteAppUserService;
import org.dromara.system.api.RemoteUserService;
import org.dromara.system.api.domain.AppUserOnline;
import org.dromara.system.api.domain.SysUserOnline;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.time.Duration;

/**
 * 用户行为侦听器的实现
 *
 * <AUTHOR>
 * @date 2025年3月5日 10:17:26
 */
@RequiredArgsConstructor
@Component
@Slf4j
public class UserActionListener implements SaTokenListener {

    private final SaTokenConfig tokenConfig;
    @DubboReference
    private RemoteUserService remoteUserService;
    @DubboReference
    private RemoteAppUserService remoteAppUserService;

    @Override
    public void doLogin(String loginType, Object loginId, String tokenValue, SaLoginModel loginModel) {
        // 获取通用信息
        UserAgent userAgent = UserAgentUtil.parse(ServletUtils.getRequest().getHeader("User-Agent"));
        String ip = ServletUtils.getClientIP();

        // 根据登录类型处理不同的登录逻辑
        if ("user".equals(loginType)) {
            // 用户端登录
            handleAppUserLogin(loginId, tokenValue, loginModel, userAgent, ip);
        } else {
            // 管理端登录
            handleSysUserLogin(loginId, tokenValue, loginModel, userAgent, ip);
        }
    }

    /**
     * 处理App用户登录
     */
    private void handleAppUserLogin(Object loginId, String tokenValue, SaLoginModel loginModel,
                                    UserAgent userAgent, String ip) {
        // 获取登录信息
        String username = (String) loginModel.getExtra(AppLoginHelper.USER_NAME_KEY);
        String tenantId = (String) loginModel.getExtra(AppLoginHelper.TENANT_KEY);
        Long userId = (Long) loginModel.getExtra(AppLoginHelper.USER_KEY);

        // 构建在线用户对象
        AppUserOnline userOnline = new AppUserOnline();
        buildCommonOnlineInfo(userOnline, ip, userAgent, tokenValue);
        userOnline.setUserName(username);
        userOnline.setClientKey("app");
        userOnline.setDeviceType(loginModel.getDevice());

        // 缓存在线用户
        cacheOnlineUser(CacheConstants.APP_ONLINE_TOKEN_KEY + tokenValue, userOnline, tenantId);

        // 记录登录日志
        recordAppLoginLog(username, tenantId, ip);
        loginModel.setToken(tokenValue);
        // 更新登录信息
        remoteAppUserService.recordLoginInfo(userId, ip);
        log.info("app user doLogin, useId:{}, token:{}", loginId, tokenValue);
    }

    /**
     * 处理系统用户登录
     */
    private void handleSysUserLogin(Object loginId, String tokenValue, SaLoginModel loginModel,
                                    UserAgent userAgent, String ip) {
        // 获取登录信息
        String username = (String) loginModel.getExtra(AdminLoginHelper.USER_NAME_KEY);
        String tenantId = (String) loginModel.getExtra(AdminLoginHelper.TENANT_KEY);
        Long userId = (Long) loginModel.getExtra(AdminLoginHelper.USER_KEY);

        // 构建在线用户对象
        SysUserOnline userOnline = new SysUserOnline();
        buildCommonOnlineInfo(userOnline, ip, userAgent, tokenValue);
        userOnline.setUserName(username);
        userOnline.setClientKey((String) loginModel.getExtra(AdminLoginHelper.CLIENT_KEY));
        userOnline.setDeviceType(loginModel.getDevice());
        userOnline.setDeptName((String) loginModel.getExtra(AdminLoginHelper.DEPT_NAME_KEY));

        // 缓存在线用户
        cacheOnlineUser(CacheConstants.ONLINE_TOKEN_KEY + tokenValue, userOnline, tenantId);

        // 记录登录日志
        recordSysLoginLog(username, tenantId, ip);

        // 更新登录信息
        remoteUserService.recordLoginInfo(userId, ip);
        log.info("sys user doLogin, useId:{}, token:{}", loginId, tokenValue);
    }

    /**
     * 构建通用在线用户信息
     */
    private <T> void buildCommonOnlineInfo(T userOnline, String ip, UserAgent userAgent, String tokenValue) {
        try {
            Method setIpaddr = userOnline.getClass().getMethod("setIpaddr", String.class);
            Method setLoginLocation = userOnline.getClass().getMethod("setLoginLocation", String.class);
            Method setBrowser = userOnline.getClass().getMethod("setBrowser", String.class);
            Method setOs = userOnline.getClass().getMethod("setOs", String.class);
            Method setLoginTime = userOnline.getClass().getMethod("setLoginTime", Long.class);
            Method setTokenId = userOnline.getClass().getMethod("setTokenId", String.class);

            setIpaddr.invoke(userOnline, ip);
            setLoginLocation.invoke(userOnline, AddressUtils.getRealAddressByIP(ip));
            setBrowser.invoke(userOnline, userAgent.getBrowser().getName());
            setOs.invoke(userOnline, userAgent.getOs().getName());
            setLoginTime.invoke(userOnline, System.currentTimeMillis());
            setTokenId.invoke(userOnline, tokenValue);
        } catch (Exception e) {
            log.error("构建在线用户信息失败", e);
        }
    }

    /**
     * 缓存在线用户信息
     */
    private <T> void cacheOnlineUser(String cacheKey, T userOnline, String tenantId) {
        TenantHelper.dynamic(tenantId, () -> {
            if (tokenConfig.getTimeout() == -1) {
                RedisUtils.setCacheObject(cacheKey, userOnline);
            } else {
                RedisUtils.setCacheObject(cacheKey, userOnline, Duration.ofSeconds(tokenConfig.getTimeout()));
            }
        });
    }

    /**
     * 记录App用户登录日志
     */
    private void recordAppLoginLog(String username, String tenantId, String ip) {
        AppLogininforEvent logininforEvent = new AppLogininforEvent();
        logininforEvent.setTenantId(tenantId);
        logininforEvent.setUsername(username);
        logininforEvent.setStatus(Constants.LOGIN_SUCCESS);
        logininforEvent.setMessage(MessageUtils.message("user.login.success"));
        logininforEvent.setRequest(ServletUtils.getRequest());
        SpringUtils.context().publishEvent(logininforEvent);
    }

    /**
     * 记录系统用户登录日志
     */
    private void recordSysLoginLog(String username, String tenantId, String ip) {
        LogininforEvent logininforEvent = new LogininforEvent();
        logininforEvent.setTenantId(tenantId);
        logininforEvent.setUsername(username);
        logininforEvent.setStatus(Constants.LOGIN_SUCCESS);
        logininforEvent.setMessage(MessageUtils.message("user.login.success"));
        logininforEvent.setRequest(ServletUtils.getRequest());
        SpringUtils.context().publishEvent(logininforEvent);
    }

    /**
     * 每次注销时触发
     */
    @Override
    public void doLogout(String loginType, Object loginId, String tokenValue) {
            if ("user".equals(loginType)) {
                RedisUtils.deleteObject(CacheConstants.APP_ONLINE_TOKEN_KEY + tokenValue);
                log.info("app user doLogout, useId:{}, token:{}", loginId, tokenValue);
            } else {
                RedisUtils.deleteObject(CacheConstants.ONLINE_TOKEN_KEY + tokenValue);
                log.info("user doLogout, useId:{}, token:{}", loginId, tokenValue);
            }
    }

    /**
     * 每次被踢下线时触发
     */
    @Override
    public void doKickout(String loginType, Object loginId, String tokenValue) {
            if ("user".equals(loginType)) {
                RedisUtils.deleteObject(CacheConstants.APP_ONLINE_TOKEN_KEY + tokenValue);
                log.info("app user doKickout, useId:{}, token:{}", loginId, tokenValue);
            } else {
                RedisUtils.deleteObject(CacheConstants.ONLINE_TOKEN_KEY + tokenValue);
                log.info("user doKickout, useId:{}, token:{}", loginId, tokenValue);
            }
    }

    /**
     * 每次被顶下线时触发
     */
    @Override
    public void doReplaced(String loginType, Object loginId, String tokenValue) {
        if ("user".equals(loginType)) {
            RedisUtils.deleteObject(CacheConstants.APP_ONLINE_TOKEN_KEY + tokenValue);
            log.info("app user doReplaced, useId:{}, token:{}", loginId, tokenValue);
        } else {
            RedisUtils.deleteObject(CacheConstants.ONLINE_TOKEN_KEY + tokenValue);
            log.info("user doReplaced, useId:{}, token:{}", loginId, tokenValue);
        }

    }

    /**
     * 每次被封禁时触发
     */
    @Override
    public void doDisable (String loginType, Object loginId, String service,int level, long disableTime){
    }

    /**
     * 每次被解封时触发
     */
    @Override
    public void doUntieDisable (String loginType, Object loginId, String service){
    }

    /**
     * 每次打开二级认证时触发
     */
    @Override
    public void doOpenSafe (String loginType, String tokenValue, String service,long safeTime){
    }

    /**
     * 每次创建Session时触发
     */
    @Override
    public void doCloseSafe (String loginType, String tokenValue, String service){
    }

    /**
     * 每次创建Session时触发
     */
    @Override
    public void doCreateSession (String id){
    }

    /**
     * 每次注销Session时触发
     */
    @Override
    public void doLogoutSession (String id){
    }

    /**
     * 每次Token续期时触发
     */
    @Override
    public void doRenewTimeout (String tokenValue, Object loginId,long timeout){
    }

}
