# 老年人能力评估模块数据库设计分析文档

## 1. 项目概述

### 1.1 模块简介

老年人能力评估模块是康养云平台的核心功能模块，严格遵循**GB/T 42195-2022《老年人能力评估规范》**国家标准，为老年人提供科学、规范、标准化的能力评估服务。

### 1.2 技术栈

- **后端框架**：Spring Boot 3.x + Spring Cloud Alibaba
- **数据库**：MySQL 8.0+
- **ORM框架**：MyBatis-Plus
- **权限管理**：Sa-Token
- **开发规范**：JDK 21 + 世界级Java代码规范

### 1.3 国标合规性

本模块完全遵循GB/T 42195-2022标准，支持：
- **4个一级指标**：自理能力、基础运动能力、精神状态、感知觉与社会参与
- **26个二级指标**：涵盖进食、穿脱衣物、平地行走、记忆、理解能力等
- **5级评分标准**：能力完好、轻度受损、中度受损、重度受损、完全丧失

## 2. 数据库表结构设计

### 2.1 核心表概览

本模块共设计8张核心表，严格遵循GB/T 42195-2022国家标准：

| 表名 | 中文名称 | 主要功能 |
|------|----------|----------|
| `aea_gb_standard_questionnaire` | 国标评估问卷表 | 管理国标问卷模板 |
| `aea_gb_standard_question` | 国标评估问题表 | 存储26个评估指标 |
| `aea_gb_standard_option` | 国标评估选项表 | 管理评分选项 |
| `aea_assessment_task` | 评估任务表 | 评估流程管理 |
| `aea_assessment_record` | 评估记录表 | 存储评估答案 |
| `aea_assessment_result` | 评估结果表 | 计算评估得分 |
| `aea_assessment_report` | 评估报告表 | 生成PDF报告 |
| `aea_report_template` | 报告模板表 | 报告模板管理 |

### 2.2 国标评估表设计分析

#### 2.2.1 GB/T 42195-2022标准结构

根据GB/T 42195-2022《老年人能力评估规范》，附录B包含以下评估表：

- **B.1 老年人能力评估表**：自理能力评估（8个二级指标）
- **B.2 基础运动能力评估表**：基础运动能力评估（4个二级指标）
- **B.3 精神状态评估表**：精神状态评估（9个二级指标）
- **B.4 感知觉与社会参与评估表**：感知觉与社会参与评估（5个二级指标）
- **B.5 老年人能力总得分**：汇总计算表

#### 2.2.2 单表vs多表设计论证

**技术可行性分析：**
- 从技术角度，可以将B.1-B.4四张表合并为一张通用评估记录表
- 合并后的表结构可包含：评估任务ID、长者ID、指标类型、指标编码、指标名称、评估得分等字段

**多表设计优势：**
1. **标准合规性**：严格遵循GB/T 42195-2022标准的逻辑分类
2. **业务清晰性**：每个一级指标有不同的评分规则和计算方式
3. **维护便利性**：符合医护人员的评估习惯，便于理解和维护
4. **报告生成**：便于生成分类报告和统计分析
5. **扩展性强**：支持未来标准更新和功能扩展

**设计决策：**
采用当前的三表结构（问卷表+问题表+选项表）管理国标问卷模板，配合评估记录表存储具体评估数据。这种设计既保持了灵活性，又符合国标规范要求。

### 2.3 详细表结构分析

#### 2.3.1 aea_gb_standard_questionnaire（国标评估问卷表）

```sql
CREATE TABLE `aea_gb_standard_questionnaire` (
  `questionnaire_id` bigint NOT NULL AUTO_INCREMENT COMMENT '问卷ID',
  `questionnaire_name` varchar(200) NOT NULL COMMENT '问卷名称',
  `questionnaire_code` varchar(50) NOT NULL COMMENT '问卷编码',
  `questionnaire_type` varchar(50) NOT NULL COMMENT '问卷类型',
  `gb_standard_version` varchar(20) NOT NULL DEFAULT 'GB/T 42195-2022' COMMENT '国标版本',
  `description` text COMMENT '问卷描述',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`questionnaire_id`),
  UNIQUE KEY `uk_questionnaire_code` (`questionnaire_code`)
) COMMENT='国标评估问卷表';
```

**核心字段说明：**
- `gb_standard_version`：确保国标版本可追溯
- `questionnaire_type`：支持不同类型的评估问卷
- `is_active`：支持问卷版本管理

#### 2.3.2 aea_gb_standard_question（国标评估问题表）

```sql
CREATE TABLE `aea_gb_standard_question` (
  `question_id` bigint NOT NULL AUTO_INCREMENT COMMENT '问题ID',
  `questionnaire_id` bigint NOT NULL COMMENT '问卷ID',
  `question_code` varchar(50) NOT NULL COMMENT '问题编码',
  `question_text` text NOT NULL COMMENT '问题内容',
  `question_type` varchar(20) NOT NULL DEFAULT 'single_choice' COMMENT '问题类型',
  `category_level1` varchar(50) NOT NULL COMMENT '一级指标',
  `category_level2` varchar(50) NOT NULL COMMENT '二级指标',
  `max_score` int NOT NULL DEFAULT '0' COMMENT '最高分值',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序',
  `is_required` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否必答',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`question_id`),
  UNIQUE KEY `uk_question_code` (`question_code`)
) COMMENT='国标评估问题表';
```

**核心字段说明：**
- `category_level1/level2`：对应国标的一级、二级指标分类
- `max_score`：支持不同问题的分值设置
- `question_type`：支持单选、多选、评分等题型

#### 2.3.3 aea_gb_standard_option（国标评估选项表）

```sql
CREATE TABLE `aea_gb_standard_option` (
  `option_id` bigint NOT NULL AUTO_INCREMENT COMMENT '选项ID',
  `question_id` bigint NOT NULL COMMENT '问题ID',
  `option_code` varchar(50) NOT NULL COMMENT '选项编码',
  `option_text` varchar(500) NOT NULL COMMENT '选项文本',
  `option_score` int NOT NULL DEFAULT '0' COMMENT '选项分值',
  `sort_order` int NOT NULL DEFAULT '0' COMMENT '排序',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`option_id`),
  UNIQUE KEY `uk_option_code` (`option_code`)
) COMMENT='国标评估选项表';
```

#### 2.3.4 aea_assessment_task（评估任务表）

```sql
CREATE TABLE `aea_assessment_task` (
  `task_id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `task_code` varchar(50) NOT NULL COMMENT '任务编码',
  `elder_id` bigint NOT NULL COMMENT '长者ID',
  `questionnaire_id` bigint NOT NULL COMMENT '问卷ID',
  `assessor_id` bigint NOT NULL COMMENT '评估员ID',
  `assessor_name` varchar(100) NOT NULL COMMENT '评估员姓名',
  `assessment_type` varchar(20) NOT NULL DEFAULT 'initial' COMMENT '评估类型',
  `task_status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '任务状态',
  `scheduled_time` datetime COMMENT '计划评估时间',
  `start_time` datetime COMMENT '开始时间',
  `end_time` datetime COMMENT '结束时间',
  `location` varchar(200) COMMENT '评估地点',
  `remarks` text COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`task_id`),
  UNIQUE KEY `uk_task_code` (`task_code`)
) COMMENT='评估任务表';
```

#### 2.3.5 aea_assessment_record（评估记录表）

```sql
CREATE TABLE `aea_assessment_record` (
  `record_id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `task_id` bigint NOT NULL COMMENT '任务ID',
  `question_id` bigint NOT NULL COMMENT '问题ID',
  `option_id` bigint COMMENT '选项ID',
  `answer_text` text COMMENT '答案文本',
  `answer_score` int NOT NULL DEFAULT '0' COMMENT '答案得分',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`record_id`),
  UNIQUE KEY `uk_task_question` (`task_id`, `question_id`)
) COMMENT='评估记录表';
```

#### 2.3.6 aea_assessment_result（评估结果表）

```sql
CREATE TABLE `aea_assessment_result` (
  `result_id` bigint NOT NULL AUTO_INCREMENT COMMENT '结果ID',
  `task_id` bigint NOT NULL COMMENT '任务ID',
  `elder_id` bigint NOT NULL COMMENT '长者ID',
  `total_score` int NOT NULL DEFAULT '0' COMMENT '总得分',
  `self_care_score` int NOT NULL DEFAULT '0' COMMENT '自理能力得分',
  `basic_mobility_score` int NOT NULL DEFAULT '0' COMMENT '基础运动能力得分',
  `mental_state_score` int NOT NULL DEFAULT '0' COMMENT '精神状态得分',
  `perception_social_score` int NOT NULL DEFAULT '0' COMMENT '感知觉与社会参与得分',
  `ability_level` varchar(20) NOT NULL COMMENT '能力等级',
  `ability_level_desc` varchar(100) NOT NULL COMMENT '能力等级描述',
  `assessment_conclusion` text COMMENT '评估结论',
  `recommendations` text COMMENT '建议',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`result_id`),
  UNIQUE KEY `uk_task_result` (`task_id`)
) COMMENT='评估结果表';
```

#### 2.3.7 aea_assessment_report（评估报告表）

```sql
CREATE TABLE `aea_assessment_report` (
  `report_id` bigint NOT NULL AUTO_INCREMENT COMMENT '报告ID',
  `task_id` bigint NOT NULL COMMENT '任务ID',
  `elder_id` bigint NOT NULL COMMENT '长者ID',
  `template_id` bigint NOT NULL COMMENT '模板ID',
  `report_title` varchar(200) NOT NULL COMMENT '报告标题',
  `report_content` longtext NOT NULL COMMENT '报告内容',
  `pdf_file_path` varchar(500) COMMENT 'PDF文件路径',
  `pdf_file_size` bigint COMMENT 'PDF文件大小',
  `report_status` varchar(20) NOT NULL DEFAULT 'draft' COMMENT '报告状态',
  `generated_time` datetime COMMENT '生成时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`report_id`),
  UNIQUE KEY `uk_task_report` (`task_id`)
) COMMENT='评估报告表';
```

#### 2.3.8 aea_report_template（报告模板表）

```sql
CREATE TABLE `aea_report_template` (
  `template_id` bigint NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `template_name` varchar(200) NOT NULL COMMENT '模板名称',
  `template_code` varchar(50) NOT NULL COMMENT '模板编码',
  `template_type` varchar(20) NOT NULL DEFAULT 'standard' COMMENT '模板类型',
  `template_content` longtext NOT NULL COMMENT '模板内容',
  `template_format` varchar(20) NOT NULL DEFAULT 'html' COMMENT '模板格式',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认模板',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`template_id`),
  UNIQUE KEY `uk_template_code` (`template_code`)
) COMMENT='报告模板表';
```

## 3. 技术设计决策

### 3.1 外键约束策略

**设计决策：不使用数据库外键约束**

**原因分析：**
1. **性能考虑**：外键约束会影响大批量数据操作性能
2. **扩展性**：便于后期微服务拆分和数据库分库分表
3. **维护性**：减少数据库层面的复杂依赖关系
4. **灵活性**：支持更灵活的数据迁移和备份策略

**数据完整性保障方案：**
1. **应用层约束**：通过业务逻辑确保数据关联的正确性
2. **数据库索引**：建立合适的索引提升查询性能
3. **事务管理**：使用Spring事务确保数据一致性
4. **定期校验**：建立数据完整性检查机制
5. **监控告警**：对数据异常进行实时监控

### 3.2 字段设计原则

#### 3.2.1 命名规范
- **表名**：`aea_` + 业务含义，使用下划线分隔
- **字段名**：业务含义明确，避免缩写
- **主键**：统一使用 `表名_id` 格式
- **时间字段**：统一使用 `create_time`、`update_time`

#### 3.2.2 数据类型选择
- **主键ID**：`bigint` 类型，支持大数据量
- **状态字段**：`varchar(20)` 便于扩展
- **分数字段**：`int` 类型，避免浮点精度问题
- **文本内容**：根据长度选择 `varchar`、`text`、`longtext`
- **时间字段**：`datetime` 类型，精确到秒

#### 3.2.3 默认值设置
- **状态字段**：设置合理的默认状态
- **分数字段**：默认为0
- **布尔字段**：明确默认值（0或1）
- **时间字段**：自动设置创建和更新时间

### 3.3 索引设计策略

#### 3.3.1 主键索引
所有表都有自增主键，自动创建聚簇索引。

#### 3.3.2 唯一索引
```sql
-- 问卷编码唯一索引
CREATE UNIQUE INDEX uk_questionnaire_code ON aea_gb_standard_questionnaire(questionnaire_code);

-- 问题编码唯一索引
CREATE UNIQUE INDEX uk_question_code ON aea_gb_standard_question(question_code);

-- 任务编码唯一索引
CREATE UNIQUE INDEX uk_task_code ON aea_assessment_task(task_code);
```

#### 3.3.3 业务索引
```sql
-- 评估任务查询索引
CREATE INDEX idx_task_elder_status ON aea_assessment_task(elder_id, task_status, create_time);

-- 评估记录查询索引
CREATE INDEX idx_record_task_question ON aea_assessment_record(task_id, question_id);

-- 评估结果查询索引
CREATE INDEX idx_result_elder_time ON aea_assessment_result(elder_id, create_time);

-- 问题分类索引
CREATE INDEX idx_question_category ON aea_gb_standard_question(category_level1, category_level2);
```

## 4. 业务流程设计

### 4.1 评估流程

```mermaid
sequenceDiagram
    participant A as 评估员
    participant S as 系统
    participant D as 数据库
    
    A->>S: 创建评估任务
    S->>D: 插入任务记录
    A->>S: 开始评估
    S->>D: 更新任务状态
    A->>S: 逐题评估
    S->>D: 保存评估记录
    A->>S: 完成评估
    S->>D: 计算评估结果
    S->>D: 生成评估报告
    S->>A: 返回评估结果
```

### 4.2 数据流转

1. **任务创建**：在 `aea_assessment_task` 表创建评估任务
2. **问卷加载**：从 `aea_gb_standard_questionnaire` 等表加载国标问卷
3. **答案记录**：在 `aea_assessment_record` 表保存每题答案
4. **结果计算**：根据国标算法计算各维度得分
5. **结果存储**：在 `aea_assessment_result` 表保存计算结果
6. **报告生成**：基于模板生成PDF报告并存储

## 5. 性能优化建议

### 5.1 查询优化

#### 5.1.1 分页查询
```sql
-- 使用覆盖索引优化分页
SELECT t.* FROM aea_assessment_task t 
INNER JOIN (
    SELECT task_id FROM aea_assessment_task 
    WHERE elder_id = ? AND task_status = ?
    ORDER BY create_time DESC 
    LIMIT ?, ?
) tmp ON t.task_id = tmp.task_id;
```

#### 5.1.2 统计查询
```sql
-- 使用索引优化统计查询
SELECT 
    ability_level,
    COUNT(*) as count
FROM aea_assessment_result 
WHERE create_time >= ? AND create_time <= ?
GROUP BY ability_level;
```

### 5.2 缓存策略

#### 5.2.1 Redis缓存
- **问卷模板**：缓存国标问卷结构，减少数据库查询
- **评估结果**：缓存最新评估结果，提升查询性能
- **统计数据**：缓存统计报表数据，定时更新

#### 5.2.2 本地缓存
- **字典数据**：缓存评估等级、状态等字典数据
- **配置信息**：缓存系统配置和模板信息

### 5.3 数据归档

#### 5.3.1 历史数据处理
- **定期归档**：将历史评估数据迁移到归档表
- **数据压缩**：对归档数据进行压缩存储
- **查询优化**：提供历史数据查询接口

## 6. 安全性设计

### 6.1 数据安全

#### 6.1.1 敏感信息保护
- **数据脱敏**：对个人敏感信息进行脱敏处理
- **访问控制**：基于角色的数据访问权限控制
- **审计日志**：记录所有数据操作日志

#### 6.1.2 SQL注入防护
- **参数化查询**：使用MyBatis-Plus的参数化查询
- **输入验证**：对所有输入参数进行严格验证
- **权限校验**：每个接口都进行权限校验

### 6.2 业务安全

#### 6.2.1 数据完整性
- **业务校验**：在业务层进行数据完整性校验
- **事务控制**：确保关键业务操作的原子性
- **异常处理**：完善的异常处理和回滚机制

## 7. 监控与维护

### 7.1 性能监控

#### 7.1.1 数据库监控
- **慢查询监控**：监控执行时间超过阈值的SQL
- **连接池监控**：监控数据库连接池状态
- **表空间监控**：监控表空间使用情况

#### 7.1.2 应用监控
- **接口性能**：监控评估相关接口的响应时间
- **业务指标**：监控评估任务完成率、错误率等
- **资源使用**：监控CPU、内存、磁盘使用情况

### 7.2 数据维护

#### 7.2.1 定期维护
- **数据备份**：定期备份评估数据
- **索引优化**：定期分析和优化索引
- **数据清理**：清理无效和过期数据

#### 7.2.2 故障恢复
- **备份策略**：制定完善的数据备份策略
- **恢复流程**：建立数据恢复标准流程
- **容灾方案**：制定数据库容灾方案

## 8. 扩展性考虑

### 8.1 标准更新支持

#### 8.1.1 版本管理
- **问卷版本**：支持国标问卷的版本管理
- **向下兼容**：确保新版本对历史数据的兼容性
- **平滑升级**：支持标准更新时的平滑升级

#### 8.1.2 配置化设计
- **评分规则**：支持评分规则的配置化
- **报告模板**：支持报告模板的灵活配置
- **业务流程**：支持评估流程的配置化

### 8.2 功能扩展

#### 8.2.1 多标准支持
- **标准扩展**：支持其他评估标准的接入
- **自定义问卷**：支持机构自定义评估问卷
- **多语言支持**：支持多语言评估问卷

#### 8.2.2 集成能力
- **API接口**：提供标准化的API接口
- **数据导入导出**：支持评估数据的导入导出
- **第三方集成**：支持与第三方系统的集成

## 9. 总结

### 9.1 设计亮点

1. **标准合规**：严格遵循GB/T 42195-2022国家标准
2. **架构清晰**：采用分层设计，职责明确
3. **扩展性强**：支持标准更新和功能扩展
4. **性能优化**：合理的索引设计和缓存策略
5. **安全可靠**：完善的安全防护和监控机制

### 9.2 技术优势

1. **无外键设计**：提升性能和扩展性
2. **标准化命名**：提高代码可读性和维护性
3. **合理索引**：优化查询性能
4. **事务控制**：确保数据一致性
5. **监控完善**：全方位的性能和业务监控

### 9.3 业务价值

1. **规范评估**：提供标准化的老年人能力评估服务
2. **科学决策**：为养老服务提供科学依据
3. **提升效率**：自动化评估流程，提升工作效率
4. **数据积累**：积累评估数据，支持统计分析
5. **持续改进**：支持评估标准的持续更新和改进

---

**文档版本**：v1.0  
**创建时间**：2024年12月  
**更新时间**：2024年12月  
**维护人员**：康养云技术团队